import * as z from 'zod';

export const PropertyOptionCreateSchema = z.object({
  propertyId: z.string().min(1, { message: 'Property is required' }),
  name: z
    .string()
    .min(1, { message: 'Name is required' })
    .optional()
    .nullable(),
  description: z.string().optional().nullable(),
  displayOrder: z.string().optional().nullable(),
});

export type PropertyOptionCreateData = z.infer<
  typeof PropertyOptionCreateSchema
>;

export const PropertyOptionUpdateSchema = PropertyOptionCreateSchema.extend({
  code: z.string().optional().nullable(),
});

export type PropertyOptionUpdateData = z.infer<
  typeof PropertyOptionUpdateSchema
>;

export const PropertyDisplaySchema = z.object({
  id: z.number().nullable().optional(),
  options: z.array(z.number()).optional(),
});

export const OfferingPropertySchema = z.object({
  property: z.array(
    z.object({
      options: z.array(z.string()).nullable().optional(),
      id: z.number().min(1, 'Invalid ID'),
    }),
  ),
});

export type OfferingPropertyFormData = z.infer<typeof OfferingPropertySchema>;

export const AddOnSchema = z.object({
  addon: z.array(
    z.object({
      id: z.number().min(1, 'Invalid ID'),
      options: z.array(z.number()).nullable().optional(), // changed from string[] to number[]
    }),
  ),
});

export type AddOnData = z.infer<typeof AddOnSchema>;
