import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import CompanyQuery from '@/services/queries/CompanyQuery';
import CompanyTable from './components/company_table';

export const metadata: Metadata = {
  title: 'Goodkey | Company',
};

export default async function Cluster() {
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery({
    queryKey: [...CompanyQuery.tags, 'Show manager'],
    queryFn: () => CompanyQuery.getAll('Show manager'),
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'Company-Contact', link: '/dashboard/setup/company-contact' },
        {
          title: 'Show Companies',
          link: '/dashboard/setup/company-contact/show-company',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <CompanyTable />
      </HydrationBoundary>
    </AppLayout>
  );
}
