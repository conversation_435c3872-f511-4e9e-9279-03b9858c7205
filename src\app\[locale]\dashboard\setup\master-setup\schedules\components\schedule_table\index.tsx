'use client';

import { useQuery } from '@tanstack/react-query';
import Link from 'next/link';

import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import { Schedule } from '@/models/Schedule';
import ScheduleQuery from '@/services/queries/ScheduleQuery';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';
import SwitchStatusModal from './switch_status_modal';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';
import { getQueryClient } from '@/utils/query-client';

export default function ScheduleTable() {
  const { data, isPending, isLoading } = useQuery({
    queryKey: ScheduleQuery.tags,
    queryFn: () => ScheduleQuery.getAll(),
  });

  const columns = generateTableColumns<Schedule>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      code: { name: 'Code', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      description: {
        name: 'Description',
        type: 'text',
        sortable: true,
      },
      isActive: {
        name: 'Status',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              <div
                className={cn(
                  'w-2 h-2 rounded-full mr-1 flex-shrink-0',
                  cell ? 'bg-green-500' : 'bg-red-500',
                )}
              />
              <span
                className={cn(
                  'text-xs font-medium px-2 py-0.5 rounded-full inline-block whitespace-nowrap',
                  cell
                    ? 'text-green-700 bg-green-50 border border-green-200'
                    : 'text-red-700 bg-red-50 border border-red-200',
                )}
              >
                {cell ? 'Active' : 'Inactive'}
              </span>
            </div>
          ),
        },
      },
      createdAt: {
        name: 'Created At',
        type: 'text',
        sortable: true,
      },
    },
    {
      'toggle-action': {
        name: 'Status',
        type: {
          type: 'node',
          render: ({ row }) => (
            <Switch
              checked={row.isActive}
              onCheckedChange={(isActive) => {
                modal(
                  ({ close }) => (
                    <SwitchStatusModal
                      close={close}
                      isChecked={isActive}
                      scheduleId={row.id}
                    />
                  ),
                  { ...DEFAULT_MODAL, closable: false },
                ).open();
              }}
            />
          ),
        },
      },
      'edit-action': {
        name: 'Edit',
        type: {
          type: 'node',
          render: ({ row }) => (
            <Link href={`/dashboard/setup/master-setup/schedules/${row.id}`}>
              <Button
                size="sm"
                variant="secondary"
                iconName="EditIcon"
              ></Button>
            </Link>
          ),
        },
      },
      'delete-action': {
        name: 'Delete',
        type: {
          type: 'node',
          render: ({ row }) => (
            <Button
              variant="remove"
              size="sm"
              iconName="RemoveIcon"
              onClick={() => {
                modal(
                  ({ close }) => (
                    <MutationConfirmModal
                      close={close}
                      title="Delete Schedule"
                      description={`Are you sure you want to delete "${row.name}"? This action cannot be undone.`}
                      mutateFn={() => ScheduleQuery.delete(row.id)}
                      mutationKey={[...ScheduleQuery.tags]}
                      onSuccess={async () => {
                        await getQueryClient().invalidateQueries({
                          queryKey: [...ScheduleQuery.tags],
                        });
                      }}
                      variant="destructive"
                      confirmButtonText="Delete"
                      confirmIconName="DeleteIcon"
                      loadingIconName="LoadingIcon"
                    />
                  ),
                  DEFAULT_MODAL,
                ).open();
              }}
            ></Button>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<Schedule>({
    name: {
      name: 'Name',
      type: 'text',
    },
    code: {
      name: 'Code',
      type: 'text',
    },
    description: {
      name: 'Description',
      type: 'text',
    },
    isActive: {
      name: 'Status',
      type: {
        type: 'select',
        options: [
          { label: 'Active', value: 'true' },
          { label: 'Inactive', value: 'false' },
        ],
      },
    },
  });

  return (
    <>
      <DataTable
        filterFields={filters}
        columns={columns}
        data={data}
        isLoading={isLoading || isPending}
        controls={
          <div className="flex flex-row gap-2 justify-end">
            <Link href="/dashboard/setup/master-setup/schedules/add">
              <Button variant="main" iconName="AddIcon">
                Add Schedule
              </Button>
            </Link>
          </div>
        }
      />
    </>
  );
}
