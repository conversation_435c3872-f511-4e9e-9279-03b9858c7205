export interface ShowInList {
  id: number;
  archive: boolean;
  name: string;
  code: string;
  startDate: string;
  endDate: string;
  displayDate: string;
  orderDeadlineDate: string;
  lateChargePercentage: string;
  link: string;
  description: string;
  display: boolean;
  createdAt: string;
  createdBy: number;
  locationId: number;
  kioskPrintingQueueDate: string;
  view: boolean;
  createdByUsername: string;
  locationName: string;
  provinceId: number;
}

export interface ShowCreateRequest {
  name: string;
  code: string;
  startDate: string;
  endDate: string;
  displayDate: string;
  orderDeadlineDate: string;
  lateChargePercentage: string;
  link?: string;
  description?: string;
  display: boolean;
  locationId: number;
  kioskPrintingQueueDate?: string;
  view: boolean;
  provinceId: number;
}

export interface ShowUpdateRequest {
  name: string;
  code: string;
  startDate: string;
  endDate: string;
  displayDate: string;
  orderDeadlineDate: string;
  lateChargePercentage: string;
  link?: string;
  description?: string;
  display: boolean;
  locationId: number;
  kioskPrintingQueueDate?: string;
  view: boolean;
  provinceId: number;
}

export interface ShowScheduleDay {
  id?: string;
  date: string;
  startTime: string;
  endTime: string;
  notes?: string;
  type: 'moveIn' | 'show' | 'moveOut';
}

export interface ShowSchedule {
  id: number;
  showScheduleDate: string; // YYYY-MM-DD
  timeStart: string; // HH:MM:SS
  timeEnd: string; // HH:MM:SS
  showId: number;
  showScheduleConfirmed: boolean;
  showScheduleComments?: string;
  createdAt: string;
  createdBy: number;
  applyScheduleToServiceForm: boolean;
  createdByUsername: string;
  showName: string;
  showCode: string;
}

export interface ShowScheduleCreateRequest {
  showScheduleDate: string; // YYYY-MM-DD
  timeStart: string; // HH:MM:SS
  timeEnd: string; // HH:MM:SS
  showScheduleConfirmed?: boolean;
  showScheduleComments?: string;
  applyScheduleToServiceForm?: boolean;
}

export interface ShowScheduleUpdateRequest {
  showScheduleDate: string; // YYYY-MM-DD
  timeStart: string; // HH:MM:SS
  timeEnd: string; // HH:MM:SS
  showScheduleConfirmed?: boolean;
  showScheduleComments?: string;
  applyScheduleToServiceForm?: boolean;
}
