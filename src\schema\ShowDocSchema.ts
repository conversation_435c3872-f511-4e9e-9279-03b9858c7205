import * as z from 'zod';

// Common schema for both upload and update
export const ShowDocBaseSchema = z.object({
  docTypeId: z.number().optional().nullable(),
  locationId: z.number().optional().nullable(),
  hallId: z.number().optional().nullable(),
  showId: z.number().optional().nullable(),
  file: z.any().optional().nullable(), // Accepts File or undefined/null
  note: z.string().optional().nullable(),
  validUntil: z.coerce.date().optional().nullable(), // Accepts string and converts to Date
});

// Schema for upload
export const ShowDocUploadSchema = ShowDocBaseSchema;

// Schema for update (includes `id` and optional `filePath`)
export const ShowDocUpdateSchema = ShowDocBaseSchema.extend({
  id: z.number({
    required_error: 'Document ID is required',
    invalid_type_error: 'Document ID must be a number',
  }),
  filePath: z.string().optional().nullable(),
});

// TypeScript types
export type ShowDocUpload = z.infer<typeof ShowDocUploadSchema>;
export type ShowDocUpdate = z.infer<typeof ShowDocUpdateSchema>;
