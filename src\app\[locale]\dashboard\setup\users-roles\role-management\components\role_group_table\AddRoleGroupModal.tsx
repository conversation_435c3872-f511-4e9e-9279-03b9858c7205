'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { X, Save, Plus } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import RoleGroupQuery from '@/services/queries/RoleGroupQuery';
import { useToast } from '@/components/ui/use-toast';

const roleGroupSchema = z
  .object({
    name: z.string().min(1, 'Name is required'),
    minLevel: z.coerce.number().min(1, 'Minimum level must be at least 1'),
    maxLevel: z.coerce.number().min(1, 'Maximum level must be at least 1'),
  })
  .refine((data) => data.maxLevel >= data.minLevel, {
    message: 'Maximum level must be greater than or equal to minimum level',
    path: ['maxLevel'],
  });

type RoleGroupFormData = z.infer<typeof roleGroupSchema>;

interface AddRoleGroupModalProps {
  open: boolean;
  onClose: () => void;
}

export default function AddRoleGroupModal({
  open,
  onClose,
}: AddRoleGroupModalProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<RoleGroupFormData>({
    resolver: zodResolver(roleGroupSchema),
    defaultValues: {
      name: '',
      minLevel: 1,
      maxLevel: 1,
    },
  });

  const { handleSubmit, reset, control } = form;

  const createRoleGroupMutation = useMutation({
    mutationFn: RoleGroupQuery.create,
    onSuccess: (newGroupId) => {
      toast({
        title: 'Success',
        description: 'Role group created successfully',
      });
      queryClient.invalidateQueries({ queryKey: RoleGroupQuery.tags });
      return newGroupId;
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create role group',
        variant: 'destructive',
      });
    },
  });

  const handleClose = () => {
    reset();
    onClose();
  };

  const onSubmit = async (data: RoleGroupFormData) => {
    try {
      await createRoleGroupMutation.mutateAsync(data);
      handleClose();
    } catch {
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add New Role Group
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">
                Basic Information
              </h3>

              <div className="space-y-4">
                <Field
                  control={control as any}
                  name="name"
                  label="Group Name"
                  type="text"
                  required
                  placeholder="Enter group name"
                  containerClassName="max-w-md"
                />

                <div className="grid grid-cols-2 gap-4">
                  <Field
                    control={control as any}
                    name="minLevel"
                    label="Minimum Level"
                    type="number"
                    required
                    placeholder="Enter minimum level"
                    containerClassName="max-w-md"
                  />

                  <Field
                    control={control as any}
                    name="maxLevel"
                    label="Maximum Level"
                    type="number"
                    required
                    placeholder="Enter maximum level"
                    containerClassName="max-w-md"
                  />
                </div>

                <p className="text-xs text-gray-500">
                  Define the level range for this role group. Roles within this
                  range can be assigned to this group.
                </p>
              </div>
            </div>

            <div className="flex justify-end gap-3 pt-4 border-t">
              <Button type="button" variant="outline" onClick={handleClose}>
                <X className="h-4 w-4 mr-1" />
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createRoleGroupMutation.isPending}
                variant={'main'}
              >
                <Save className="h-4 w-4 mr-1" />
                {createRoleGroupMutation.isPending
                  ? 'Creating...'
                  : 'Create Role Group'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
