'use client';
import { useParams, usePathname, useRouter } from 'next/navigation';
import React from 'react';

import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/components/ui/app_layout';

interface IAddRoleSection {
  children?: React.ReactNode;
  isCreate?: boolean;
}

export const roleTabs: {
  label: string;
  value: string;
}[] = [
  {
    label: 'General Information',
    value: '',
  },
  {
    label: 'Menu Access Control',
    value: 'menu',
  },
  {
    label: 'Permission',
    value: 'permission',
  },
];

function AddRoleSection({ children, isCreate }: IAddRoleSection) {
  const params = useParams();
  const router = useRouter();
  const pathname = usePathname();

  // Generate breadcrumb items based on current state
  const getBreadcrumbItems = () => {
    const isAdd = !params?.id || params?.id === 'add';
    const currentTab = roleTabs.find(
      (tab) => tab.value === pathname?.split('/').pop(),
    );

    const items = [
      { title: 'Setup', link: '/dashboard/setup' },
      {
        title: 'Role Management',
        link: '/dashboard/setup/users-roles/role-management',
      },
      {
        title: isAdd ? 'Add Role' : 'Edit Role',
        link: `/dashboard/setup/users-roles/role-management/${params?.id}`,
      },
    ];

    // Add current tab to breadcrumb if not on the main tab
    if (currentTab && currentTab.value !== '') {
      items.push({
        title: currentTab.label,
        link: `/dashboard/setup/users-roles/role-management/${params?.id}/${currentTab.value}`,
      });
    }

    return items;
  };

  return (
    <AppLayout items={getBreadcrumbItems()}>
      <div className="flex flex-col w-full">
        <Tabs
          className="w-full self-start"
          value={
            roleTabs.find((tab) => tab.value === pathname?.split('/').pop())
              ?.value || ''
          }
        >
          <TabsList className="">
            {roleTabs.map(({ label, value }, index) => (
              <TabsTrigger
                onClick={() => {
                  router.push(
                    `/dashboard/setup/users-roles/role-management/${params?.id}/${value}`,
                  );
                }}
                value={value}
                key={value}
                disabled={isCreate && index != 0}
              >
                {label}
              </TabsTrigger>
            ))}
          </TabsList>
          <div className="flex flex-col pt-6">{children}</div>
        </Tabs>
      </div>
    </AppLayout>
  );
}

export default AddRoleSection;
