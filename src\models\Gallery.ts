export interface GalleryCategory {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  displayOrder: number;
}

export interface GallerySubcategory {
  id: number;
  categoryId: number;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  categoryName: string;
  displayOrder: number;
}

export interface GalleryImage {
  id: number;
  subcategoryId: number;
  name: string;
  alt: string;
  url: string;
  createdAt: string;
  subcategoryName: string;
  categoryName: string;
  displayOrder: number;
}
