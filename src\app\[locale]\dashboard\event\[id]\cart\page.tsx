import type { Metadata } from 'next';
import CartClient from './CartClient';

export const metadata: Metadata = {
  title: 'Shopping Cart | GOODKEY SHOW SERVICES LTD.',
  description:
    'View and manage your shopping cart items for your event at GOODKEY SHOW SERVICES LTD.',
};

export default async function CartPage(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;
  return <CartClient params={params} />;
}
