'use client';
import { ReactNode } from 'react';
import { useLocale } from 'next-intl';
import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3';

export default function RecaptchaProvider({
  children,
}: {
  children: ReactNode;
}) {
  const locale = useLocale();

  return (
    <GoogleReCaptchaProvider
      scriptProps={{
        async: false,
        defer: false,
        appendTo: 'head',
      }}
      language={locale}
      reCaptchaKey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY!}
    >
      {children}
    </GoogleReCaptchaProvider>
  );
}
