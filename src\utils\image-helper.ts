import { Area } from 'react-easy-crop';

/**
 * Loads an image from a URL and returns it as an HTMLImageElement.
 * @param url - The URL of the image to load.
 */
export const createImage = (src: string | File): Promise<HTMLImageElement> =>
  new Promise((resolve, reject) => {
    const image = new Image();
    image.addEventListener('load', () => {
      if (src instanceof File) {
        URL.revokeObjectURL(image.src); // Release the object URL
      }
      resolve(image);
    });
    image.addEventListener('error', (error) => reject(error));

    image.setAttribute('crossOrigin', 'anonymous'); // Avoid cross-origin issues
    image.src = src instanceof File ? URL.createObjectURL(src) : src;
  });

/**
 * Given a File and an optional Area (from react-easy-crop), returns a new File
 * that is a cropped version of the original image, or the original image if no
 * crop is specified.
 *
 * @param file - The file to crop.
 * @param pixelCrop - The cropped area in pixels, specified as an object with
 *   `x`, `y`, `width`, and `height` properties. If not specified, the entire
 *   image is used.
 *
 * @returns A promise that resolves to a new File that is a cropped version of
 *   the original image, or null if the canvas is empty.
 */
export async function getImageFile(
  file: File,
  pixelCrop?: Area,
): Promise<File | null> {
  const image = await createImage(file);

  // Set up canvas
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) return null;

  // Set canvas size to the full image size, or to the cropped area if specified
  canvas.width = pixelCrop ? pixelCrop.width : image.width;
  canvas.height = pixelCrop ? pixelCrop.height : image.height;

  // Draw the image on the canvas, cropping if needed
  if (pixelCrop) {
    ctx.drawImage(
      image,
      pixelCrop.x,
      pixelCrop.y,
      pixelCrop.width,
      pixelCrop.height,
      0,
      0,
      pixelCrop.width,
      pixelCrop.height,
    );
  } else {
    ctx.drawImage(image, 0, 0);
  }

  // Convert canvas to a blob, then to a File
  return new Promise<File | null>((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (!blob) {
          reject(new Error('Canvas is empty'));
          return;
        }
        // Convert the Blob to a File
        resolve(new File([blob], file.name, { type: 'image/png' }));
      },
      'image/png',
      0.8,
    );
  });
}
export function getAspectRatio(file: File): Promise<number> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const objectURL = URL.createObjectURL(file);

    img.onload = () => {
      const width = img.width;
      const height = img.height;
      URL.revokeObjectURL(objectURL);
      resolve(width / height);
    };

    img.onerror = () => {
      URL.revokeObjectURL(objectURL); // Clean up even if there's an error
      reject(new Error('Failed to load the image.'));
    };

    img.src = objectURL;
  });
}
