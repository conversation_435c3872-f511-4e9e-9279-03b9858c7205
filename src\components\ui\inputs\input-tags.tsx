'use client';

import * as React from 'react';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ClearIcon } from '@/assets/Icons';

import { type InputProps } from './input';
import { Badge } from '../Badge';

type InputTagsProps = Omit<InputProps, 'value' | 'onChange'> & {
  value: string;
  onChange: React.Dispatch<React.SetStateAction<string>>;
};

const InputTags = React.forwardRef<HTMLInputElement, InputTagsProps>(
  ({ className, value = '', onChange, ...props }, ref) => {
    const [pendingDataPoint, setPendingDataPoint] = React.useState('');

    const tagsArray = React.useMemo(
      () =>
        (value ?? '')
          .split(',')
          .map((tag) => tag.trim())
          .filter((tag) => tag !== ''),
      [value],
    );

    React.useEffect(() => {
      if (pendingDataPoint.includes(',')) {
        const newTags = new Set([
          ...tagsArray,
          ...pendingDataPoint.split(',').map((chunk) => chunk.trim()),
        ]);
        onChange(Array.from(newTags).join(','));
        setPendingDataPoint('');
      }
    }, [pendingDataPoint, onChange, tagsArray]);

    const addPendingDataPoint = () => {
      if (pendingDataPoint) {
        const newTags = new Set([...tagsArray, pendingDataPoint.trim()]);
        onChange(Array.from(newTags).join(','));
        setPendingDataPoint('');
      }
    };

    return (
      <div
        className={cn(
          'border border-gray-300 rounded-sm',
          // caveat: :has() variant requires tailwind v3.4 or above: https://tailwindcss.com/blog/tailwindcss-v3-4#new-has-variant
          'has-[:focus-visible]:outline-none has-[:focus-visible]:border-[#1a7efb] min-h-10 flex w-full flex-wrap gap-2 px-3 py-2 text-sm ring-offset-white disabled:cursor-not-allowed disabled:opacity-50',
          className,
        )}
      >
        {tagsArray.map((item) => (
          <Badge key={item} variant="secondary">
            {item}
            <Button
              variant="ghost"
              size="icon"
              className="ml-2 h-3 w-3"
              onClick={() => {
                onChange(tagsArray.filter((i) => i !== item).join(','));
              }}
            >
              <ClearIcon className="w-3" />
            </Button>
          </Badge>
        ))}
        <input
          className="flex-1 outline-none placeholder:text-neutral-500 dark:placeholder:text-neutral-400"
          value={pendingDataPoint}
          onChange={(e) => setPendingDataPoint(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ',') {
              e.preventDefault();
              addPendingDataPoint();
            } else if (
              e.key === 'Backspace' &&
              pendingDataPoint.length === 0 &&
              tagsArray.length > 0
            ) {
              e.preventDefault();
              onChange(tagsArray.slice(0, -1).join(','));
            }
          }}
          onBlur={addPendingDataPoint}
          {...props}
          ref={ref}
        />
      </div>
    );
  },
);

InputTags.displayName = 'InputTags';

export { InputTags };
