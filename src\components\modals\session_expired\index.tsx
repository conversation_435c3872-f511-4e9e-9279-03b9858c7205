import Link from 'next/link';
import { useTranslations } from 'next-intl';

import { Button } from '@/components/ui/button';
import ModalContainer from '@/components/ui/overlay/components/modal_container';

export default function SessionExpired({ close }: { close: () => void }) {
  const c = useTranslations('Common');
  return (
    <ModalContainer
      className="gap-0"
      title={c('sessionExpiredTitle')}
      description={c('sessionExpiredDesc')}
      controls={
        <Link href="/login" onClick={close}>
          <Button className="">{c('login')}</Button>
        </Link>
      }
    />
  );
}
