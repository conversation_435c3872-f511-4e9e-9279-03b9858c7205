import { useMutation } from '@tanstack/react-query';

import { Button, IconName } from '@/components/ui/button';

import ModalContainer from '@/components/ui/overlay/components/modal_container';

interface IMutationConfirmModal<T> {
  close: () => void;
  mutateFn: () => Promise<T>;
  mutationKey: readonly any[];
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
  title: string;
  description: string;
  variant?: 'default' | 'destructive' | 'success';
  confirmButtonText?: string;
  cancelButtonText?: string;
  confirmIconName?: IconName;
  cancelIconName?: IconName;
  loadingIconName?: IconName;
  loading?: boolean;
}
export default function MutationConfirmModal<T>({
  close,
  description,
  title,
  onSuccess,
  mutateFn,
  onError,
  mutationKey,
  variant = 'default',
  confirmButtonText,
  cancelButtonText = 'Cancel',
  confirmIconName,
  cancelIconName = 'ClearIcon',
  loadingIconName = 'LoadingIcon',
  loading: externalLoading,
}: IMutationConfirmModal<T>) {
  const { mutate, isPending } = useMutation({
    mutationKey: mutationKey,
    mutationFn: mutateFn,
    onSuccess: async (data) => {
      onSuccess?.(data);
      close();
    },
    onError: (error) => {
      onError?.(error);
    },
  });

  const finalLoadingState = externalLoading || isPending;
  const finalConfirmButtonText =
    confirmButtonText || (variant === 'destructive' ? 'Delete' : 'Confirm');

  return (
    <ModalContainer title={title} onClose={close}>
      <div className="py-2 text-slate-700">{description}</div>
      <div className="flex justify-end gap-3 pt-4">
        <Button
          variant="outline"
          disabled={finalLoadingState}
          iconName={cancelIconName}
          onClick={close}
        >
          {cancelButtonText}
        </Button>
        <Button
          onClick={() => mutate()}
          disabled={finalLoadingState}
          variant={variant === 'destructive' ? 'destructive' : 'default'}
          iconName={
            finalLoadingState
              ? loadingIconName
              : confirmIconName ||
                (variant === 'destructive' ? 'DeleteIcon' : 'SaveIcon')
          }
          iconProps={{
            className: 'text-white',
          }}
        >
          {finalLoadingState ? 'Please wait...' : finalConfirmButtonText}
        </Button>
      </div>
    </ModalContainer>
  );
}
