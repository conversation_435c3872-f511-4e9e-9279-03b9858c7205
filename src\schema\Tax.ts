import * as z from 'zod';
import { NumberString } from './common';

export const TaxRequestSchema = z.object({
  provinceId: z.string().min(1, { message: 'Province is required' }),
  taxTypeId: z.string().min(1, { message: 'Tax Type is required' }),
  displayOrder: z.nullable(NumberString),
  taxRate: NumberString,
});

export type TaxRequestData = z.infer<typeof TaxRequestSchema>;

export const TaxUpdateSchema = z.object({
  displayOrder: z
    .number()
    .int()
    .min(0, { message: 'Display order must be zero or greater' }),
  taxRate: z.number().min(0, { message: 'Tax rate must be zero or greater' }),
});

export type TaxUpdateData = z.infer<typeof TaxUpdateSchema>;
