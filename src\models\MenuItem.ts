import { BriefData } from './BriefData';
import { PermissionKey } from './Permission';
import { UserData } from './User';
export interface MenuItemBrief extends BriefData {
  parent?: MenuItemBrief;
  icon?: string;
  isVisible?: boolean;
  permission?: PermissionKey;
  displayOrder: number;
  url?: string;
  target?: string;
  isParent?: boolean;
  isDashboard?: boolean;
  menus?: string[];
  section?: string;
  level?: number;
  children?: MenuItemBrief[];
}
export interface MenuItem extends MenuItemBrief {
  name: any;
  description?: string;
}
export interface MenuConfig extends BriefData {
  isRoleBased: boolean;
  isPermissionBased: boolean;
  isLevelBased: boolean;
  hasIcon: boolean;
  hasImage: boolean;
  hasDescription: boolean;
}

export function getMenuName(menu: MenuItem): string {
  if (menu.parent) {
    return getMenuName(menu.parent) + ` > ${menu.name}`;
  } else {
    return menu.name ?? '';
  }
}
export function getMenuDisplayOrder(menu: MenuItem): string {
  if (menu.parent) {
    return getMenuDisplayOrder(menu.parent) + ` -> ${menu.displayOrder}`;
  } else {
    return menu.displayOrder.toString();
  }
}
export function getFullUrlFromMenuItem(menuItem: MenuItem) {
  const buildUrl = (item: typeof menuItem): string => {
    if (!item.parent) {
      return item.url ?? '';
    }
    return `${buildUrl(item.parent)}${item.url}`;
  };

  return menuItem.url === null ? undefined : `/dashboard${buildUrl(menuItem)}`;
}
export function filterMenuItems(
  menuItems: MenuItem[],
  authorization: UserData,
  section?: string,
): MenuItem[] {
  // console.log('menuItems', menuItems);
  // console.log('authorization', authorization);
  return menuItems.filter((item) => {
    if (!item.isVisible || !item.isDashboard) return false;
    if (section && item.section !== section) return false;
    const isAuthorized = authorization.menuItems.includes(item.id);
    if (item.children && item.children.length > 0) {
      item.children = filterMenuItems(item.children, authorization, section);
    }
    if (authorization.isSuper) return true;
    return isAuthorized || (item.children && item.children.length > 0);
  });
}

export function findClosestMenuItem(menuItems: MenuItem[], path?: string) {
  if (!path) return undefined;
  let closestMatch: MenuItem | undefined = undefined;
  for (const menuItem of menuItems) {
    if (menuItem.url && path.startsWith(menuItem.url)) {
      if (!closestMatch || menuItem.url.length > closestMatch.url!.length) {
        closestMatch = menuItem;
      }
    }
  }
  console.log('closestMatch', closestMatch);
  return closestMatch;
}
export function urlCompare(url: string, itemUrl: string) {
  const pattern = new RegExp(
    '^' + itemUrl.replace(/{[^}]+}/g, '[^/]+') + '(?:/|$)',
  );
  return pattern.test(url);
}
