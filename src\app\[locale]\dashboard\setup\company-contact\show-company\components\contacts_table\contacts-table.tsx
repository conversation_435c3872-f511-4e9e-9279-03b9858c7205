'use client';

import { useQuery } from '@tanstack/react-query';
import { Edit2, Trash2, Plus, User } from 'lucide-react';
import Link from 'next/link';
import { modal, DEFAULT_MODAL } from '@/components/ui/overlay';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { Button } from '@/components/ui/button';
import { getQueryClient } from '@/utils/query-client';

interface ContactsTableProps {
  companyId: number;
  companyName: string;
}

export const ContactsTable = ({
  companyId,
  companyName,
}: ContactsTableProps) => {
  const { data, error } = useQuery({
    queryKey: [...CompanyQuery.tags, 'contacts', companyId],
    queryFn: () => CompanyQuery.contacts.getAll(companyId),
  });

  if (!data || data.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-sm font-medium text-brand-brown flex items-center gap-1">
            <User className="h-3 w-3" />
            Contacts for {companyName}
          </h3>
          <Link
            href={`/dashboard/setup/company-contact/show-company/${companyId}/contacts/add`}
          >
            <Button variant="main" size="sm" className="h-6 px-2 text-xs">
              <Plus className="mr-1 h-3 w-3" />
              Add
            </Button>
          </Link>
        </div>
        <div className="text-center py-8">
          <User className="h-8 w-8 text-gray-400 mx-auto mb-3" />
          <h4 className="text-md font-medium text-gray-900 mb-1">
            No contacts found
          </h4>
          <p className="text-sm text-gray-500">
            Add the first contact for this company.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-sm font-medium text-brand-brown flex items-center gap-1">
          <User className="h-3 w-3" />
          Contacts for {companyName}
        </h3>
        <Link
          href={`/dashboard/setup/company-contact/show-company/${companyId}/contacts/add`}
        >
          <Button
            variant="main"
            size="sm"
            className="h-6 px-2 text-xs  min-w-0"
          >
            <Plus className=" h-3 w-3" />
          </Button>
        </Link>
      </div>

      {error ? (
        <div className="text-red-500 p-4 text-center bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center justify-center space-x-2">
            <span className="text-red-600">⚠️</span>
            <span>Error loading contacts: {error.message}</span>
          </div>
        </div>
      ) : (
        <div className="grid gap-2">
          {data?.map((contact) => (
            <div
              key={contact.id}
              className="bg-white border border-gray-200 rounded-lg px-3 py-1 shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 flex-1 min-w-0">
                  <span
                    className={`text-sm font-medium ${contact.isArchived ? 'line-through text-gray-500' : 'text-gray-900'} truncate flex items-center gap-1`}
                  >
                    <User className="h-3 w-3 flex-shrink-0" />
                    {contact.fullName}
                  </span>
                  <span
                    className={`text-xs text-gray-600 ${contact.isArchived ? 'line-through' : ''}`}
                  >
                    {contact.contactType}
                  </span>
                  {contact.email && (
                    <span
                      className={`text-xs text-gray-600 ${contact.isArchived ? 'line-through' : ''} flex items-center gap-1`}
                    >
                      📧 {contact.email}
                    </span>
                  )}
                  {contact.telephone && (
                    <span
                      className={`text-xs text-gray-600 ${contact.isArchived ? 'line-through' : ''} flex items-center gap-1`}
                    >
                      📞 {contact.telephone}
                    </span>
                  )}
                  {contact.isArchived && (
                    <span className="text-xs bg-gray-200 text-gray-700 px-1.5 py-0.5 rounded-full">
                      Archived
                    </span>
                  )}
                </div>

                <div className="flex items-center gap-1 flex-shrink-0">
                  <Link
                    href={`/dashboard/setup/company-contact/show-company/${companyId}/contacts/${contact.id}`}
                  >
                    <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                      <Edit2 className="h-3 w-3" />
                    </Button>
                  </Link>
                  <Button
                    variant="remove"
                    size="sm"
                    iconName="RemoveIcon"
                    className="h-6 w-6 p-0"
                    onClick={() => {
                      modal(
                        ({ close }) => (
                          <MutationConfirmModal
                            close={close}
                            title="Delete Contact"
                            description={`Are you sure you want to delete "${contact.fullName}"?`}
                            mutateFn={() =>
                              CompanyQuery.contacts.delete(
                                companyId,
                                contact.id,
                              )
                            }
                            mutationKey={[
                              ...CompanyQuery.tags,
                              'contacts',
                              companyId,
                            ]}
                            onSuccess={async () => {
                              await getQueryClient().invalidateQueries({
                                queryKey: [
                                  ...CompanyQuery.tags,
                                  'contacts',
                                  companyId,
                                ],
                              });
                            }}
                            variant="destructive"
                            confirmButtonText="Delete"
                            confirmIconName="DeleteIcon"
                            loadingIconName="LoadingIcon"
                          />
                        ),
                        DEFAULT_MODAL,
                      ).open();
                    }}
                  ></Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ContactsTable;
