import { BriefData } from '@/models/BriefData';
import { PermissionKey } from '@/models/Permission';
import { Role } from '@/models/Role';
import { RoleData } from '@/schema/RoleSchema';

import fetcher from './fetcher';
const RoleQuery = {
  tags: ['Roles'] as const,
  getAll: async () => fetcher<BriefData[]>(`roles`),
  getOne: async (id: number) => fetcher<Role>(`roles/${id}`),
  create: async (data: RoleData) =>
    fetcher<number>(`roles`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),
  update: (id: number) => async (data: RoleData) =>
    fetcher<number>(`roles/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),
  setPermissions: (id: number) => async (permissions: PermissionKey[]) =>
    fetcher<boolean>(`roles/${id}/permissions`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ permissions }),
    }),
  getMenu: (id: number) => fetcher<number[]>(`roles/${id}/menu`),
  setMenu: (id: number) => async (data: number[]) =>
    fetcher(`roles/${id}/menu`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),
};
export default RoleQuery;
