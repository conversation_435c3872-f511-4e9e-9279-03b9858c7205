import { z } from 'zod';

export const OfferingCreateSchema = z.object({
  categoryId: z.string().nullable().optional(),
  groupTypeId: z.string().nullable().optional(),
  name: z.string().min(1, { message: 'Name is required' }),
  code: z.string().nullable().optional(),
  supplierItemNumber: z.string().nullable().optional(),
  publicDescription: z.string().nullable().optional(),
  internalDescription: z.string().nullable().optional(),
  displayOrder: z.string().nullable().optional(),
  unitChargedId: z.string().nullable().optional(),
  isUnitTypeEach: z.boolean().nullable().optional(),
  isAddOn: z.boolean().default(false),
  isForSmOnly: z.boolean().default(false),
  isInternalOnly: z.boolean().default(false),
  image: z
    .array(
      z.instanceof(File).refine((file) => file.size < 4 * 1024 * 1024, {
        message: 'File size must be less than 4MB',
      }),
    )
    .nullable(),
  imagePath: z.string().nullable().optional(),
  isActive: z.boolean().default(true),
  isObsolete: z.boolean().default(false),
  taxType: z.array(z.string()).nullable().optional(),
});

export type OfferingCreateData = z.infer<typeof OfferingCreateSchema>;

export const OfferingPropertyCreateSchema = z.object({
  code: z.string().nullable().optional(),
  supplierItemNumber: z.string().nullable().optional(),
  isForSmOnly: z.boolean().default(false),
  isInternalOnly: z.boolean().default(false),
  image: z
    .array(
      z.instanceof(File).refine((file) => file.size < 4 * 1024 * 1024, {
        message: 'File size must be less than 4MB',
      }),
    )
    .nullable(),
  imagePath: z.string().nullable().optional(),
  isActive: z.boolean().default(true),
});

export type OfferingPropertyCreateData = z.infer<
  typeof OfferingPropertyCreateSchema
>;
