import {
  Comic_Neue,
  IBM_Plex_Sans_Devanagari,
  Inter,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from 'next/font/google';

export const inter = Inter({ subsets: ['latin'], variable: '--font-inter' });
export const lora = Lora({
  subsets: ['latin'],
  weight: ['400', '500', '600'],
  variable: '--font-lora',
});
export const iBMPlexSans = IBM_Plex_Sans_Devanagari({
  subsets: ['latin'],
  weight: ['400', '500', '600'],
  variable: '--font-ibm',
});
export const comicNeue = Comic_Neue({
  subsets: ['latin'],
  weight: ['400'],
  variable: '--font-comic',
});
export const roboto = Roboto({
  subsets: ['latin'],
  weight: ['400', '500', '700'],
  variable: '--font-roboto',
});
