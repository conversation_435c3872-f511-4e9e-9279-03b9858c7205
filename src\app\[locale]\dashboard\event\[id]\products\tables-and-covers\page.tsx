import type { Metadata } from 'next';
import TablesAndCoversClient from './TablesAndCoversClient';

export const metadata: Metadata = {
  title: 'Tables and Covers | GOODKEY SHOW SERVICES LTD.',
  description:
    'Browse tables and covers products for your event at GOODKEY SHOW SERVICES LTD.',
};

export default async function TablesAndCoversPage(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;
  return <TablesAndCoversClient params={params} />;
}
