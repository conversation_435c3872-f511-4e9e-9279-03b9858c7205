import {
  ShowInList,
  ShowCreateRequest,
  ShowUpdateRequest,
  ShowScheduleCreateRequest,
  ShowScheduleUpdateRequest,
  ShowSchedule,
} from '@/models/Show';
import fetcher from './fetcher';
import { ShowGeneralInfoData } from '@/components/show-tabs/GeneralInfoTab';

const ShowQuery = {
  tags: ['Shows'] as const,

  getAll: async () => fetcher<ShowInList[]>('Shows'),

  getOne: async (id: number) =>
    fetcher<ShowGeneralInfoData>(`Shows/${id}/general-info`),

  create: async (data: ShowCreateRequest) =>
    fetcher<number>('Shows/general-info', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  update: (id: number) => async (data: ShowUpdateRequest) =>
    fetcher<boolean>(`Shows/${id}/general-info`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  delete: async (id: number) =>
    fetcher<boolean>(`Shows/${id}/general-info`, {
      method: 'DELETE',
    }),

  toggleArchive: async (id: number) =>
    fetcher<boolean>(`Shows/${id}/toggle-archive`, {
      method: 'PATCH',
    }),

  getHallContact: async (id: number) =>
    fetcher<{
      showId: number;
      hallId: number;
      contactId: number;
      hallName: string;
      hallCode: string;
      contactName: string;
      contactEmail: string;
      contactPhone: string;
    }>(`Shows/${id}/hall`),

  updateHallContact:
    (id: number) => async (data: { hallId: number; contactId: number }) =>
      fetcher<boolean>(`Shows/${id}/hall`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      }),

  // --- Show Schedules ---

  getSchedules: async (showId: number) =>
    fetcher<ShowSchedule[]>(`/shows/${showId}/schedules`),
  getSchedule: async (id: number) =>
    fetcher<ShowSchedule>(`/shows/schedules/${id}`),

  createSchedule: async (showId: number, data: ShowScheduleCreateRequest) =>
    fetcher(`/shows/${showId}/schedules`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  updateSchedule:
    (showId: number, id: number) => async (data: ShowScheduleUpdateRequest) =>
      fetcher(`/shows/${showId}/schedules/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      }),

  deleteSchedule: async (showId: number, id: number) =>
    fetcher(`/shows/${showId}/schedules/${id}`, { method: 'DELETE' }),

  toggleScheduleConfirmed: async (showId: number, id: number) =>
    fetcher(`/shows/${showId}/schedules/${id}/toggle-confirmed`, {
      method: 'PATCH',
    }),

  toggleScheduleApplyToService: async (showId: number, id: number) =>
    fetcher(`/shows/${showId}/schedules/${id}/toggle-apply-to-service`, {
      method: 'PATCH',
    }),
};

export default ShowQuery;
