import { Contact } from '@/models/Contact';
import fetcher from './fetcher';
import { ShowContactData } from '@/schema/ShowContactSchema';

const ShowContactQuery = {
  tags: ['ShowContact'] as const,

  // GET: /ShowContact/{locationId}
  getByLocation: async (locationId: number) => {
    return fetcher<Contact[]>(`ShowContact/${locationId}`);
  },

  // GET: /ShowContact/contact/{contactId}
  getById: async (contactId: number) => {
    return fetcher<Contact>(`ShowContact/contact/${contactId}`);
  },

  // POST: /ShowContact
  create: async (data: ShowContactData) => {
    return fetcher<boolean>('ShowContact', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
  },

  // PATCH: /ShowContact/{contactId}
  update: (contactId: number) => async (data: ShowContactData) => {
    return fetcher<boolean>(`ShowContact/${contactId}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
  },
};

export default ShowContactQuery;
