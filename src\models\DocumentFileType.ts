export interface DocumentFileTypeInList {
  id: number;
  name: string;
  extensionCode: string;
  extension: string;
  isImage: boolean;
}

export interface DocumentFileTypeDetail {
  id: number;
  name: string;
  extensionCode: string;
  extension: string;
  isAvailable: boolean;
  isImage: boolean;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: string;
}

export interface DocumentFileTypeCreateData {
  name: string;
  extensionCode: string;
  extension: string;
  isImage: boolean;
}

export interface DocumentFileTypeUpdateData {
  name: string;
  extensionCode: string;
  extension: string;
  isAvailable: boolean;
  isImage: boolean;
}
