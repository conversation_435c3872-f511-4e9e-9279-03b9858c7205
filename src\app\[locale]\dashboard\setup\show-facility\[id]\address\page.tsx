import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import ShowLocationQuery from '@/services/queries/ShowLocationQuery';
import { getQueryClient } from '@/utils/query-client';
import ShowFacilityAddressInfo from '../components/show_facility_address_info';

export default async function ShowFacilityAdressPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;

    const client = getQueryClient();

    if (id !== 'add') {
      if (Number.isNaN(Number(id))) throw new Error();
      await client.prefetchQuery({
        queryKey: ['Show Facility', { id: Number(id) }],
        queryFn: () => ShowLocationQuery.getById(Number(id)),
      });
    }

    return (
      <div>
        <HydrationBoundary state={dehydrate(client)}>
          <ShowFacilityAddressInfo id={Number(id)} />
        </HydrationBoundary>
      </div>
    );
  } catch (error) {
    redirect('/dashboard/setup/show-facility/add');
  }
}
