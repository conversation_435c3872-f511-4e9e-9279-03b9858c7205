import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { ReactNode } from 'react';

import PermissionQuery from '@/services/queries/PermissionQuery';
import RoleQuery from '@/services/queries/RoleQuery';
import { getQueryClient } from '@/utils/query-client';

import AddRoleSection from './components/add_role_section';

export default async function Layout({
  children,
  params,
}: Readonly<{
  children: ReactNode;
  params: Promise<{ locale: string; id: string }>;
}>) {
  // Await the params before destructuring
  const resolvedParams = await params;
  const { id } = resolvedParams;

  try {
    const queryClient = getQueryClient();

    if (id !== 'add') {
      if (isNaN(Number.parseInt(id))) throw new Error();

      await queryClient.fetchQuery({
        queryKey: [...RoleQuery.tags, { id: Number.parseInt(id) }],
        queryFn: () => RoleQuery.getOne(Number.parseInt(id)),
      });
    }
    await queryClient.prefetchQuery({
      queryKey: PermissionQuery.tags,
      queryFn: PermissionQuery.getAll,
    });
    return (
      <div className="flex flex-col self-stretch w-full overflow-y-auto scrollbar-hide  gap-8 ">
        <HydrationBoundary state={dehydrate(queryClient)}>
          <AddRoleSection isCreate={isNaN(Number.parseInt(id))}>
            {children}
          </AddRoleSection>
        </HydrationBoundary>
      </div>
    );
  } catch (e) {
    redirect('/dashboard/setup/users-roles/role-management/add');
  }
}
