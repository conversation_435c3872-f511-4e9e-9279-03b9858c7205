'use client';
export const imageLoader = ({
  src,
  width,
  height,
}: {
  src?: string;
  width?: number;
  height?: number;
}) => {
  return `/images${src}?${width ? 'width=' + width : ''}${height ? '&height=' + height : ''}`;
};

export const publicImageLoader = ({
  src,
  width,
  height,
}: {
  src?: string;
  width?: number;
  height?: number;
}) => {
  return `${src}?${width ? 'width=' + width : ''}${height ? '&height=' + height : ''}`;
};
