import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import { getQueryClient } from '@/utils/query-client';
import AppLayout from '@/components/ui/app_layout';
import ScheduleTable from './components/schedule_table';
import ScheduleQuery from '@/services/queries/ScheduleQuery';
export const metadata: Metadata = {
  title: 'Goodkey | Schedules',
};

export default async function SchedulesPage() {
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery({
    queryKey: ScheduleQuery.tags,
    queryFn: () => ScheduleQuery.getAll(),
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'Master-Setup', link: '/dashboard/setup/master-setup' },
        { title: 'Schedules', link: '/dashboard/setup/master-setup/schedules' },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <ScheduleTable />
      </HydrationBoundary>
    </AppLayout>
  );
}
