import './style/index.scss';
import { cn } from '@/lib/utils';

import { Button } from '../../button';
interface ImageChangerProps {
  src?: string;
  onChange?: (src: File) => void;
  className?: string;
}

export default function ImageChanger({
  src,
  className,
  onChange,
}: ImageChangerProps) {
  const uploadImage = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/png, image/jpeg';
    input.onchange = (_) => {
      const files: FileList = input.files as FileList;

      onChange?.(files[0]);
    };
    input.click();
  };
  return (
    <div
      className={cn(
        'w-full h-full rounded-full overflow-hidden border-2 border-primary relative group cursor-pointer',
        className,
      )}
      onClick={uploadImage}
    >
      <img
        src={src + '?' + performance.now()}
        alt="image"
        className="object-cover w-full h-full"
      />
      <Button className="hidden group-hover:block bg-primary p-2 change-image-span text-white absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
        Change Image
      </Button>
    </div>
  );
}
