import * as z from 'zod';

export const ContactCreateSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z
    .string()
    .email({ message: 'Please enter a valid email address' })
    .optional(),
  telephone: z.string().optional(),
  ext: z.string().optional(),
  cellphone: z.string().optional(),
  contactTypeId: z
    .string()
    .min(1, { message: 'Contact type is required' })
    .transform(Number),
  isArchived: z.boolean().optional(),
});

export const ContactUpdateSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z
    .string()
    .email({ message: 'Please enter a valid email address' })
    .optional(),
  telephone: z.string().optional(),
  ext: z.string().optional(),
  cellphone: z.string().optional(),
  contactTypeId: z
    .string()
    .min(1, { message: 'Contact type is required' })
    .transform(Number),
  isArchived: z.boolean().default(false),
});

export type ContactCreateData = z.infer<typeof ContactCreateSchema>;
export type ContactUpdateData = z.infer<typeof ContactUpdateSchema>;
