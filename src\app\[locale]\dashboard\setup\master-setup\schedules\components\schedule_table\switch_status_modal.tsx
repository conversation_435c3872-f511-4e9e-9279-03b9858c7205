import { useMutation } from '@tanstack/react-query';

import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';
import { ClearIcon, LoadingIcon, SaveIcon } from '@/assets/Icons';
import ScheduleQuery from '@/services/queries/ScheduleQuery';
import { getQueryClient } from '@/utils/query-client';

interface ISwitchStatusModal {
  close: () => void;
  isChecked: boolean;
  scheduleId: number;
}

export default function SwitchStatusModal({
  close,
  isChecked,
  scheduleId,
}: ISwitchStatusModal) {
  const { mutate, isPending } = useMutation({
    mutationKey: ScheduleQuery.tags,
    mutationFn: ScheduleQuery.toggleActive,
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({
        queryKey: ScheduleQuery.tags,
      });
      close();
    },
  });

  const titleText = isChecked ? 'Activate Schedule' : 'Deactivate Schedule';
  const descriptionText = isChecked
    ? 'This schedule will be activated and available for use.'
    : "This schedule will be deactivated and won't be available for use.";
  const confirmButtonText = isChecked ? 'Activate' : 'Deactivate';
  const confirmButtonVariant = isChecked ? 'primary' : 'destructive';

  return (
    <AlertDialog open={true} onOpenChange={close}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{titleText}</AlertDialogTitle>
        </AlertDialogHeader>
        <div className="py-2 text-slate-700">{descriptionText}</div>
        <AlertDialogFooter>
          <AlertDialogCancel asChild>
            <Button variant="outline" disabled={isPending} iconName="ClearIcon">
              Cancel
            </Button>
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              onClick={() => mutate(scheduleId)}
              disabled={isPending}
              variant={confirmButtonVariant}
              iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
              iconProps={{
                className: 'text-white',
              }}
            >
              {isPending ? 'Please wait...' : confirmButtonText}
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
