export class AppError extends Error {
  public readonly status: number;

  // Add an optional flag to indicate if the message is likely safe for UI display
  public readonly isUserSafe: boolean;

  constructor(message: string, status: number, isUserSafe: boolean = false) {
    super(message);
    this.status = status;
    // Default to false unless explicitly set
    this.isUserSafe = isUserSafe;
    this.name = 'AppError';

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }
}
