import * as z from 'zod';

// ShowLocationGeneralSchema
export const ShowLocationGeneralSchema = z.object({
  id: z.number().optional(),
  locationCode: z.string().min(1, { message: 'Required' }),
  name: z.string().min(1, { message: 'Name is required' }),
  telephone: z.string().optional().nullable(),
  tollfree: z.string().optional().nullable(),
  fax: z.string().optional().nullable(),
  mapLink: z.string().optional().nullable(),
  website: z.string().optional().nullable(),
  email: z
    .string()
    .email({ message: 'Invalid email address' })
    .optional()
    .nullable(),
  accessPlanPath: z.string().optional().nullable(),
  isArchived: z.boolean().default(false),
  accessPlan: z
    .array(
      z.instanceof(File).refine((file) => file.size < 4 * 1024 * 1024, {
        message: 'File size must be less than 4MB',
      }),
    )
    .min(1, 'Please select at least one file.')
    .max(1)
    .nullable(),
});
export type ShowLocationGeneralData = z.infer<typeof ShowLocationGeneralSchema>;

// ShowLocationAddressSchema
export const ShowLocationAddressSchema = z.object({
  id: z.number(),
  address1: z.string().optional().nullable(),
  address2: z.string().optional().nullable(),
  postalCode: z.string().optional().nullable(),
  city: z.string().optional().nullable(),
  provinceId: z.string().optional().nullable(),
  countryId: z.string().optional().nullable(),
  sameForShipping: z.boolean().default(false),
  shippingAddress1: z.string().optional().nullable(),
  shippingAddress2: z.string().optional().nullable(),
  shippingPostalCode: z.string().optional().nullable(),
  shippingCity: z.string().optional().nullable(),
  shippingProvinceId: z.string().optional().nullable(),
  shippingCountryId: z.string().optional().nullable(),
});
export type ShowLocationAddressData = z.infer<typeof ShowLocationAddressSchema>;
