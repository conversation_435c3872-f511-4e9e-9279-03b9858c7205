import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { ReactNode } from 'react';
import { getQueryClient } from '@/utils/query-client';
import PropertySection from './components/property_section';
import AppLayout from '@/components/ui/app_layout';

export default async function Layout({
  children,
  params,
}: Readonly<{
  children: ReactNode;
  params: Promise<{ id: string }>;
}>) {
  const resolvedParams = await params;
  const { id } = resolvedParams;

  const isEdit = !Number.isNaN(Number(id)) && id !== 'add';

  if (!isEdit && id !== 'add') {
    redirect('/dashboard/setup/master-setup/property/add');
  }

  return (
    <div className="flex flex-col self-stretch w-full gap-8">
      <HydrationBoundary state={dehydrate(getQueryClient())}>
        <AppLayout
          items={[
            { title: 'Setup', link: '/dashboard/setup' },
            { title: 'Master-Setup', link: '/dashboard/setup/master-setup' },
            { title: 'Property', link: '/dashboard/setup/property' },
            {
              title: 'Property Detail',
              link: `/dashboard/setup/master-setup/property/${id}`,
            },
          ]}
        >
          <div className="flex w-full">
            <PropertySection id={isEdit ? Number(id) : undefined} />
            <div className="w-full overflow-y-auto scrollbar-hide">
              {children}
            </div>
          </div>
        </AppLayout>
      </HydrationBoundary>
    </div>
  );
}
