module.exports = {
  settings: {
    'import/resolver': {
      typescript: {
        alwaysTryTypes: true,
        project: './tsconfig.json',
      },
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
        paths: ['src'],
      },
    },
  },
  extends: [
    'next/core-web-vitals',
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:import/recommended',
    'plugin:import/typescript',
    'airbnb-typescript',
    'airbnb/hooks',
    'plugin:react/recommended',
    'plugin:react/jsx-runtime',
    'plugin:prettier/recommended',
  ],
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 12,
    sourceType: 'module',
    project: './tsconfig.json',
  },
  plugins: ['react', '@typescript-eslint', 'react-hooks', 'unused-imports'],
  globals: { Atomics: 'readonly', SharedArrayBuffer: 'readonly' },
  rules: {
    'no-unused-vars': 'off',
    '@typescript-eslint/no-unused-vars': [
      'warn', // Keep as warning but won't break build
      { argsIgnorePattern: '^_', varsIgnorePattern: '^_' },
    ],
    'react/no-unescaped-entities': 'off',
    '@next/next/no-img-element': 0,
    'react/no-unknown-property': ['error', { ignore: ['css'] }],
    'react/prop-types': 0,
    'no-console': 'warn', // Keep as warning but won't break build
    'linebreak-style': 'off',
    'prettier/prettier': ['warn', { endOfLine: 'auto' }], // Changed to warn
    'react-hooks/rules-of-hooks': 2,
    'react-hooks/exhaustive-deps': 'warn', // Keep as warning but won't break build
    'no-empty-pattern': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/no-empty-interface': 'off',
    'unused-imports/no-unused-imports': 'warn',
    'import/no-named-as-default': 'warn', // Added this rule as warning
    '@typescript-eslint/naming-convention': [
      'error',
      {
        selector: 'typeProperty',
        format: ['camelCase', 'PascalCase'],
        leadingUnderscore: 'allow',
        trailingUnderscore: 'allow',
      },
      {
        selector: 'parameter',
        format: ['camelCase', 'PascalCase'],
        leadingUnderscore: 'allow',
        trailingUnderscore: 'allow',
      },
      {
        selector: 'interface',
        format: ['PascalCase'],
        leadingUnderscore: 'allow',
        trailingUnderscore: 'allow',
      },
    ],
  },
  ignorePatterns: [
    '.eslintrc.js',
    'next.config.js',
    'next.config.mjs', // Added mjs config
    'postcss.config.js',
    'tailwind.config.js',
    'tailwind.config.ts', // Added ts config
    'node_modules/',
    'dist/',
    'build/',
    '.next/',
    'coverage/',
    'server.js',
  ],
};
