'use client';

import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import {
  Upload,
  AlertCircle,
  FileSpreadsheet,
  CheckCircle2,
} from 'lucide-react';
import * as XLSX from 'xlsx';
import { Button } from '@/components/ui/button';
import ModalContainer from '@/components/ui/overlay/components/modal_container';
import { getQueryClient } from '@/utils/query-client';
import UsersQuery from '@/services/queries/UsersQuery';
import { modal } from '@/components/ui/overlay';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

// Define the structure for the Excel data - matching UserSchema
interface UserExcelRow {
  firstName: string;
  lastName: string;
  verificationEmail: string;
  statusId?: string;
  salutationId?: string;
  mobileNumber?: string;
  departmentId?: string;
  workEmail?: string;
  workPhoneNumber?: string;
  // Removed roleId field as it's not needed
}

// Define the structure for import results
interface ImportResult {
  success: number;
  failed: number;
  errors: string[];
  total: number;
}

export default function ExcelImportModal() {
  const [file, setFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);

  // Check if a file is an Excel file
  const isExcelFile = (fileToCheck: File): boolean => {
    return (
      fileToCheck.type ===
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      fileToCheck.type === 'application/vnd.ms-excel' ||
      fileToCheck.name.endsWith('.xlsx') ||
      fileToCheck.name.endsWith('.xls')
    );
  };

  // Read Excel file and convert to JSON
  const readExcelFile = (fileToRead: File): Promise<UserExcelRow[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const data = e.target?.result;
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const json = XLSX.utils.sheet_to_json<UserExcelRow>(worksheet);
          resolve(json);
        } catch (error) {
          reject(new Error('Failed to parse Excel file'));
        }
      };

      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };

      reader.readAsArrayBuffer(fileToRead);
    });
  };

  // Simple check for empty data without validation
  const checkExcelData = (data: UserExcelRow[]): boolean => {
    if (data.length === 0) {
      throw new Error('The Excel file contains no data rows.');
    }
    return true;
  };

  // Map Excel row to API data structure
  const mapExcelRowToUserData = (row: UserExcelRow) => {
    // Map to the structure expected by the API
    return {
      firstName: row.firstName,
      lastName: row.lastName,
      verificationEmail: row.verificationEmail,
      mobileNumber: row.mobileNumber,
      workEmail: row.workEmail,
      workPhoneNumber: row.workPhoneNumber,
      statusId: row.statusId,
      salutationId: row.salutationId,
      departmentId: row.departmentId,
      archive: false, // Required field with default value
    };
  };

  // Handle drag events
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  // Handle file drop
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const droppedFile = e.dataTransfer.files[0];
      if (isExcelFile(droppedFile)) {
        setFile(droppedFile);
      }
    }
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      if (isExcelFile(selectedFile)) {
        setFile(selectedFile);
      }
    }
  };

  // Generate and download template with example values
  const downloadTemplate = () => {
    // Create example data with placeholder values
    const exampleData = [
      {
        firstName: 'John',
        lastName: 'Doe',
        verificationEmail: '<EMAIL>',
        statusId: '1', // Active
        salutationId: '1', // Mr.
        mobileNumber: '444444444',
        departmentId: '1', // IT
        workEmail: '<EMAIL>',
        workPhoneNumber: '444444444',
      },
      {
        firstName: 'Jane',
        lastName: 'Smith',
        verificationEmail: '<EMAIL>',
        statusId: '2', // Inactive
        salutationId: '2', // Mrs.
        mobileNumber: '55555555',
        departmentId: '2', // HR
        workEmail: '<EMAIL>',
        workPhoneNumber: '55555555',
      },
    ];

    // Create the worksheet with the example data
    const worksheet = XLSX.utils.json_to_sheet(exampleData);

    // Create a new workbook and add the worksheet
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Users');

    // Write the workbook to a file
    XLSX.writeFile(workbook, 'user_import_template.xlsx');
  };

  // Process the Excel file and create users
  const { mutate, isPending } = useMutation({
    mutationFn: async () => {
      if (!file) return null;

      setIsProcessing(true);
      setProgress(0);

      try {
        // Read the Excel file
        const data = await readExcelFile(file);

        // Just check if the data is not empty
        checkExcelData(data);

        const result: ImportResult = {
          success: 0,
          failed: 0,
          errors: [],
          total: data.length,
        };

        // Process each row
        for (let i = 0; i < data.length; i++) {
          try {
            const userData = mapExcelRowToUserData(data[i]);
            await UsersQuery.create(userData);
            result.success++;
          } catch (error) {
            result.failed++;
            result.errors.push(`Row ${i + 2}: ${(error as Error).message}`);
          }

          // Update progress
          setProgress(Math.round(((i + 1) / data.length) * 100));
        }

        // Refresh the users list
        await getQueryClient().invalidateQueries({ queryKey: UsersQuery.tags });

        setImportResult(result);
        return result;
      } catch (error) {
        setImportResult({
          success: 0,
          failed: 0,
          errors: [(error as Error).message],
          total: 0,
        });
        throw error;
      } finally {
        setIsProcessing(false);
      }
    },
  });

  return (
    <ModalContainer
      className="gap-4 max-w-md"
      title="Import Users"
      description="Upload an Excel file to create multiple users at once."
      controls={
        <div className="flex flex-row gap-2">
          {!importResult && !isProcessing && (
            <Button
              onClick={() => mutate()}
              variant="default"
              disabled={!file || isPending}
            >
              {isPending ? 'Importing...' : 'Import Users'}
              {!isPending && <Upload className="ml-2 h-4 w-4" />}
            </Button>
          )}
          <Button onClick={() => modal.close()} variant="secondary">
            {importResult ? 'Close' : 'Cancel'}
          </Button>
        </div>
      }
    >
      {!importResult && !isProcessing ? (
        <div className="space-y-4">
          <div
            className={cn(
              'border-2 border-dashed rounded-md p-6 text-center',
              dragActive ? 'border-primary bg-primary/5' : 'border-gray-300',
              file ? 'bg-muted/50' : '',
            )}
            onDragEnter={handleDrag}
            onDragOver={handleDrag}
            onDragLeave={handleDrag}
            onDrop={handleDrop}
          >
            {file ? (
              <div className="flex flex-col items-center gap-2">
                <FileSpreadsheet className="h-10 w-10 text-primary" />
                <p className="font-medium">{file.name}</p>
                <p className="text-sm text-muted-foreground">
                  {(file.size / 1024).toFixed(2)} KB
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFile(null)}
                  className="mt-2"
                >
                  Change File
                </Button>
              </div>
            ) : (
              <div className="flex flex-col items-center gap-2">
                <FileSpreadsheet className="h-10 w-10 text-muted-foreground" />
                <p className="font-medium">Drag and drop Excel file here</p>
                <p className="text-sm text-muted-foreground">or</p>
                <label className="cursor-pointer">
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    type="button"
                  >
                    Browse Files
                  </Button>
                  <input
                    type="file"
                    accept=".xlsx,.xls"
                    className="hidden"
                    onChange={handleFileChange}
                  />
                </label>
              </div>
            )}
          </div>

          <div className="text-center">
            <Button variant="link" size="sm" onClick={downloadTemplate}>
              Download template
            </Button>
          </div>
        </div>
      ) : isProcessing ? (
        <div className="space-y-4">
          <Alert>
            <AlertTitle>Processing users</AlertTitle>
            <AlertDescription>
              Please wait while we process your file...
            </AlertDescription>
          </Alert>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{progress}%</span>
            </div>
            <Progress value={progress} />
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <Alert variant={importResult?.failed ? 'destructive' : 'default'}>
            <CheckCircle2 className="h-4 w-4" />
            <AlertTitle>
              {importResult?.failed
                ? 'Import completed with errors'
                : 'Import completed successfully'}
            </AlertTitle>
            <AlertDescription>
              Successfully imported {importResult?.success} out of{' '}
              {importResult?.total} users.
            </AlertDescription>
          </Alert>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Success rate</span>
              <span>
                {Math.round(
                  ((importResult?.success || 0) / (importResult?.total || 1)) *
                    100,
                )}
                %
              </span>
            </div>
            <Progress
              value={
                ((importResult?.success || 0) / (importResult?.total || 1)) *
                100
              }
            />
          </div>

          {importResult &&
            importResult.failed > 0 &&
            importResult.errors &&
            importResult.errors.length > 0 && (
              <div className="mt-4 space-y-2">
                <h4 className="font-medium">Errors:</h4>
                <div className="max-h-40 overflow-y-auto rounded border p-2 text-sm">
                  {importResult.errors.map((error, index) => (
                    <div key={index} className="py-1">
                      <AlertCircle className="inline h-3 w-3 mr-1 text-destructive" />
                      {error}
                    </div>
                  ))}
                </div>
              </div>
            )}
        </div>
      )}
    </ModalContainer>
  );
}
