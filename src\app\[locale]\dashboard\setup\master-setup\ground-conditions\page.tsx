import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import AppLayout from '@/components/ui/app_layout';
import GroundConditionQuery from '@/services/queries/GroundConditionQuery';
import { getQueryClient } from '@/utils/query-client';
import GroundConditionsManagementSection from './components/GroundConditionsManagementSection';

export default async function GroundConditionsManagementPage() {
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery({
    queryKey: ['ground-conditions'],
    queryFn: GroundConditionQuery.getAll,
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'Master Setup', link: '/dashboard/setup/master-setup' },
        {
          title: 'Ground Conditions',
          link: '/dashboard/setup/master-setup/ground-conditions',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <GroundConditionsManagementSection />
      </HydrationBoundary>
    </AppLayout>
  );
}
