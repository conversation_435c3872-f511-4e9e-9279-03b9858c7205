'use client';
import { ClassAttributes, FormHTMLAttributes, ReactNode } from 'react';

import './style/index.scss';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Spinner } from '@/components/ui/spinner';
import { cn } from '@/lib/utils';

interface ModalContainerProps {
  title?: string;
  isLoading?: boolean;
  className?: string;
  onSubmit?: React.FormEventHandler<HTMLFormElement>;
  controls?: ReactNode;
  children?: ReactNode;
  description?: string;
  controlsPosition?: 'center' | 'end';
  overflowHidden?: boolean;
  onClose?: () => void;
}
const Form = (
  props: ClassAttributes<HTMLFormElement> & FormHTMLAttributes<HTMLFormElement>,
) => <form {...props} />;
const Div = (
  props: ClassAttributes<HTMLDivElement> & React.HTMLAttributes<HTMLDivElement>,
) => <div {...props} />;

export default function ModalContainer({
  title,
  description,
  children,
  onSubmit,
  controls,
  isLoading,
  className = '',
  controlsPosition = 'end',
  overflowHidden,
  onClose,
}: ModalContainerProps) {
  const Container = onSubmit ? Form : Div;

  return (
    <Container onSubmit={onSubmit as any} className="modal-container h-full">
      {isLoading ? (
        <Spinner className="w-20 h-20" />
      ) : (
        <Card className={cn('h-full', className)}>
          <CardHeader className="pb-0">
            <CardTitle className="pb-2 text-main">{title}</CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </CardHeader>
          {children && (
            <CardContent
              className="modal-body flex-1"
              style={overflowHidden ? { overflow: 'hidden' } : undefined}
            >
              {children}
            </CardContent>
          )}
          {controls && (
            <CardFooter
              className={cn(
                'gap-2',
                controlsPosition == 'center' ? 'justify-center' : 'justify-end',
              )}
            >
              {controls}
            </CardFooter>
          )}
        </Card>
      )}
    </Container>
  );
}
