import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import MenuItemTable from './components/menu_item_table';
import { redirect } from 'next/navigation';
import MenuQuery from '@/services/queries/MenuQuery';

export default async function Page() {
  const queryClient = getQueryClient();
  try {
    await queryClient.prefetchQuery({
      queryKey: [...MenuQuery.tags],
      queryFn: () => MenuQuery.getAll(),
    });
    const sections = await queryClient.fetchQuery({
      queryKey: [...MenuQuery.tags, 'sections'],
      queryFn: () => MenuQuery.getSections(),
    });

    return (
      <AppLayout
        items={[
          { title: 'Setup', link: '/dashboard/setup' },
          { title: 'System Setup', link: '/dashboard/setup/system-setup' },
          {
            title: `Menu Management`,
            link: `/dashboard/setup/system-setup/menu-management`,
          },
        ]}
      >
        <HydrationBoundary state={dehydrate(queryClient)}>
          <MenuItemTable sections={sections.map((c) => c.name)} />
        </HydrationBoundary>
      </AppLayout>
    );
  } catch {
    redirect('/');
  }
}
