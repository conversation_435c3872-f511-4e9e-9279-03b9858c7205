import { create } from 'zustand';

interface SetupSidebarState {
  collapsed: boolean;
  hovered: boolean;
  userToggled: boolean;
  setCollapsed: (collapsed: boolean) => void;
  setHovered: (hovered: boolean) => void;
  toggleCollapsed: () => void;
  resetUserToggled: () => void;
}

export const useSetupSidebarStore = create<SetupSidebarState>()((set) => ({
  collapsed: false, // Default to opened (not collapsed)
  hovered: false,
  userToggled: false, // Track if user has manually toggled the sidebar
  setCollapsed: (collapsed: boolean) => set(() => ({ collapsed })),
  setHovered: (hovered: boolean) => set(() => ({ hovered })),
  toggleCollapsed: () =>
    set((state) => ({
      collapsed: !state.collapsed,
      userToggled: true,
    })),
  resetUserToggled: () => set(() => ({ userToggled: false })),
}));
