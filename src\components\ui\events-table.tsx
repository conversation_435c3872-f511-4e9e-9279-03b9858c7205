'use client';

import { useState, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  CalendarIcon,
  MapPinIcon,
  AddIcon,
  SearchIcon,
  WarningIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@/assets/Icons';
import { useRouter } from 'next/navigation';

interface Event {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  location: string;
  province: string;
  floorPlan: 'Approved' | 'Waiting for Approval' | 'Add Floor Plan';
}

const eventsData: Event[] = [
  {
    id: '1',
    name: 'TEST SANDY',
    startDate: '2006-11-29',
    endDate: '2006-12-03',
    location: 'Vancouver Convention Centre East',
    province: 'BRITISH COLUMBIA',
    floorPlan: 'Approved',
  },
  {
    id: '2',
    name: 'CIM CONVENTION + EXPO 2023 - MTL',
    startDate: '2023-04-29',
    endDate: '2023-05-02',
    location: 'Palais Des Congres Montreal',
    province: 'QUEBEC',
    floorPlan: 'Waiting for Approval',
  },
  {
    id: '3',
    name: 'RVC 2023',
    startDate: '2023-05-30',
    endDate: '2023-06-01',
    location: 'Quebec City Convention Centre',
    province: 'QUEBEC',
    floorPlan: 'Waiting for Approval',
  },
  {
    id: '4',
    name: 'GLOBAL ENERGY SHOW 2023',
    startDate: '2023-06-12',
    endDate: '2023-06-14',
    location: 'Calgary Exhibition & Stampede Round-Up Centre',
    province: 'ALBERTA',
    floorPlan: 'Waiting for Approval',
  },
  {
    id: '5',
    name: '24TH WORLD PETROLEUM CONGRESS',
    startDate: '2023-09-17',
    endDate: '2023-09-21',
    location: 'Stampede Park BMO Centre',
    province: 'ALBERTA',
    floorPlan: 'Add Floor Plan',
  },
  {
    id: '6',
    name: 'THE GREAT CANADIAN TRADE FAIR 2024',
    startDate: '2024-04-11',
    endDate: '2024-04-13',
    location: 'Millennium Place',
    province: 'ALBERTA',
    floorPlan: 'Waiting for Approval',
  },
  {
    id: '7',
    name: 'STETTLER TRADE SHOW 2024',
    startDate: '2024-04-11',
    endDate: '2024-04-13',
    location: 'Stettler Recreation Centre',
    province: 'ALBERTA',
    floorPlan: 'Waiting for Approval',
  },
  {
    id: '8',
    name: 'HORSE EXPO CANADA 2024',
    startDate: '2024-04-25',
    endDate: '2024-04-27',
    location: 'Westerner Park',
    province: 'ALBERTA',
    floorPlan: 'Waiting for Approval',
  },
  {
    id: '9',
    name: 'CIM CONNECT 2024',
    startDate: '2024-05-11',
    endDate: '2024-05-13',
    location: 'Vancouver Convention Centre West',
    province: 'BRITISH COLUMBIA',
    floorPlan: 'Approved',
  },
  {
    id: '10',
    name: 'RENDEZ-VOUS CANADA 2024',
    startDate: '2024-05-13',
    endDate: '2024-05-16',
    location: 'Edmonton Convention Centre',
    province: 'ALBERTA',
    floorPlan: 'Waiting for Approval',
  },
  {
    id: '11',
    name: 'CARBON CAPTURE CANADA 2024',
    startDate: '2024-09-09',
    endDate: '2024-09-10',
    location: 'Edmonton Convention Centre',
    province: 'ALBERTA',
    floorPlan: 'Waiting for Approval',
  },
  {
    id: '12',
    name: 'AGRI-TRADE EQUIPMENT EXPO 2024',
    startDate: '2024-11-13',
    endDate: '2024-11-15',
    location: 'Westerner Park (All Halls)',
    province: 'ALBERTA',
    floorPlan: 'Waiting for Approval',
  },
  {
    id: '13',
    name: 'FESTIVAL OF TREES 2024 - RED DEER',
    startDate: '2024-11-28',
    endDate: '2024-12-01',
    location: 'Westerner Park',
    province: 'ALBERTA',
    floorPlan: 'Waiting for Approval',
  },
  {
    id: '14',
    name: 'ALBERTA GIFT FAIR SPRING 2025',
    startDate: '2025-02-23',
    endDate: '2025-02-26',
    location: 'Edmonton Expo Centre',
    province: 'ALBERTA',
    floorPlan: 'Add Floor Plan',
  },
  {
    id: '15',
    name: 'SMR 2025',
    startDate: '2025-03-04',
    endDate: '2025-03-05',
    location: 'Strathcona County Hall',
    province: 'ALBERTA',
    floorPlan: 'Add Floor Plan',
  },
];

export default function EventsTable() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [provinceFilter, setProvinceFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [locationFilter, setLocationFilter] = useState<string>('all');

  const provinces = useMemo(() => {
    const uniqueProvinces = Array.from(
      new Set(eventsData.map((event) => event.province)),
    );
    return ['all', ...uniqueProvinces];
  }, []);

  const locations = useMemo(() => {
    const uniqueLocations = Array.from(
      new Set(eventsData.map((event) => event.location)),
    );
    return ['all', ...uniqueLocations];
  }, []);

  const filteredEvents = useMemo(() => {
    return eventsData.filter((event) => {
      const searchMatch =
        searchTerm === '' ||
        event.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.province.toLowerCase().includes(searchTerm.toLowerCase());

      const provinceMatch =
        provinceFilter === 'all' || event.province === provinceFilter;

      const locationMatch =
        locationFilter === 'all' || event.location === locationFilter;

      const statusMatch =
        statusFilter === 'all' || event.floorPlan === statusFilter;

      return searchMatch && provinceMatch && locationMatch && statusMatch;
    });
  }, [searchTerm, provinceFilter, locationFilter, statusFilter]);

  const activeFilterCount = [
    searchTerm && searchTerm.trim() !== '',
    provinceFilter !== 'all',
    locationFilter !== 'all',
    statusFilter !== 'all',
  ].filter(Boolean).length;

  const resetFilters = () => {
    setSearchTerm('');
    setProvinceFilter('all');
    setLocationFilter('all');
    setStatusFilter('all');
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}/${date.getFullYear()}`;
  };

  const getFloorPlanBadge = (status: string) => {
    if (status === 'Waiting for Approval') {
      return (
        <span className="text-[#77400F] font-medium flex items-center">
          <WarningIcon className="h-4 w-4 mr-1 text-[#77400F]" />
          Waiting for Approval
        </span>
      );
    } else if (status === 'Add Floor Plan') {
      return (
        <Button
          variant="ghost"
          size="sm"
          className="text-[#00646C] hover:text-[#00646C]/80 p-0"
        >
          <AddIcon className="h-3.5 w-3.5 mr-1" /> Add Floor Plan
        </Button>
      );
    } else {
      return (
        <span className="text-[#CED600] font-medium flex items-center">
          <CheckCircleIcon className="h-4 w-4 mr-1 text-[#CED600]" />
          Approved
        </span>
      );
    }
  };

  const handleViewEvent = (eventId: string) => {
    // Always use ID "1" for TEST SANDY to match our mock data
    if (eventId === '1') {
      router.push(`/dashboard/event/1`);
    } else {
      router.push(`/dashboard/event/${eventId}`);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
        <div className="relative w-full md:w-64">
          <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-400" />
          <Input
            type="text"
            placeholder="Search events..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex flex-wrap gap-2 w-full md:w-auto">
          <Select value={provinceFilter} onValueChange={setProvinceFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All Provinces" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Provinces</SelectItem>
              {provinces
                .filter((p) => p !== 'all')
                .map((province) => (
                  <SelectItem key={province} value={province}>
                    {province}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>

          <Select value={locationFilter} onValueChange={setLocationFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All Locations" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Locations</SelectItem>
              {locations
                .filter((l) => l !== 'all')
                .map((location) => (
                  <SelectItem key={location} value={location}>
                    {location}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All Statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="Approved">Approved</SelectItem>
              <SelectItem value="Waiting for Approval">
                Waiting for Approval
              </SelectItem>
              <SelectItem value="Add Floor Plan">Add Floor Plan</SelectItem>
            </SelectContent>
          </Select>

          {activeFilterCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={resetFilters}
              className="flex items-center gap-1"
            >
              <XCircleIcon className="h-3 w-3" />
              Reset ({activeFilterCount})
            </Button>
          )}
        </div>
      </div>

      {/* Custom table with sticky header */}
      <div className="border rounded-md bg-white">
        <div className="overflow-auto max-h-[70vh] relative">
          {/* Sticky header */}
          <div className="sticky top-0 z-20 bg-slate-50 border-b">
            <div className="grid grid-cols-5 w-full">
              <div className="font-medium p-4">Event Name</div>
              <div className="font-medium p-4">Date</div>
              <div className="font-medium p-4">Location</div>
              <div className="font-medium p-4">Province</div>
              <div className="font-medium p-4">Floor Plan</div>
            </div>
          </div>

          {/* Table body */}
          <div>
            {filteredEvents.map((event, index) => (
              <div
                key={event.id}
                className={`grid grid-cols-5 w-full ${index % 2 === 1 ? 'bg-slate-50' : ''}`}
              >
                <div className="p-4 font-medium text-[#00646C]">
                  <button
                    className="text-left hover:underline focus:outline-none focus:underline"
                    onClick={() => handleViewEvent(event.id)}
                  >
                    {event.name}
                  </button>
                </div>
                <div className="p-4">
                  <div className="flex items-center gap-1">
                    <CalendarIcon className="h-3.5 w-3.5 text-slate-400" />
                    <span>
                      {formatDate(event.startDate)} -{' '}
                      {formatDate(event.endDate)}
                    </span>
                  </div>
                </div>
                <div className="p-4">
                  <div className="flex items-center gap-1">
                    <MapPinIcon className="h-3.5 w-3.5 text-slate-400" />
                    <span>{event.location}</span>
                  </div>
                </div>
                <div className="p-4">{event.province}</div>
                <div className="p-4">{getFloorPlanBadge(event.floorPlan)}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
