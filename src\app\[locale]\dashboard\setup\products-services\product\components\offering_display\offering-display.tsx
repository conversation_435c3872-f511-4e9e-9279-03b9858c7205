// components/OfferingDisplayOut.tsx
'use client';
import { Link } from '@/utils/navigation';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { PropertyOptionsDto } from '@/models/Offering';
interface OfferingDisplayProps {
  id: number;
  name?: string | null;
  groupId: number;
  categoryId: number;
  options?: PropertyOptionsDto[];
}

const OfferingDisplay: React.FC<OfferingDisplayProps> = ({
  id,
  name,
  groupId,
  categoryId,
  options,
}) => {
  return (
    <Accordion key={id} type="single" collapsible className="space-y-3">
      <AccordionItem value={name + id.toString()}>
        <AccordionTrigger
          className={`px-3 py-1 hover:bg-slate-50 hover:rounded-lg w-full`}
        >
          <div className="flex flex-row gap-4 items-center justify-between">
            <div className="flex items-center gap-1 flex-1 min-w-0">
              <Link
                href={`/dashboard/setup/products-services/product/${groupId}/category/${categoryId}/${id}`}
              >
                <span
                  className={`text-sm font-medium truncate flex items-center gap-1 hover:text-main hover:underline ${options && options.length === 0 ? 'text-gray-500' : 'text-gray-900'}`}
                >
                  {name}
                </span>
              </Link>
            </div>
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-4 pb-3">
          {options && options.length > 0 ? (
            options.map((o, index) => (
              <div
                key={o.id}
                className={`px-3 py-1 hover:bg-slate-50 hover:rounded-lg w-full`}
              >
                <div className="flex flex-row gap-4 items-center justify-between">
                  <div className="flex items-center gap-1 flex-1 min-w-0">
                    <Link
                      href={`/dashboard/setup/products-services/product/${groupId}/category/${categoryId}/${id}/property`}
                    >
                      <span
                        className={`text-sm font-medium truncate flex items-center gap-1 hover:text-main hover:underline`}
                      >
                        ({index + 1}) {o.name}
                        {/* {o.image && (
                          <div className="relative inline-block group">
                            <div className="absolute bottom left-full ml-2 transform -translate-y-1/2 hidden group-hover:block z-50 bg-white border border-gray-300 rounded shadow-md">
                              <img
                                src={
                                  o.image.startsWith('/images')
                                    ? o.image
                                    : '/images' + o.image
                                }
                                alt="preview"
                                className="max-w-[100px] max-h-[100px] object-cover rounded"
                              />
                            </div>
                            <ImageIcon className="w-4 h-4" />
                          </div>
                        )} */}
                        {/* &nbsp;- {o.code} */}
                      </span>
                    </Link>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-sm text-gray-500 italic">
              No options in this category.
            </div>
          )}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default OfferingDisplay;
