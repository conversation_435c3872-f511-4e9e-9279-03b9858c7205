'use client';

import { useQuery } from '@tanstack/react-query';
import { Edit2, Trash2 } from 'lucide-react';
import { FaPlus } from 'react-icons/fa';

import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import PropertyOptionQuery from '@/services/queries/PropertyOptionQuery'; // The new query service for property options
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import AddPropertyOptionModal from '../add_property_option_modal'; // Modal component for adding/editing property option
import { PropertyOption } from '@/models/PropertyOption';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';
import { getQueryClient } from '@/utils/query-client';

interface PropertyOptionsTableProps {
  propertyId: number;
}

export const PropertyOptionsTable = ({
  propertyId,
}: PropertyOptionsTableProps) => {
  // Fetch data using the PropertyOptionQuery
  const { data, isLoading } = useQuery({
    queryKey: ['PropertyOptions', { propertyId: propertyId }],
    queryFn: () => PropertyOptionQuery.getByPropertyId(propertyId),
    enabled: !!propertyId,
  });

  const columns = generateTableColumns<PropertyOption>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      code: { name: 'Code', type: 'text', sortable: true },
      displayOrder: { name: 'Display Order', type: 'text', sortable: true },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-2 justify-center">
              <Button
                size="sm"
                variant="secondary"
                iconName="EditIcon"
                onClick={() => {
                  modal(
                    <AddPropertyOptionModal
                      propertyOptionId={row.id}
                      propertyId={propertyId}
                    />,
                    {
                      ...DEFAULT_MODAL,
                      width: 'w-full',
                    },
                  ).open();
                }}
              ></Button>
              <Button
                variant="remove"
                size="sm"
                iconName="RemoveIcon"
                onClick={() => {
                  modal(
                    ({ close }) => (
                      <MutationConfirmModal
                        close={close}
                        title="Delete Option"
                        description={`Are you sure you want to delete option "${row.name}"?`}
                        mutateFn={() => PropertyOptionQuery.delete(row.id)}
                        mutationKey={[
                          ...PropertyOptionQuery.tags,
                          'options',
                          row.id,
                        ]}
                        onSuccess={async () => {
                          await getQueryClient().invalidateQueries({
                            queryKey: [
                              ...PropertyOptionQuery.tags,
                              'contacts',
                              row.id,
                            ],
                          });
                          await getQueryClient().invalidateQueries({
                            queryKey: [
                              'PropertyOptions',
                              { propertyId: Number(propertyId) },
                            ],
                          });
                        }}
                        variant="destructive"
                        confirmButtonText="Delete"
                        confirmIconName="DeleteIcon"
                        loadingIconName="LoadingIcon"
                      />
                    ),
                    DEFAULT_MODAL,
                  ).open();
                }}
              ></Button>
            </div>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<PropertyOption>({
    name: {
      name: 'Name',
      type: 'text',
    },
  });

  return (
    <DataTable
      columns={columns}
      filterFields={filters}
      data={data}
      isLoading={isLoading}
      controls={
        <div className="flex justify-end gap-2">
          <Button
            variant="main"
            onClick={() => {
              modal(<AddPropertyOptionModal propertyId={propertyId} />, {
                ...DEFAULT_MODAL,
                width: 'w-full',
              }).open();
            }}
          >
            <FaPlus />
            Add New Property Option
          </Button>
        </div>
      }
    />
  );
};

export default PropertyOptionsTable;
