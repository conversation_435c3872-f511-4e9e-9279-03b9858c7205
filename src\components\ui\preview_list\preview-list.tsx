import { ReactNode } from 'react';

import { cn } from '@/lib/utils';

import Header from '../header';
import { ScrollArea } from '../scroll-area';

interface IPreviewList {
  title: string;
  buttonNode?: ReactNode;
  children?: ReactNode;
  listHeight?: number | string;
  noBorder?: boolean;
}

function PreviewList({
  title,
  buttonNode,
  children,
  listHeight,
  noBorder,
}: IPreviewList) {
  return (
    <div
      className="preview-list flex flex-col w-full items-stretch self-stretch gap-10  "
      style={{
        border: !noBorder ? `1px solid  hsl(var(--border))` : undefined,
        padding: noBorder ? 0 : '20px 20px',
      }}
    >
      <Header title={title} buttonNode={buttonNode} />
      <ScrollArea
        className={cn('flex items-stretch')}
        style={{ height: listHeight }}
      >
        {children}
      </ScrollArea>
    </div>
  );
}

export default PreviewList;
