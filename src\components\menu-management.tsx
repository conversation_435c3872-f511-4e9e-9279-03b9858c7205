'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertTriangle,
  ArrowDown,
  ArrowUp,
  Bell,
  ChevronRight,
  Cog,
  CreditCard,
  Edit,
  FileSpreadsheet,
  FileText,
  Grip,
  List,
  MapPin,
  Menu,
  MoreHorizontal,
  Package,
  Percent,
  Plus,
  Save,
  Search,
  Settings,
  Trash2,
  UserCog,
  Users,
  Warehouse,
  X,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Define types for menu items
interface MenuItem {
  id: string;
  name: string;
  path: string;
  icon: string;
  parent: string | null;
  order: number;
  visible: boolean;
  roles: string[];
}

// Sample menu data
const initialMenuItems: MenuItem[] = [
  {
    id: 'system-notifications',
    name: 'System Notifications',
    path: '/setup',
    icon: 'Bell',
    parent: null,
    order: 1,
    visible: true,
    roles: ['admin', 'manager'],
  },
  {
    id: 'show-setup',
    name: 'Show Setup',
    path: '#',
    icon: 'Settings',
    parent: null,
    order: 2,
    visible: true,
    roles: ['admin', 'manager', 'staff'],
  },
  {
    id: 'list-of-shows',
    name: 'List of Shows',
    path: '/setup/list-of-shows',
    icon: 'List',
    parent: 'show-setup',
    order: 1,
    visible: true,
    roles: ['admin', 'manager', 'staff'],
  },
  {
    id: 'show-managers',
    name: 'Show Managers',
    path: '/setup/show-managers',
    icon: 'Users',
    parent: 'show-setup',
    order: 2,
    visible: true,
    roles: ['admin', 'manager'],
  },
  {
    id: 'show-locations',
    name: 'Show Locations',
    path: '/setup/show-locations',
    icon: 'MapPin',
    parent: 'show-setup',
    order: 3,
    visible: true,
    roles: ['admin', 'manager'],
  },
  {
    id: 'master-setup',
    name: 'Master Setup',
    path: '#',
    icon: 'Settings',
    parent: null,
    order: 3,
    visible: true,
    roles: ['admin'],
  },
  {
    id: 'reports',
    name: 'Reports',
    path: '#',
    icon: 'FileSpreadsheet',
    parent: null,
    order: 4,
    visible: true,
    roles: ['admin', 'manager'],
  },
  {
    id: 'system-setup',
    name: 'System Setup',
    path: '/setup/system-setup',
    icon: 'Cog',
    parent: null,
    order: 5,
    visible: true,
    roles: ['admin'],
  },
  {
    id: 'menu-management',
    name: 'Menu Management',
    path: '/setup/menu-management',
    icon: 'Menu',
    parent: null,
    order: 6,
    visible: true,
    roles: ['admin'],
  },
  {
    id: 'user-management',
    name: 'User Management',
    path: '/setup/user-management',
    icon: 'UserCog',
    parent: null,
    order: 7,
    visible: true,
    roles: ['admin'],
  },
];

// Available icons
const availableIcons = [
  'Bell',
  'Settings',
  'List',
  'Users',
  'MapPin',
  'Package',
  'Warehouse',
  'FileText',
  'FileSpreadsheet',
  'CreditCard',
  'AlertTriangle',
  'Percent',
  'Cog',
  'UserCog',
  'Menu',
  'Home',
  'Calendar',
  'Search',
  'Filter',
  'Plus',
  'Edit',
  'Trash2',
  'Eye',
  'EyeOff',
];

// Available roles
const availableRoles = ['admin', 'manager', 'staff', 'exhibitor', 'vendor'];

export default function MenuManagement() {
  const [menuItems, setMenuItems] = useState<MenuItem[]>(initialMenuItems);
  const [editingItem, setEditingItem] = useState<MenuItem | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Get parent menu items (those with no parent)
  const parentMenuItems = menuItems
    .filter((item) => item.parent === null)
    .sort((a, b) => a.order - b.order);

  // Get filtered menu items based on active tab and search term
  const filteredMenuItems = menuItems.filter((item) => {
    const searchMatch =
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.path.toLowerCase().includes(searchTerm.toLowerCase());

    if (activeTab === 'all') return searchMatch;
    if (activeTab === 'parent') return item.parent === null && searchMatch;
    if (activeTab === 'child') return item.parent !== null && searchMatch;
    if (activeTab === 'hidden') return !item.visible && searchMatch;

    return searchMatch;
  });

  // Function to get child menu items for a parent
  const getChildMenuItems = (parentId: string) => {
    return menuItems
      .filter((item) => item.parent === parentId)
      .sort((a, b) => a.order - b.order);
  };

  // Function to handle visibility toggle
  const handleVisibilityToggle = (id: string) => {
    setMenuItems(
      menuItems.map((item) =>
        item.id === id ? { ...item, visible: !item.visible } : item,
      ),
    );
  };

  // Function to handle item deletion
  const handleDeleteItem = (id: string) => {
    // Check if item has children
    const hasChildren = menuItems.some((item) => item.parent === id);

    if (hasChildren) {
      alert(
        'Cannot delete menu item with children. Please delete or reassign children first.',
      );
      return;
    }

    setMenuItems(menuItems.filter((item) => item.id !== id));
  };

  // Function to handle item editing
  const handleEditItem = (item: MenuItem) => {
    setEditingItem({ ...item });
    setIsDialogOpen(true);
  };

  // Function to handle adding a new item
  const handleAddItem = (parentId: string | null = null) => {
    const newItem: MenuItem = {
      id: `menu-item-${Date.now()}`,
      name: '',
      path: '',
      icon: 'Menu',
      parent: parentId,
      order:
        parentId === null
          ? parentMenuItems.length + 1
          : getChildMenuItems(parentId).length + 1,
      visible: true,
      roles: ['admin'],
    };

    setEditingItem(newItem);
    setIsDialogOpen(true);
  };

  // Function to save edited or new item
  const handleSaveItem = () => {
    if (!editingItem) return;

    if (!editingItem.name.trim()) {
      alert('Menu item name is required');
      return;
    }

    if (editingItem.id.includes('menu-item-')) {
      // New item
      setMenuItems([...menuItems, editingItem]);
    } else {
      // Existing item
      setMenuItems(
        menuItems.map((item) =>
          item.id === editingItem.id ? editingItem : item,
        ),
      );
    }

    setIsDialogOpen(false);
    setEditingItem(null);
  };

  // Function to move item up in order
  const handleMoveUp = (id: string) => {
    const itemToMove = menuItems.find((item) => item.id === id);
    if (!itemToMove) return;

    const siblings = menuItems
      .filter((item) => item.parent === itemToMove.parent)
      .sort((a, b) => a.order - b.order);

    const itemIndex = siblings.findIndex((item) => item.id === id);
    if (itemIndex <= 0) return; // Already at the top

    const updatedItems = menuItems.map((item) => {
      if (item.id === id) {
        return { ...item, order: item.order - 1 };
      }
      if (item.id === siblings[itemIndex - 1].id) {
        return { ...item, order: item.order + 1 };
      }
      return item;
    });

    setMenuItems(updatedItems);
  };

  // Function to move item down in order
  const handleMoveDown = (id: string) => {
    const itemToMove = menuItems.find((item) => item.id === id);
    if (!itemToMove) return;

    const siblings = menuItems
      .filter((item) => item.parent === itemToMove.parent)
      .sort((a, b) => a.order - b.order);

    const itemIndex = siblings.findIndex((item) => item.id === id);
    if (itemIndex >= siblings.length - 1) return; // Already at the bottom

    const updatedItems = menuItems.map((item) => {
      if (item.id === id) {
        return { ...item, order: item.order + 1 };
      }
      if (item.id === siblings[itemIndex + 1].id) {
        return { ...item, order: item.order - 1 };
      }
      return item;
    });

    setMenuItems(updatedItems);
  };

  // Render icon for menu item
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case 'Bell':
        return <Bell className="h-4 w-4" />;
      case 'Settings':
        return <Settings className="h-4 w-4" />;
      case 'List':
        return <List className="h-4 w-4" />;
      case 'Users':
        return <Users className="h-4 w-4" />;
      case 'MapPin':
        return <MapPin className="h-4 w-4" />;
      case 'Package':
        return <Package className="h-4 w-4" />;
      case 'Warehouse':
        return <Warehouse className="h-4 w-4" />;
      case 'FileText':
        return <FileText className="h-4 w-4" />;
      case 'FileSpreadsheet':
        return <FileSpreadsheet className="h-4 w-4" />;
      case 'CreditCard':
        return <CreditCard className="h-4 w-4" />;
      case 'AlertTriangle':
        return <AlertTriangle className="h-4 w-4" />;
      case 'Percent':
        return <Percent className="h-4 w-4" />;
      case 'Cog':
        return <Cog className="h-4 w-4" />;
      case 'UserCog':
        return <UserCog className="h-4 w-4" />;
      case 'Menu':
        return <Menu className="h-4 w-4" />;
      default:
        return <Menu className="h-4 w-4" />;
    }
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-slate-800">Menu Management</h1>
        <Button
          className="bg-[#00646C] hover:bg-[#00646C]/90"
          onClick={() => handleAddItem()}
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Menu Item
        </Button>
      </div>

      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle>Menu Structure</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between mb-4">
            <div className="relative w-full md:w-64">
              <Input
                type="text"
                placeholder="Search menu items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-400" />
            </div>

            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full md:w-auto"
            >
              <TabsList>
                <TabsTrigger value="all">All Items</TabsTrigger>
                <TabsTrigger value="parent">Parent Items</TabsTrigger>
                <TabsTrigger value="child">Child Items</TabsTrigger>
                <TabsTrigger value="hidden">Hidden Items</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow className="bg-slate-50">
                  <TableHead className="w-10"></TableHead>
                  <TableHead className="w-10"></TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Path</TableHead>
                  <TableHead>Parent</TableHead>
                  <TableHead>Order</TableHead>
                  <TableHead>Visible</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMenuItems.map((item, index) => {
                  const parentItem = item.parent
                    ? menuItems.find((mi) => mi.id === item.parent)
                    : null;

                  return (
                    <TableRow
                      key={item.id}
                      className={index % 2 === 1 ? 'bg-slate-50' : ''}
                    >
                      <TableCell>
                        <div className="flex items-center justify-center">
                          <Grip className="h-4 w-4 text-slate-400" />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center text-slate-500">
                          {renderIcon(item.icon)}
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        <div className="flex items-center">
                          {item.parent && (
                            <ChevronRight className="h-4 w-4 mr-1 text-slate-400" />
                          )}
                          {item.name}
                        </div>
                      </TableCell>
                      <TableCell className="text-slate-600">
                        {item.path}
                      </TableCell>
                      <TableCell>
                        {parentItem ? parentItem.name : '-'}
                      </TableCell>
                      <TableCell>{item.order}</TableCell>
                      <TableCell>
                        <Switch
                          checked={item.visible}
                          onCheckedChange={() =>
                            handleVisibilityToggle(item.id)
                          }
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleMoveUp(item.id)}
                            disabled={
                              item.parent === null
                                ? item.order <= 1
                                : item.order <= 1
                            }
                          >
                            <ArrowUp className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleMoveDown(item.id)}
                            disabled={
                              item.parent === null
                                ? item.order >= parentMenuItems.length
                                : item.parent !== null &&
                                  item.order >=
                                    getChildMenuItems(item.parent).length
                            }
                          >
                            <ArrowDown className="h-4 w-4" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => handleEditItem(item)}
                              >
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                              {item.parent === null && (
                                <DropdownMenuItem
                                  onClick={() => handleAddItem(item.id)}
                                >
                                  <Plus className="h-4 w-4 mr-2" />
                                  Add Child Item
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleDeleteItem(item.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
                {filteredMenuItems.length === 0 && (
                  <TableRow>
                    <TableCell
                      colSpan={8}
                      className="text-center py-4 text-slate-500"
                    >
                      No menu items found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>
              {editingItem && editingItem.id.includes('menu-item-')
                ? 'Add Menu Item'
                : 'Edit Menu Item'}
            </DialogTitle>
            <DialogDescription>
              Configure the menu item details. Click save when you're done.
            </DialogDescription>
          </DialogHeader>

          {editingItem && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={editingItem.name}
                  onChange={(e) =>
                    setEditingItem({ ...editingItem, name: e.target.value })
                  }
                  className="col-span-3"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="path" className="text-right">
                  Path
                </Label>
                <Input
                  id="path"
                  value={editingItem.path}
                  onChange={(e) =>
                    setEditingItem({ ...editingItem, path: e.target.value })
                  }
                  className="col-span-3"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="icon" className="text-right">
                  Icon
                </Label>
                <Select
                  value={editingItem.icon}
                  onValueChange={(value) =>
                    setEditingItem({ ...editingItem, icon: value })
                  }
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select an icon" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableIcons.map((icon) => (
                      <SelectItem key={icon} value={icon}>
                        <div className="flex items-center">
                          {renderIcon(icon)}
                          <span className="ml-2">{icon}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="parent" className="text-right">
                  Parent
                </Label>
                <Select
                  value={editingItem.parent || 'null'}
                  onValueChange={(value) =>
                    setEditingItem({
                      ...editingItem,
                      parent: value === 'null' ? null : value,
                      order:
                        value === 'null'
                          ? parentMenuItems.length + 1
                          : getChildMenuItems(value).length + 1,
                    })
                  }
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="No parent (top level)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="null">No parent (top level)</SelectItem>
                    {parentMenuItems
                      .filter((item) => item.id !== editingItem.id) // Can't be its own parent
                      .map((item) => (
                        <SelectItem key={item.id} value={item.id}>
                          {item.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="order" className="text-right">
                  Order
                </Label>
                <Input
                  id="order"
                  type="number"
                  min="1"
                  value={editingItem.order}
                  onChange={(e) =>
                    setEditingItem({
                      ...editingItem,
                      order: Number.parseInt(e.target.value) || 1,
                    })
                  }
                  className="col-span-3"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="visible" className="text-right">
                  Visible
                </Label>
                <div className="flex items-center space-x-2 col-span-3">
                  <Switch
                    id="visible"
                    checked={editingItem.visible}
                    onCheckedChange={(checked) =>
                      setEditingItem({ ...editingItem, visible: checked })
                    }
                  />
                  <Label htmlFor="visible" className="font-normal">
                    {editingItem.visible ? 'Visible' : 'Hidden'}
                  </Label>
                </div>
              </div>

              <div className="grid grid-cols-4 items-start gap-4">
                <Label className="text-right pt-2">Roles</Label>
                <div className="col-span-3 space-y-2">
                  {availableRoles.map((role) => (
                    <div key={role} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`role-${role}`}
                        checked={editingItem.roles.includes(role)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setEditingItem({
                              ...editingItem,
                              roles: [...editingItem.roles, role],
                            });
                          } else {
                            setEditingItem({
                              ...editingItem,
                              roles: editingItem.roles.filter(
                                (r) => r !== role,
                              ),
                            });
                          }
                        }}
                        className="h-4 w-4 rounded border-slate-300 text-[#00646C] focus:ring-[#00646C]"
                      />
                      <Label
                        htmlFor={`role-${role}`}
                        className="font-normal capitalize"
                      >
                        {role}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button
              className="bg-[#00646C] hover:bg-[#00646C]/90"
              onClick={handleSaveItem}
            >
              <Save className="mr-2 h-4 w-4" />
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
