'use client';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface KeyboardShortcutsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function KeyboardShortcutsDialog({
  open,
  onOpenChange,
}: KeyboardShortcutsDialogProps) {
  const shortcuts = {
    navigation: [
      { keys: ['G', 'D'], description: 'Go to Dashboard' },
      { keys: ['G', 'U'], description: 'Go to Users' },
      { keys: ['G', 'P'], description: 'Go to Payments' },
      { keys: ['G', 'S'], description: 'Go to Settings' },
    ],
    roles: [
      { keys: ['R', 'F'], description: 'Switch to Finance Role Center' },
      { keys: ['R', 'S'], description: 'Switch to Sales Role Center' },
      { keys: ['R', 'I'], description: 'Switch to Inventory Role Center' },
      { keys: ['R', 'A'], description: 'Switch to Administrator Role Center' },
    ],
    actions: [
      { keys: ['⌘', 'K'], description: 'Open command palette' },
      { keys: ['⌘', 'B'], description: 'Toggle FactBox' },
      { keys: ['⌘', 'N'], description: 'Create new item' },
      { keys: ['⌘', 'S'], description: 'Save current work' },
      { keys: ['Shift', '?'], description: 'Show keyboard shortcuts' },
    ],
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-slate-800">
            Keyboard Shortcuts
          </DialogTitle>
          <DialogDescription>
            Use these keyboard shortcuts to work more efficiently.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="navigation" className="mt-4">
          <TabsList className="bg-slate-100">
            <TabsTrigger
              value="navigation"
              className="data-[state=active]:bg-white"
            >
              Navigation
            </TabsTrigger>
            <TabsTrigger value="roles" className="data-[state=active]:bg-white">
              Role Centers
            </TabsTrigger>
            <TabsTrigger
              value="actions"
              className="data-[state=active]:bg-white"
            >
              Actions
            </TabsTrigger>
          </TabsList>

          {Object.entries(shortcuts).map(([category, shortcutList]) => (
            <TabsContent key={category} value={category} className="mt-4">
              <div className="space-y-2">
                {shortcutList.map((shortcut, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between py-2"
                  >
                    <span className="text-sm text-slate-700">
                      {shortcut.description}
                    </span>
                    <div className="flex items-center gap-1">
                      {shortcut.keys.map((key, keyIndex) => (
                        <kbd
                          key={keyIndex}
                          className="flex h-6 min-w-6 items-center justify-center rounded border border-slate-200 bg-slate-50 px-1.5 text-xs font-medium text-slate-700"
                        >
                          {key}
                        </kbd>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
