{"name": "cn-cms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "turbopack": "next dev --turbopack", "typecheck": "tsc --noEmit", "build": "next build", "start": "next start", "lint": "eslint ./src --color", "fix": "next lint --fix"}, "dependencies": {"@ariakit/react": "^0.4.10", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.3.4", "@popperjs/core": "^2.11.8", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-toolbar": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@tanstack/react-query": "^5.28.8", "@tanstack/react-query-devtools": "^5.28.8", "@tanstack/react-query-next-experimental": "^5.28.8", "@tanstack/react-table": "^8.20.5", "@types/jsonwebtoken": "^9.0.6", "@types/nprogress": "^0.2.3", "awesome-phonenumber": "^6.9.0", "axios": "^1.7.7", "chalk": "^5.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "cmdk": "^1.0.0", "color-convert": "^2.0.1", "country-flag-icons": "^1.5.13", "date-fns": "^3.6.0", "embla-carousel": "^8.3.0", "embla-carousel-autoplay": "^8.3.0", "embla-carousel-fade": "^8.3.0", "embla-carousel-react": "^8.3.0", "embla-carousel-wheel-gestures": "^8.0.1", "framer-motion": "^12.6.3", "immer": "^10.0.4", "jose": "^5.2.4", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.456.0", "nanoid": "^5.0.7", "next": "^15.2.5", "next-intl": "^4.0.2", "next-themes": "^0.2.1", "next-video": "^1.1.4", "nprogress": "^0.2.0", "react": "^18.3.1", "react-awesome-reveal": "^4.3.1", "react-colorful": "^5.6.1", "react-datepicker": "^6.9.0", "react-day-picker": "^8.10.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-easy-crop": "^5.0.8", "react-google-recaptcha-v3": "^1.10.1", "react-hook-form": "^7.53.2", "react-icons": "^5.0.1", "react-lite-youtube-embed": "^2.4.0", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.3", "react-responsive": "^10.0.0", "react-tweet": "^3.2.1", "reactjs-tiptap-editor": "0.1.7", "sass": "^1.71.0", "schema-dts": "^1.1.5", "slate": "^0.103.0", "slate-history": "^0.100.0", "slate-hyperscript": "^0.100.0", "slate-react": "^0.106.0", "sonner": "^1.4.41", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "typescript-cookie": "^1.0.6", "xlsx": "^0.18.5", "zod": "^3.22.4", "zustand": "^4.5.2"}, "devDependencies": {"@chromatic-com/storybook": "^1.2.25", "@emotion/babel-plugin": "^11.10.5", "@emotion/eslint-plugin": "^11.10.0", "@next/bundle-analyzer": "^15.3.1", "@storybook/addon-onboarding": "^8.0.4", "@svgr/webpack": "^8.1.0", "@types/color-convert": "^2.0.4", "@types/debug": "^4.1.12", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18", "@types/scheduler": "^0.16.8", "autoprefixer": "^10.0.1", "babel-plugin-react-compiler": "^19.0.0-beta-ebf51a3-20250411", "eslint": "^8", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-next": "14.1.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.10.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.31.11", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.8.0", "eslint-plugin-unused-imports": "^3.1.0", "postcss": "^8", "postcss-import": "^16.1.0", "prettier": "^3.2.5", "prettier-eslint": "^16.3.0", "tailwindcss": "^3.3.0", "typescript": "^5"}}