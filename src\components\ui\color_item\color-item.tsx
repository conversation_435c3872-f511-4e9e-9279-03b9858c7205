import { Trash } from 'lucide-react';

import { ImageData } from '@/models/ImageData';

import AppImage from '../app_image';
import { Button } from '../button';

interface IColorItem {
  name: string;
  code?: string;
  colorCode?: string;
  description?: string;
  image?: ImageData;
  checked?: boolean;
  onRemove?: () => void;
}

function ColorItem({ name, code, image, colorCode, onRemove }: IColorItem) {
  return (
    <div className="flex flex-col items-center justify-center gap-3 p-4 aspect-square max-w-[180px] hover:brightness-150 relative  ">
      <div
        className="w-[80px] h-[80px] relative outline-1 outline outline-[rgba(0,0,0,0.2)]"
        style={{ backgroundColor: colorCode ?? '#00000' }}
      >
        {image && <AppImage fill={true} image={image} objectFit="cover" />}
      </div>
      <div className="flex flex-col gap-1 items-center">
        <span className="text-lg ">{name}</span>
        {code && <span className="text-lg ">({code})</span>}
        {/* {description ?? (
          <span className="text-lg text-center  ">{description}</span>
        )} */}
      </div>
      {onRemove && (
        <Button
          variant={'destructive'}
          size={'sm'}
          className="absolute top-4 right-4 rounded-none"
          onClick={onRemove}
        >
          <Trash className="size-4 " />
        </Button>
      )}
    </div>
  );
}

export default ColorItem;
