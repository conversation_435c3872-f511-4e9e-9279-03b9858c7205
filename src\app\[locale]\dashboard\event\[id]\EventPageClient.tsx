'use client';
import { Calendar, Clock, Download, FileText, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { EventSidebar } from '@/components/ui/event-sidebar';

// Mock data for the TEST SANDY event
const eventData = {
  id: '1',
  name: 'TEST SANDY',
  location: 'Vancouver Convention Centre East',
  province: 'BRITISH COLUMBIA',
  startDate: '2006-11-30',
  endDate: '2006-12-04',
  floorPlan: 'Approved',
  orderDeadline: '2006-10-27',
  surchargePercentage: 25,
  documents: ['Floor Plan', 'Show Schedule'],
  schedule: [
    {
      details: 'Show Opening Breakfast Time',
      date: '2015-03-19',
      startTime: '8:15 AM',
      endTime: '8:30 AM',
      status: 'To be confirmed',
    },
    {
      details: 'Decoration All Out',
      date: '2009-09-03',
      startTime: '',
      endTime: '',
      status: 'To be confirmed',
    },
    {
      details: 'Note: test comment',
      date: '2009-09-03',
      startTime: '8:00 AM',
      endTime: '9:00 AM',
      status: 'To be confirmed',
    },
    {
      details: 'Advance Freight Move-in',
      date: '2009-09-03',
      startTime: 'N/A',
      endTime: '1:00 PM',
      status: 'To be confirmed',
    },
    {
      details: 'Advance Freight Move-in',
      date: '2009-09-03',
      startTime: 'N/A',
      endTime: '3:30 PM',
      status: 'To be confirmed',
    },
    {
      details: 'Freight All Out',
      date: '2009-09-03',
      startTime: '5:00 PM',
      endTime: '12:00 PM',
      status: 'To be confirmed',
    },
    {
      details: 'Exhibitor Early Move-in',
      date: '2013-12-01',
      startTime: '7:30 AM',
      endTime: '10:30 AM',
      status: 'To be confirmed',
    },
    {
      details: 'Note: 123',
      date: '2013-12-05',
      startTime: '7:30 AM',
      endTime: '11:15 AM',
      status: 'To be confirmed',
    },
    {
      details: 'Exhibitor Move-in',
      date: '2013-12-06',
      startTime: '8:45 AM',
      endTime: '10:00 AM',
      status: 'To be confirmed',
    },
    {
      details: 'Exhibitor Move-out',
      date: '2013-12-10',
      startTime: '4:45 AM',
      endTime: '8:45 PM',
      status: 'To be confirmed',
    },
    {
      details: 'Exhibitor Move-out',
      date: '2013-12-11',
      startTime: '7:00 AM',
      endTime: '9:00 AM',
      status: 'To be confirmed',
    },
  ],
  workorderLocations: [
    {
      name: 'Exhibition Floor',
      details: '',
    },
    {
      name: 'Food court - Food Court Area',
      details: '',
    },
    {
      name: 'Hall B - Archway',
      details: '',
    },
    {
      name: 'Malopan Diner',
      details: 'This is a test from Norm, July 19, 2019.',
    },
    {
      name: 'Reg Area - Registration',
      details: '',
    },
    {
      name: 'Test Mert',
      details: 'testtttt details',
    },
  ],
  showManagement: {
    manager: {
      name: 'Nadia Bakka',
      company: 'The CIM, Metallurgy and Petroleum',
      email: '<EMAIL>',
      tel: '************ ext 1360',
    },
    billingContact: {
      name: 'Alex Nelyubin',
      company: 'Traders Forum',
      email: '<EMAIL>',
      tel: '************',
    },
  },
  showDetails: {
    numberOfBooths: '122',
    boothNumbers: '254',
    suppliedInEachBooth: '',
    chairs: '',
    signs: '',
    sizeOfBooths: '',
    booths: 'split level',
    drapeColourBackWall: '',
    drapeColourSideWall: '',
    tables: '',
    tablesColour: '',
    carpetColour: '',
    aisleCarpeting: 'No',
    aisleCarpetingColour: '',
  },
};

// Format date for display
function formatDate(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric',
  });
}

// Format date range for display
function formatDateRange(startDate: string, endDate: string) {
  const start = new Date(startDate);
  const end = new Date(endDate);

  const startMonth = start.toLocaleDateString('en-US', { month: 'long' });
  const endMonth = end.toLocaleDateString('en-US', { month: 'long' });

  if (startMonth === endMonth) {
    return `${startMonth} ${start.getDate()} - ${end.getDate()}, ${end.getFullYear()}`;
  } else {
    return `${startMonth} ${start.getDate()} - ${endMonth} ${end.getDate()}, ${end.getFullYear()}`;
  }
}

export default function EventPageClient({
  params,
}: {
  params: { id: string };
}) {
  // In a real app, you would fetch the event data based on the ID
  // For this demo, we'll just use the mock data if the ID matches
  // Accept any ID for now since we're only showing TEST SANDY data
  // In a real app, you would fetch the event data based on the ID
  console.log('Event ID:', params.id);
  // For demo purposes, we'll show the TEST SANDY data regardless of ID

  const currentDate = new Date();
  const formattedCurrentDate = currentDate.toLocaleDateString('en-US', {
    weekday: 'long',
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  });

  const formattedCurrentTime = currentDate.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  });

  return (
    <div className="container mx-auto py-8 px-4">
      {/* <HorizontalMenu /> */}

      <div className="mb-6 flex items-center justify-between">
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => window.history.back()}>
            Back to Events
          </Button>
          <Button
            className="bg-[#00646C] hover:bg-[#00646C]/90 text-white"
            onClick={() =>
              (window.location.href = `/dashboard/event/${params.id}/products`)
            }
          >
            Order Online
          </Button>
        </div>
      </div>

      {/* Event Information section with gradient - Full width, single row */}
      <div className="mb-6 rounded-lg overflow-hidden shadow-md">
        <div className="bg-gradient-to-r from-[#00646C] to-[#00646C]/70 text-white p-4">
          <h2 className="text-xl font-semibold">Event Information</h2>
        </div>
        <div className="bg-white p-6 border border-t-0 border-slate-200 rounded-b-lg">
          <div className="flex flex-wrap md:flex-nowrap items-center gap-8">
            <div className="w-full md:w-1/3">
              <h3 className="font-medium text-slate-800 mb-2">Event Name</h3>
              <p className="text-lg">{eventData.name}</p>
            </div>
            <div className="w-full md:w-1/3">
              <h3 className="font-medium text-slate-800 mb-2">Location</h3>
              <div className="flex items-center gap-2">
                <MapPin className="h-5 w-5 text-[#00646C]" />
                <span className="text-lg">{eventData.location}</span>
              </div>
            </div>
            <div className="w-full md:w-1/3">
              <h3 className="font-medium text-slate-800 mb-2">Event Dates</h3>
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-[#00646C]" />
                <span className="text-lg">
                  {formatDateRange(eventData.startDate, eventData.endDate)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-6">
        <div className="w-full md:w-64 mb-6 md:mb-0">
          <EventSidebar eventId={params.id} activeItem="SHOW MANAGEMENT" />
        </div>

        <div className="flex-1">
          {/* First row - Show Management and Notices */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Show Management</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium text-slate-800 mb-2">Manager</h3>
                    <div className="space-y-1 text-sm">
                      <p className="font-medium">
                        Name: {eventData.showManagement.manager.name}
                      </p>
                      <p>Company: {eventData.showManagement.manager.company}</p>
                      <p>Email: {eventData.showManagement.manager.email}</p>
                      <p>Tel: {eventData.showManagement.manager.tel}</p>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium text-slate-800 mb-2">
                      Billing Contact
                    </h3>
                    <div className="space-y-1 text-sm">
                      <p className="font-medium">
                        Name: {eventData.showManagement.billingContact.name}
                      </p>
                      <p>
                        Company:{' '}
                        {eventData.showManagement.billingContact.company}
                      </p>
                      <p>
                        Email: {eventData.showManagement.billingContact.email}
                      </p>
                      <p>Tel: {eventData.showManagement.billingContact.tel}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Notices</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-3 bg-green-50 text-green-700 rounded-md">
                    <p className="font-medium">Online Ordering is now Open!</p>
                  </div>
                  <div>
                    <p>
                      <span className="font-medium">Order Deadline Date:</span>{' '}
                      Friday, Oct 27, 2006
                    </p>
                    <p className="text-sm text-slate-500">
                      (After this date a {eventData.surchargePercentage}%
                      surcharge will be added)
                    </p>
                  </div>
                  <div className="flex items-center gap-1 text-slate-500">
                    <Clock className="h-4 w-4" />
                    <span>
                      System time now: {formattedCurrentDate}{' '}
                      {formattedCurrentTime}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Second row - Workorder Locations and Show Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Workorder Locations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {eventData.workorderLocations.map((location, index) => (
                    <div key={index} className="border rounded-md p-3">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-slate-800">
                          {location.name}
                        </h3>
                      </div>
                      {location.details && (
                        <p className="text-sm text-slate-600">
                          {location.details}
                        </p>
                      )}
                      <div className="flex gap-2 mt-2">
                        <Button variant="outline" size="sm" className="w-full">
                          Modify
                        </Button>
                        <Button variant="outline" size="sm" className="w-full">
                          Subdivisions
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Show Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="flex justify-between col-span-2">
                    <span className="font-medium">Number of Booths:</span>
                    <span>{eventData.showDetails.numberOfBooths}</span>
                  </div>
                  <div className="flex justify-between col-span-2">
                    <span className="font-medium">Booth Numbers:</span>
                    <span>{eventData.showDetails.boothNumbers}</span>
                  </div>
                  <div className="flex justify-between col-span-2">
                    <span className="font-medium">Booths:</span>
                    <span>{eventData.showDetails.booths}</span>
                  </div>
                  <div className="flex justify-between col-span-1">
                    <span className="font-medium">Chairs:</span>
                    <span>{eventData.showDetails.chairs || '—'}</span>
                  </div>
                  <div className="flex justify-between col-span-1">
                    <span className="font-medium">Signs:</span>
                    <span>{eventData.showDetails.signs || '—'}</span>
                  </div>
                  <div className="flex justify-between col-span-1">
                    <span className="font-medium">Tables:</span>
                    <span>{eventData.showDetails.tables || '—'}</span>
                  </div>
                  <div className="flex justify-between col-span-1">
                    <span className="font-medium">Tables Color:</span>
                    <span>{eventData.showDetails.tablesColour || '—'}</span>
                  </div>
                  <div className="flex justify-between col-span-1">
                    <span className="font-medium">Drape Back Wall:</span>
                    <span>
                      {eventData.showDetails.drapeColourBackWall || '—'}
                    </span>
                  </div>
                  <div className="flex justify-between col-span-1">
                    <span className="font-medium">Drape Side Wall:</span>
                    <span>
                      {eventData.showDetails.drapeColourSideWall || '—'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Third row - Documents */}
          <div className="mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Show Documents Available:</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {eventData.documents.map((document) => (
                    <Button
                      key={document}
                      variant="outline"
                      className="w-full justify-start"
                    >
                      <FileText className="mr-2 h-4 w-4" />
                      {document}
                      <Download className="ml-auto h-4 w-4" />
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Fourth row - Schedule */}
          <div>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Show Schedule</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-slate-50">
                        <TableHead className="font-medium">Details</TableHead>
                        <TableHead className="font-medium">Date</TableHead>
                        <TableHead className="font-medium">
                          Start Time
                        </TableHead>
                        <TableHead className="font-medium">End Time</TableHead>
                        <TableHead className="font-medium">Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {eventData.schedule.map((item, index) => (
                        <TableRow
                          key={index}
                          className={index % 2 === 1 ? 'bg-slate-50' : ''}
                        >
                          <TableCell className="font-medium">
                            {item.details}
                          </TableCell>
                          <TableCell>{formatDate(item.date)}</TableCell>
                          <TableCell>{item.startTime}</TableCell>
                          <TableCell>{item.endTime}</TableCell>
                          <TableCell>
                            <span className="text-[#00646C] font-medium">
                              {item.status}
                            </span>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
