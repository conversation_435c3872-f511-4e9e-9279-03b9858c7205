import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { getQueryClient } from '@/utils/query-client';
import CategoryQuery from '@/services/queries/CategoryQuery';
import AddOnSection from '../component/add_on_section';

export default async function Page({
  params,
}: {
  params: Promise<{
    groupId: string;
    categoryId: string;
    id: string;
  }>;
}) {
  try {
    const { groupId, categoryId, id } = await params;

    const client = getQueryClient();

    if (id !== 'add') {
      if (Number.isNaN(Number(id))) throw new Error('Invalid property id');
      await client.prefetchQuery({
        queryKey: ['Addon'],
        queryFn: () => CategoryQuery.getAllAddOn(),
      });
    }

    return (
      <div className="space-y-4 px-2">
        <HydrationBoundary state={dehydrate(client)}>
          <AddOnSection id={Number(id!)} />
        </HydrationBoundary>
      </div>
    );
  } catch (error) {
    redirect('/dashboard/setup/products-services/product/add');
  }
}
