import Link from 'next/link';
import Image from 'next/image';
import { FileQuestion, Home, ArrowRight, Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { Inter } from 'next/font/google';

const inter = Inter({ subsets: ['latin'] });

export default function NotFound() {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(inter.className, 'flex flex-col min-h-screen')}>
        {/* Header */}
        <header className="border-b bg-gradient-to-r from-[#00646C] to-[#0097a7] shadow-md">
          <div className="container flex h-16 items-center justify-between px-4 md:px-6">
            <div className="flex items-center gap-4">
              <Link href="/" className="flex items-center gap-2">
                <Image
                  src="/gss-logo.svg"
                  alt="Goodkey Show Services Logo"
                  width={120}
                  height={65}
                  className="h-16 w-auto  " // Make logo white
                />
              </Link>
            </div>
            <nav className="hidden md:flex items-center gap-6">
              <Link
                href="/"
                className="text-sm font-medium text-white hover:text-yellow-200 transition-colors"
              >
                Home
              </Link>
              <Link
                href="/dashboard"
                className="text-sm font-medium text-white hover:text-yellow-200 transition-colors"
              >
                Dashboard
              </Link>
            </nav>
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="icon"
                className="text-white hover:text-yellow-200"
              >
                <Menu className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 flex flex-col container items-center justify-center py-8 px-4 md:px-6 bg-gradient-to-b from-blue-50 to-teal-50">
          <div className="flex flex-col gap-5 w-full mb-8">
            <h1 className="text-5xl py-4 font-bold text-center bg-gradient-to-r from-[#00646C] to-purple-600 text-transparent bg-clip-text">
              Page Under Construction
            </h1>
          </div>

          <Card className="mx-auto max-w-2xl mt-8 border-[#00646C]/20 shadow-lg overflow-hidden">
            <CardHeader className="text-center bg-gradient-to-b from-blue-50 to-white">
              <div className="flex justify-center mb-4">
                <div className="p-4 rounded-full bg-gradient-to-br from-[#00646C] to-teal-400 shadow-md">
                  <FileQuestion className="h-16 w-16 text-white" />
                </div>
              </div>
              <CardTitle className="text-2xl bg-gradient-to-r from-[#00646C] to-teal-500 text-transparent bg-clip-text">
                This page is under construction
              </CardTitle>
              <CardDescription className="text-gray-600 font-medium">
                The page you're looking for is being developed and will be
                available soon.
              </CardDescription>
            </CardHeader>
            <CardContent className="bg-white">
              <div className="flex flex-col items-center space-y-4">
                <p className="text-center text-gray-600">
                  We're working on adding this feature to the system. In the
                  meantime, you can navigate to other sections of the
                  application.
                </p>

                <div className="flex flex-col sm:flex-row gap-4 mt-6">
                  <Button
                    asChild
                    variant="default"
                    className="bg-gradient-to-r from-[#00646C] to-teal-500 hover:from-teal-600 hover:to-[#00646C] transition-all duration-300 shadow-md"
                  >
                    <Link href="/">
                      <Home className="mr-2 h-4 w-4" />
                      Return to Home
                    </Link>
                  </Button>
                  <Button
                    asChild
                    variant="outline"
                    className="border-teal-400 text-teal-600 hover:bg-teal-50 transition-all duration-300 shadow-sm"
                  >
                    <Link href="/dashboard">
                      Go to Dashboard
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </main>

        {/* Footer */}
        <footer className="border-t bg-gradient-to-r from-[#00646C] to-[#0097a7]">
          <div className="container flex flex-col md:flex-row items-center justify-between gap-4 py-6 px-4 md:px-6 text-sm">
            <p className="text-center md:text-left text-white">
              © {new Date().getFullYear()} Goodkey Show Services Ltd. All
              rights reserved.
            </p>
            <nav className="flex items-center gap-6">
              <Link
                href="/dashboard"
                className="text-white hover:text-yellow-200 transition-colors"
              >
                Dashboard
              </Link>
            </nav>
          </div>
        </footer>
      </body>
    </html>
  );
}
