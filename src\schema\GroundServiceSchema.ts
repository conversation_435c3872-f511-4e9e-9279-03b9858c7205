import { z } from 'zod';

export const GroundServiceCreateSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  description: z.string().min(1, { message: 'Description is required' }),
  conditions: z.string().min(1, { message: 'Conditions are required' }),
  localCartageAppliable: z.boolean(),
  flatRate: z.preprocess(
    (val) => (val === '' ? undefined : val),
    z.coerce
      .number({ invalid_type_error: 'Flat Rate must be a number' })
      .min(0, { message: 'Flat Rate must be at least 0' }),
  ),
});

export type GroundServiceCreateType = z.infer<typeof GroundServiceCreateSchema>;
