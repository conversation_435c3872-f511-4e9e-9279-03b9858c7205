import { useState, useMemo } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/Badge';
import { Filter, X, ChevronDown, ChevronUp, Building2 } from 'lucide-react';
import { CompanyInList } from '@/models/Company';
import {
  AutocompleteInput,
  AutocompleteOption,
} from '@/components/ui/autocomplete-input';

export interface CompanyFilters {
  name?: string;
  city?: string;
  province?: string;
  showArchived: boolean;
}

interface CompanyFiltersProps {
  filters: CompanyFilters;
  onFilterChange: (filters: CompanyFilters) => void;
  onResetFilters: () => void;
  companies: CompanyInList[];
}

const CompanyFiltersComponent = ({
  filters,
  onFilterChange,
  onResetFilters,
  companies,
}: CompanyFiltersProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const companyOptions = useMemo((): AutocompleteOption[] => {
    return companies.map((company) => ({
      id: company.id,
      label: company.name,
      value: company.name,
      subtitle: [company.city, company.province].filter(Boolean).join(', '),
      icon: <Building2 className="h-3 w-3" />,
      disabled: false,
      metadata: {
        badge: company.isArchived ? 'Archived' : undefined,
      },
    }));
  }, [companies]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    onFilterChange({ ...filters, [name]: value });
  };

  const handleNameChange = (value: string) => {
    onFilterChange({ ...filters, name: value });
  };

  const handleArchivedChange = (value: string) => {
    onFilterChange({ ...filters, showArchived: value === 'true' });
  };

  const activeFilterCount = [
    filters.name && filters.name.trim() !== '',
    filters.city && filters.city.trim() !== '',
    filters.province && filters.province.trim() !== '',
    filters.showArchived === false,
  ].filter(Boolean).length;

  return (
    <div className="mb-6 w-full">
      <div className="flex items-center justify-between border-b pb-4">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <h3 className="text-sm font-medium text-gray-700">Filters</h3>
          {activeFilterCount > 0 && (
            <Badge
              variant="secondary"
              className="ml-2 bg-brand-brown/10 text-brand-brown hover:bg-brand-brown/20"
            >
              {activeFilterCount}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-gray-500 hover:text-gray-700 p-1 h-8"
          >
            {isExpanded ? (
              <>
                <ChevronUp className="h-4 w-4 mr-1" /> Hide
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4 mr-1" /> Show
              </>
            )}
          </Button>
          {activeFilterCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onResetFilters}
              className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 h-8"
            >
              <X className="h-4 w-4 mr-1" /> Clear
            </Button>
          )}
        </div>
      </div>

      {isExpanded && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4 pt-2">
          <div>
            <Label
              htmlFor="name"
              className="text-xs font-medium text-gray-600 mb-1.5 block"
            >
              Company Name
            </Label>
            <AutocompleteInput
              value={filters.name || ''}
              onChange={handleNameChange}
              placeholder="Search by company name"
              options={companyOptions}
              noResultsText="No companies found matching '{value}'"
            />
          </div>

          <div>
            <Label
              htmlFor="city"
              className="text-xs font-medium text-gray-600 mb-1.5 block"
            >
              City
            </Label>
            <Input
              id="city"
              name="city"
              placeholder="Filter by city"
              value={filters.city || ''}
              onChange={handleInputChange}
              className="h-9"
            />
          </div>

          <div>
            <Label
              htmlFor="province"
              className="text-xs font-medium text-gray-600 mb-1.5 block"
            >
              Province
            </Label>
            <Input
              id="province"
              name="province"
              placeholder="Filter by province"
              value={filters.province || ''}
              onChange={handleInputChange}
              className="h-9"
            />
          </div>

          <div>
            <Label
              htmlFor="showArchived"
              className="text-xs font-medium text-gray-600 mb-1.5 block"
            >
              Archived Items
            </Label>
            <Select
              value={filters.showArchived ? 'true' : 'false'}
              onValueChange={(value) => handleArchivedChange(value)}
            >
              <SelectTrigger id="showArchived" className="h-9">
                <SelectValue placeholder="Show archived items?" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="true">Show All</SelectItem>
                <SelectItem value="false">Hide Archived</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {activeFilterCount > 0 && (
        <div className="flex flex-wrap gap-2 mt-4">
          {filters.name && filters.name.trim() !== '' && (
            <Badge
              variant="outline"
              className="bg-gray-100 text-gray-800 hover:bg-gray-200 flex items-center gap-1 py-1 px-2"
            >
              Name: {filters.name}
              <X
                className="h-3 w-3 ml-1 cursor-pointer"
                onClick={() => onFilterChange({ ...filters, name: '' })}
              />
            </Badge>
          )}
          {filters.city && filters.city.trim() !== '' && (
            <Badge
              variant="outline"
              className="bg-gray-100 text-gray-800 hover:bg-gray-200 flex items-center gap-1 py-1 px-2"
            >
              City: {filters.city}
              <X
                className="h-3 w-3 ml-1 cursor-pointer"
                onClick={() => onFilterChange({ ...filters, city: '' })}
              />
            </Badge>
          )}
          {filters.province && filters.province.trim() !== '' && (
            <Badge
              variant="outline"
              className="bg-gray-100 text-gray-800 hover:bg-gray-200 flex items-center gap-1 py-1 px-2"
            >
              Province: {filters.province}
              <X
                className="h-3 w-3 ml-1 cursor-pointer"
                onClick={() => onFilterChange({ ...filters, province: '' })}
              />
            </Badge>
          )}
          {filters.showArchived === false && (
            <Badge
              variant="outline"
              className="bg-gray-100 text-gray-800 hover:bg-gray-200 flex items-center gap-1 py-1 px-2"
            >
              Hide Archived
              <X
                className="h-3 w-3 ml-1 cursor-pointer"
                onClick={() =>
                  onFilterChange({ ...filters, showArchived: true })
                }
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
};

export default CompanyFiltersComponent;
