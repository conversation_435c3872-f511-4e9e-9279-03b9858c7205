import { z } from 'zod';

export const ShowScheduleSchema = z.object({
  showScheduleDate: z.date({ required_error: 'Date is required' }),
  timeStart: z.date({ required_error: 'Start time is required' }),
  timeEnd: z.date({ required_error: 'End time is required' }),
  showScheduleConfirmed: z.boolean().optional(),
  showScheduleComments: z.string().optional(),
  applyScheduleToServiceForm: z.boolean().optional(),
});

export type ShowScheduleFormType = z.infer<typeof ShowScheduleSchema>;
