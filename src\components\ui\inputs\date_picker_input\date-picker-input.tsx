// DatePickerInput.tsx
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import React from 'react';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';

import { useFormField } from '../../form';

// Add the disabled property to the interface
interface IDatePickerInput {
  onValueChange: (value: Date | undefined) => void;
  date?: Date;
  disabled?: (date: Date) => boolean; // New optional prop
}

function DatePickerInput({ onValueChange, date, disabled }: IDatePickerInput) {
  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(
    date || new Date(),
  );

  const { error } = useFormField();

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant={'outline'}
          className={cn(
            'flex w-full bg-transparent text-foreground text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:border-[#1a7efb] disabled:cursor-not-allowed disabled:opacity-50',
            'p-2 border border-gray-300 rounded-sm',
            error && 'border-red-500',
          )}
        >
          {selectedDate ? (
            format(selectedDate, 'PPP')
          ) : (
            <span>Pick a date</span>
          )}
          <CalendarIcon className="ml-auto h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={selectedDate}
          onSelect={(selectedD) => {
            onValueChange(selectedD);
            setSelectedDate(selectedD);
          }}
          initialFocus
          disabled={disabled} // Pass the disabled function to Calendar
        />
      </PopoverContent>
    </Popover>
  );
}

export default DatePickerInput;
