import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import WarehouseQuery from '@/services/queries/WarehouseQuery';
import AdvanceReceivingWarehouseTable from './components/advance_receiving_warehouse';

export const metadata: Metadata = {
  title: 'Goodkey | Advance Receiving Warehouse',
};

export default async function AdvanceReceivingWarehouse() {
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery({
    queryKey: [WarehouseQuery.tags, { warehouseType: 1 }],
    queryFn: () => WarehouseQuery.getAll(1),
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'Master-Setup', link: '/dashboard/setup/master-setup' },
        {
          title: 'Advance Receiving Warehouse',
          link: '/dashboard/setup/master-setup/advance-receiving-warehouse',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <AdvanceReceivingWarehouseTable />
      </HydrationBoundary>
    </AppLayout>
  );
}
