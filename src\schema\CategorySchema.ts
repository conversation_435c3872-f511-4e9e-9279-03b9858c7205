import * as z from 'zod';

export const CategorySchema = z.object({
  id: z.number().optional(),
  code: z.string().optional().nullable(),
  name: z.string().min(1, { message: 'Name is required' }),
  isSoldByQ: z.boolean().default(false),
  imagePath: z.string().optional().nullable(),
  isAvailable: z.boolean().default(false),
  isInternalProduct: z.boolean().default(false),
  displayOrder: z.string().optional().nullable(),
  groupId: z.string().nullable().optional(),
  image: z
    .array(
      z.instanceof(File).refine((file) => file.size < 4 * 1024 * 1024, {
        message: 'File size must be less than 4MB',
      }),
    )
    .min(1, 'Please select at least one image.')
    .max(1)
    .nullable(),
});
export type CategoryData = z.infer<typeof CategorySchema>;

// Schema for update (includes `id` and optional `filePath`)
export const CategoryUpdateSchema = CategorySchema.extend({
  groupType: z.string().optional().nullable(),
});
export type CategoryUpdateData = z.infer<typeof CategoryUpdateSchema>;
