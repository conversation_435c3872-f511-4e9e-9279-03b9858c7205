import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { getQueryClient } from '@/utils/query-client';
import OfferingQuery from '@/services/queries/OfferingQuery';
import RichTextDescription from '../component/rich_text_description';

export default async function Page({
  params,
}: {
  params: Promise<{
    groupId: string;
    categoryId: string;
    id: string;
  }>;
}) {
  try {
    const { groupId, categoryId, id } = await params;

    const client = getQueryClient();

    if (id !== 'add') {
      if (Number.isNaN(Number(id))) throw new Error('Invalid property id');
      await client.prefetchQuery({
        queryKey: ['Offering', { id }],
        queryFn: () => OfferingQuery.getById(Number(id!)),
      });
    }

    return (
      <div className="space-y-4 px-2">
        <HydrationBoundary state={dehydrate(client)}>
          <RichTextDescription
            id={Number(id!)}
            groupId={Number(groupId)}
            categoryId={Number(categoryId)}
          />
        </HydrationBoundary>
      </div>
    );
  } catch (error) {
    redirect('/dashboard/setup/products-services/product/add');
  }
}
