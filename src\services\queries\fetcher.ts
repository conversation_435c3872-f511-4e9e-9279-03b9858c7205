import axios from 'axios';
import Cookies from 'js-cookie';
import { useLocale as getLocate } from 'next-intl';

import { Tokens } from '@/models/Auth';

import { AppError } from './error';

const isFront = typeof window !== 'undefined';

//TODO update this file
async function getCookies(name: string) {
  if (!isFront) {
    const { cookies } = await import('next/headers');
    const cookiesInstance = await cookies();
    return cookiesInstance.get(name)?.value;
  } else {
    return Cookies.get(name);
  }
}
export default async function fetcher<T>(
  url: string | URL | Request,
  init?: RequestInit | undefined,
  noProxy: boolean = false,
) {
  const locale = (await getCookies('NEXT_LOCALE')) || 'fr';
  const fullUrl =
    isFront && !noProxy
      ? `/api/${url}`
      : `${process.env.API_BASE_URL ?? process.env.NEXT_PUBLIC_API_BASE_URL}/${url}`;
  const auth = JSON.parse((await getCookies('AuthStore')) ?? '{}')?.state as
    | Tokens
    | undefined;
  const response = await fetch(fullUrl, {
    ...init,
    headers: {
      ...init?.headers,
      ...((!isFront || noProxy) && auth?.accessToken
        ? { Authorization: `Bearer ${auth.accessToken}` }
        : {}),
      'accept-language': locale,
    },
  });
  if (!response.ok) {
    // Keep your existing 401 handling exactly here
    if (response.status === 401) {
      if (isFront) {
        (await import('../zustand/ConnectionStore'))?.default
          ?.getState()
          ?.setConnected(false, 'expired');
      }
    }

    // Now, attempt to parse the error response body and determine safety
    let errorMessage = `HTTP error! Status: ${response.status}`;
    let isUserSafe = false;
    try {
      // Attempt to parse as JSON first
      const errorData = await response.clone().json();
      if (errorData && errorData.message) {
        errorMessage = errorData.message;
        if (response.status >= 400 && response.status < 500) {
          isUserSafe = true;
        }
      }
    } catch (jsonError) {
      // JSON parsing failed, log the specific JSON error
      console.error('Failed to parse error response as JSON:', jsonError);
      try {
        // Attempt to read the response body as text for more context
        const textError = await response.clone().text();
        // Include the raw text (or part of it) in the error message
        // Avoid making it too long for display purposes if needed
        errorMessage = `HTTP error! Status: ${response.status}. Response body: ${textError.substring(0, 200)}${textError.length > 200 ? '...' : ''}`;
      } catch (textError) {
        // If reading as text also fails, log that too
        console.error('Failed to read error response as text:', textError);
        // Stick with the original generic message
        errorMessage = `HTTP error! Status: ${response.status}. Failed to read response body.`;
      }
      // Since parsing failed or we got non-JSON, message is not considered safe
      isUserSafe = false;
    }
    // Throw AppError with the potentially more detailed message and status
    throw new AppError(errorMessage, response.status, isUserSafe);
  }

  // Check if the response is a Blob (for file downloads)
  if (
    response.headers
      .get('Content-Type')
      ?.includes('application/octet-stream') ||
    response.headers.get('Content-Type')?.includes('application/pdf') ||
    response.headers.get('Content-Type')?.includes('image/')
  ) {
    return (await response.blob()) as T;
  }

  const data = (await response.json()) as ApiResponse<T>;

  if (data.statusCode !== 200 && data.statusCode !== 201) {
    // Determine safety based on the statusCode in the response body
    const statusCode = data.statusCode ?? response.status;
    // Assume messages for 4xx status codes in the body are also user-safe
    const isUserSafe = statusCode >= 400 && statusCode < 500;
    throw new AppError(
      data.message ?? 'Something went wrong',
      statusCode,
      isUserSafe, // Pass the safety flag here too
    );
  }
  return data.data;
}
export async function proxyFetch<T>(
  url: string | URL | Request,
  init?: RequestInit | undefined,
) {
  const response = await fetch(`api/${url}`, init);

  if (!response.ok) {
    throw new AppError(
      `HTTP error! Status: ${response.status}`,
      response.status,
    );
  }

  const data = (await response.json()) as ApiResponse<T>;
  if (data.statusCode !== 200) {
    throw new AppError(
      `HTTP error! Status: ${response.status}`,
      response.status,
    );
  }
  return data.data;
}
const axiosInstance = axios.create();

axiosInstance.interceptors.request.use((config) => {
  let locale;
  if (typeof window !== 'undefined') {
    locale = Cookies.get('NEXT_LOCALE') ?? 'fr';
  } else {
    locale = getLocate();
  }
  config.url = `${process.env.API_BASE_URL}/${locale}/api/${config.url}`;

  return config;
});
export { axiosInstance };
