{"Common": {"ValidationMessages": {"minErrorMessage": "{field} is too short", "maxErrorMessage": "{field} is too long", "invalidEmailErrorMessage": "Please enter a valid email address", "Required": "The {field} is required"}, "tooltip": {"edit": "Edit", "delete": "Delete", "view": "View", "copy": "Copy", "archive": "Archive", "report": "Report"}, "tables": {"id": "ID", "name": "Name", "status": "Status", "phoneNumber": "Phone Number", "createdAt": "Created At", "updatedAt": "Updated At", "actions": "Actions", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "mobile": "Mobile", "preferredLang": "Preferred Lang", "address1": "Address", "city": "City", "province": "Province", "postalCode": "Postal Code", "country": "Country", "clientType": "Client Type", "clientStatus": "Client Status", "clientStatusActive": "Active", "clientStatusInactive": "Inactive", "outfitters": "Outfitters", "accommodationType": "Accommodation Type", "subject": "Subject", "source": "Source", "originalFileName": "Original File Name", "uploadDate": "Uploaded Date", "title": "Title", "company": "Company", "verified": "Verified", "footer": "Footer", "clientName": "Client Name", "assignedTo": "Assigned To", "importantNote": "Important Notes", "quantity": "Quantity", "duration": "Duration", "description": "Description", "itemType": "Item Type", "action": "Action", "areYouSureToRemove": "Are you sure you want to remove"}, "status": {"published": "Published", "draft": "Draft"}, "email": "Email", "pleaseWait": "Please wait...", "errorMessage": "We couldn't process your request. Please try again.", "message": "Message", "submit": "Submit", "cancel": "Cancel", "failedCaptcha": "Captcha verification failed.", "error": "Error", "somethingWentWrong": "Something went wrong. Please try again.", "done": "Done", "close": "Close", "open": "Open", "openSidebar": "Open sidebar", "Language": "Language", "english": "English", "french": "French", "edit": "Edit", "addNew": "Add New", "inactive": "Inactive", "active": "Active", "prospect": "Prospect", "profileId": "Profile ID", "createdBy": "Created By", "updatedBy": "Updated By", "createdAt": "Created At", "updatedAt": "Updated At", "success": "Success", "created": "created", "updated": "updated", "next": "Next", "prev": "Previous", "symbol": "Symbol", "add": "Add", "delete": "Delete", "view": "View", "select": "Select", "update": "Update", "upload": "Upload", "successTitle": "Success", "outfitters": "Outfitters", "agree": "I agree", "preview": "Preview", "live": "Live", "product": "Product", "package": "Package", "outfitter": "Outfitter", "room": "Room", "service": "Service", "offerings": "Offerings", "accommodation": "Accommodation", "territory": "Territory", "autoGenerated": "Auto Generated", "published": "Published", "unpublished": "Unpublished", "archived": "Archived", "notArchived": "Not Archived", "create": "Create", "save": "Save", "search": "Search", "reset": "Reset", "saveLoading": "Saving...", "minSize": "<PERSON>", "maxSize": "<PERSON>", "clickToUpload": "Click to upload", "dragAndDrop": "or drag and drop", "included": "Included", "notIncluded": "Not Included", "continue": "Continue", "dialogConfirmTitle": "Are you absolutely sure?", "dialogConfirmDesc": "This action cannot be undone. This will permanently delete the", "lastModifiedBy": "Last Modified By", "loading": "Loading...", "saving": "Saving...", "selectMainImage": "Select main image", "editImage": "Edit image", "deleteImage": "Delete image", "noImagesUploaded": "No images uploaded yet", "addNewImage": "Add new image", "imageGalleryTitle": "List of images", "addingImageToast": "Image has been added successfully", "imageVerification": "The maximum allowed file size for image is 4MB. Only files with the following extensions are accepted: .jpg, .jpeg, .png.", "available": "Available", "imageRemoveToast": "Image has been removed successfully", "documentManagement": "Document Management", "documentGroups": "Document Groups", "documentGroup": "Document Group", "addNewDocumentGroup": "Add New Document Group", "editDocumentGroup": "Edit Document Group", "documentCategories": "Document Categories", "documentCategory": "Document Category", "addNewDocumentCategory": "Add New Document Category", "editDocumentCategory": "Edit Document Category", "documentFileTypes": "Document File Types", "documentFileType": "Document File Type", "addNewDocumentFileType": "Add New Document File Type", "editDocumentFileType": "Edit Document File Type", "documents": "Documents", "document": "Document", "addNewDocument": "Add New Document", "editDocument": "Edit Document", "fileExtension": "File Extension", "mimeType": "MIME Type", "deleteImageTitle": "Delete image", "deleteImageDescription": "Are you sure you want to delete this image?", "documentGroupCreatedSuccess": "Document group created successfully", "documentGroupUpdatedSuccess": "Document group updated successfully", "documentGroupDeletedSuccess": "Document group deleted successfully", "documentCategoryCreatedSuccess": "Document category created successfully", "documentCategoryUpdatedSuccess": "Document category updated successfully", "documentCategoryDeletedSuccess": "Document category deleted successfully", "documentFileTypeCreatedSuccess": "Document file type created successfully", "documentFileTypeUpdatedSuccess": "Document file type updated successfully", "documentFileTypeDeletedSuccess": "Document file type deleted successfully", "documentCreatedSuccess": "Document created successfully", "documentUpdatedSuccess": "Document updated successfully", "documentDeletedSuccess": "Document deleted successfully", "deleteButtonPending": "Please wait...", "bedConfig": "Bed Configuration", "main": "Main", "imageSelectionTitle": "Select a main image", "backToList": "Back to list", "pricePeriodCreated": "Pricing period has been created successfully", "pricePeriodDeleted": "Pricing period has been deleted successfully", "priceSectionTitle": "Price Section", "priceCalendarTitle": "Price Calendar", "dateRange": "Date Range", "weekday": "Weekday", "weekdayPlaceholder": "Select Weekday", "price": "Price", "pricePlaceholder": "Enter Price", "deleteRange": "Delete Range", "addRange": "Add Range", "free": "Free", "filterBy": "Filter <PERSON>", "back": "Back", "todayPrice": "Today's Price", "fullDay": "Full Day", "halfDay": "Half Day", "isHalfDay": "Is Half Day", "lastModified": "Last Modified", "modifiedBy": "Modified By", "profile": "Profile", "logOut": "Log Out", "login": "<PERSON><PERSON>", "sessionExpiredTitle": "Session expired", "sessionExpiredDesc": "Session has expired. Please login again.", "applicableTaxesLabel": "Applicable Taxes", "GSTLabel": "GST", "PSTLabel": "PST", "HSTLabel": "HST", "department": "Department", "personalEmail": "Personal Email", "workEmail": "Work Email", "role": "Role", "verified": "Verified", "notVerified": "Not Verified", "username": "Username", "warning": "Warning", "yes": "Yes", "no": "No", "archive": "Archive", "archiveEmail": "Archive Email", "archiveEmailConfirm": "Are you sure you want to archive this email?", "archiveSuccessMessage": "Email has been archived successfully.", "removeEmail": "Re<PERSON><PERSON>", "removeEmailConfirm": "Are you sure you want to remove this email?", "removeSuccessMessage": "Email has been removed successfully.", "testEmail": "Test Email", "testEmailConfirm": "Are you sure you want to send a test to this email?", "testSuccessMessage": "<PERSON><PERSON> has been sent successfully.", "sendEmail": "Send Email", "sendEMailConfirm": "Are you sure you want to send this email?", "sendSuccessMessage": "<PERSON><PERSON> has been sent successfully.", "announcementReport": "Announcement Report", "emailCampaignReport": "Email Campaign Report", "detailedInformationCampaign": "Please see below for detailed information about this campaign.", "detailedInformationAnnouncement": "Please see below for detailed information about this announcement.", "announcement": "Announcement", "emailCampaign": "Email Campaign", "createdDate": "Created Date:", "lastUpdatedBy": "Last Updated By:", "lastUpdatedDate": "Last Updated Date:", "sentBy": "Sent By:", "sentDate": "Sent Date:", "fromEmail": "From Email:", "fromName": "From Name:", "totalSendRequest": "Total Send Request:", "numberSentSuccess": "No Sent Success:", "numberSentError": "No Sent Error:", "total": "Total", "noProcessed": "No. Processed", "noDelivered": "No. Delivery", "noOpen": "No. Open", "noClick": "No. Click", "noBounce": "No. <PERSON>", "noDeferred": "No. Deferred", "noDropped": "No. Dropped", "report": "Report", "or": "Or", "footer": "Footer", "tableNote": "Please apply filters to see data.", "noResults": "No results found.", "remove": "Remove", "client": "Clients", "numberOfAdults": "Number of Adults", "numberOfChildren": "Number of Children", "note": "Notes", "createRequest": "Create Request", "createRequestDesc": "Please fill out the following form to create a request.", "requestCreated": "Request has been created successfully.", "Product": "Product", "Service": "Service", "Room": "Room", "Package": "Package", "Quantity": "Quantity", "FullDay": "Full Day", "HalfDay": "Half Day", "removeRequestLine": "Remove Item", "removeRequestLineConfirm": "Are you sure you want to remove this item?", "updateRange": "Update Range", "pricePeriodUpdated": "Pricing period has been updated successfully", "editing": "Editing", "itemRemoved": "Item has been removed successfully.", "PriceBreakdown": "Price Breakdown", "PricePerUnit": "Price/Unit", "AccTax": "Acc Tax", "items": "Items", "from": "From", "to": "to", "Guests": "Guests", "currency": "<PERSON><PERSON><PERSON><PERSON>", "cad": "CAD", "removeDeposit": "Re<PERSON><PERSON>", "removeDepositConfirm": "Are you sure you want to remove this deposit?", "archiveDepositSuccessMessage": "Deposit has been removed successfully.", "send": "Send", "unassigned": "Unassigned"}, "contact": {"usernamePlaceholder": "Enter your username", "rememberMe": "Remember me", "enterCredentials": "Enter your credentials to access your account", "amount": "Amount", "metaDataTitle": "Contact", "metaDataDesc": "<PERSON> Contact | <PERSON>", "bannerTitle": "Contact", "bannerSubTitle": "Contact", "contactTitle": "Send us a message", "contactSubTitle": " Please fill out the following form if you have any questions about our products.", "contactUsa": "United States", "contactName": "Name ", "contactCompany": "Company ", "contactPhone": "Phone Number ", "contactEmail": "Email ", "contactMessage": "Your Message ", "contactBtn": "Submit Form", "contactSuccessmsg": "Thank you for your message. We will get in touch with you shortly", "contactErrormsg": "Something went wrong. Please try again later.", "placeholderName": "Your Name", "placeholderCompany": "Your Company", "placeholderPhone": "Phone Number", "placeholderMail": "Email Address", "placeholderMessage": "Your Message", "nameLeastcaracters": "Name must be at least 7 characters long", "contactInfoTitleP1": "Contact info", "contactInfoTitleP2": "Get in Touch", "contactInfoDescription": "Are you looking for a partner to help take your digital strategy up another level? We've got just what you need! We will analyze and evaluate projects so that we can provide recommendations tailored specifically toward meeting each client's unique needs.", "formTitle": "Submit an Inquiry", "firstName": "First Name", "lastName": "Last Name", "name": "Name", "description": "Description", "email": "Email", "message": "Message", "submit": "Send your message", "contactSubmit": "Submit", "subject": "Subject", "phoneNumber": "Phone Number", "mobileNumber": "Mobile Number", "telephone": "Telephone", "leaveComment": "Leave a Comment", "selectInquiry": "Select an Inquiry", "country": "Country", "city": "City", "countryPlaceholder": "Select Country", "postalCode": "Postal Code", "address": "Address", "preferredLang": "Preferred Language", "preferredLangPlaceholder": "Select Preferred Language", "clientType": "Client Type", "clientTypePlaceholder": "Select Client Type", "clientStatus": "Client Status", "clientStatusPlaceholder": "Select Client Status", "province": "Province", "companyName": "Company Name", "comments": "Comments", "namePlaceholder": "Enter your name", "firstNamePlaceholder": "Enter your first name", "lastNamePlaceholder": "Enter your last name", "emailPlaceholder": "Enter your email", "phonePlaceholder": "Enter your phone number", "messagePlaceholder": "Enter your message", "requestQuote": "Get a Free Quote", "sendRequest": "Send Request", "movingFrom": "Moving From", "movingTo": "Moving To", "movingDate": "Moving Date", "setAppointment": "Set an Appointment", "appointmentDate": "Appointment Date", "jobSubTitle": "To submit a quick job application, please fill out the boxes below:", "UploadCVLabel": "Upload your CV", "UploadCVPlaceholder": "Choose <PERSON>", "UploadOtherLabel": "Upload Other Document", "UploadOtherPlaceholder": "Choose <PERSON>", "emailCardNote": "(For any further questions or information, please feel free to send your request to this email.)", "datePlaceholder": "Select Date", "cityPlaceholder": "City/State", "gender": "Gender", "genderPlaceholder": "Select Gender", "genderMale": "Male", "genderFemale": "Female", "genderOther": "Other", "birthDate": "Birth Date", "birthDatePlaceholder": "Select Birth Date", "outfittersLabel": "Outfitters", "outfittersPlaceholder": "Select Outfitters", "Submit": "Submit", "accountVerification": "Account Verification", "accountEnterPassword": "Please enter a password for your account.", "userName": "Username", "password": "Password", "confirmPassword": "Confirm Password", "newPasswordPlaceholder": "Please enter your new password", "signIn": "<PERSON><PERSON>", "signInLoading": "Logging in...", "createAccount": "Create account", "credentialsError": "Username or password is incorrect", "forgetPassword": "Forget Password", "sessionExpired": "Session expired", "passwordResetRequestedSuccess": "Password Reset Requested Successfully", "emailVerificationLinkText": "You will receive an Email with the verification link", "forgetPasswordQuestion": "Forgot your password?", "resetPassword": "Reset Password", "selectCluster": "Select a cluster", "maxFileUploadText": "The maximum allowed file size for image is 4MB. Only files with the following extensions are accepted: .jpg, .jpeg, .png.", "titleLabel": "Title", "altTextLabel": "Alt Text", "imageLabel": "Image", "titlePlaceholder": "Enter title", "altTextPlaceholder": "Enter alt text", "imagePlaceholder": "Choose Image", "selectCategory": "Select a category", "displayOrder": "Display Order", "activityType": "Activity Type", "department": "Department", "selectActivityType": "Select an activity type", "unity": "Unity", "selectUnity": "Select unity", "symbol": "Symbol", "applicableTaxes": "Applicable taxes", "isAddon": "<PERSON>", "mainService": "Main Service", "selectMainService": "Select main service", "accommodationType": "Accommodation Type", "selectAccommodationType": "Select an accommodation type", "outfitter": "Outfitter", "selectOutfitter": "Select an outfitter", "accommodationId": "Accommodation ID", "autoGenerated": "Auto Generated", "amenities": "Amenities", "selectAmenities": "Select amenities", "service": "Service", "selectService": "Select a service", "title": "Title", "altText": "Alt Text", "roomIdentity": "Room Identity", "roomId": "Room ID", "roomType": "Room Type", "selectRoomType": "Select a room type", "quantity": "Quantity", "capacity": "Capacity", "speciesClassification": "Specie Classification", "selectSpeciesClassification": "Select Specie Classification", "hasRestriction": "Has Restriction?", "conservationDetail": "Conservation Detail", "profileImage": "Profile Image", "content": "Content", "send": "Send", "useDefaultFooter": "Use Default Footer?", "date": "Date", "type": "Type", "from": "From", "source": "Source", "language": "Language", "selectProvince": "Select Province", "duplicate": "Duplicate", "langPlaceholder": "Select Language", "sendTo": "Send to", "template": "Template", "templatePlaceholder": "Select Template", "uploadImage": "Upload Image", "addToExistingSource": "Add to existing source", "addToExistingSourcePlaceholder": "Select a Contact Source", "newSourceName": "New Source Name", "file": "File", "salutation": "Salutation", "company": "Company", "applyExistDesign": "Apply this Template to unsent email campaigns?", "statusLabel": "Status", "statusPlaceholder": "Select Status", "employeeLabel": "Employee", "employeePlaceholder": "Select Employee", "serviceType": "Service Type", "selectServiceType": "Select Service Type", "serviceDuration": "Service Duration", "selectServiceDuration": "Select Service Duration"}, "navigation": {"menuLinks": {"dashboard": "Dashboard", "client": "Client", "clientList": "Client List", "addClient": "Add Client", "requests": "Requests", "webManagement": "Web Management", "orders": "Orders", "offerings": "Offerings", "products": "Products", "inventory": "Inventory", "packages": "Packages", "roleManagement": "Role Management", "packagesPrices": "Packages Prices", "productsPrices": "Products Prices", "servicePrices": "Service Prices", "roomCategoryPrices": "Room Category Prices", "operationalSeasons": "Operational Seasons", "communications": "Communications", "emailCampaigns": "Email Campaigns", "emailTemplates": "Email Templates", "contactList": "Clients' Contact", "uploadContactFile": "Upload Contact File", "locations": "Locations", "cluster": "Cluster", "territory": "Territory", "outfitters": "Outfitters", "buildingTypes": "Building Types", "roomTypes": "Room Types", "accommodation&facilities": "Accommodation & Facilities", "accommodations": "Accommodations", "bedConfigurations": "Bed Configurations", "services": "Services", "amenities": "Amenities", "mealPlanTypes": "Meal Plan Types", "activities": "Activities", "interests": "Interests", "activityTypes": "Activity Types", "departments": "Departments", "equipment&supplies": "Equipment & Supplies", "equipment": "Equipment", "equipmentCategories": "Equipment Categories", "wildlife&nature": "Wildlife & Nature", "species": "Species", "speciesCategories": "Species Categories", "season&scheduling": "Seasons & Scheduling", "seasons": "Seasons", "userManagement": "User Management", "roles": "Roles", "users": "Users", "masterSetup": "Master Setup", "systemSetup": "System Setup", "mediaTypes": "Media Types", "taxes": "Taxes", "businessRelated": "Business Related", "department": "Department", "employeeManagement": "Employee Management", "emailAnnouncement": "Email Announcement", "emailCampaign": "Email Campaign", "emailCampaignDetail": "Email Campaign Detail", "emailContactFile": "Email Contact File", "emailTemporary": "Email Temporary Contacts", "emailTemplate": "<PERSON>ail Te<PERSON>late", "emailTemplateDetail": "Email Template Detail", "emailTemplateFooter": "<PERSON><PERSON>", "emailContactList": "Clients' Contact List"}}, "emailDesignPage": {"updateFooterSuccessMessage": "Footer has been saved successfully.", "generalInformation": "General Information", "descriptionFooter": "the general information of the email footer.", "description": "the general information of the email template.", "archiveSuccessMessage": "Email Template has been archived successfully.", "archiveEmailTemplate": "Archive Email Template", "archiveEmailTemplateConfirm": "Are you sure you want to archive this email template?", "addTemplate": "Add New Template", "warning": "The email template content will be applied to the unsent email campaign using this template. Are you sure you want to continue?", "applyExistDesign": "Apply this Template to unsent email campaigns?", "updateTemplateSuccessMessage": "Email Template has been saved successfully."}, "emailTemporaryContactPage": {"removeContactConfirm": "Are you sure you want to remove this contact?", "removeContact": "Remove Contact", "removeContactSuccessMessage": "Contact has been removed successfully.", "confirmContactSuccessMessage": "Contact has been confirmed successfully.", "verifyContactProfile": "Verify Contact Profile", "confirmContact": "Which contact profile do you want to use?", "newClientProfile": "New Client Profile", "existingClientProfile": "Existing Client Profile", "note1": "Once you confirmed, the new client profile contact will override the original client profile contact when the list is synched.", "note2": "Once you confirmed, the new client profile contact will not be added to your client contact list when the list is synched", "note3": "unless you update their email", "updateContactSuccessMessage": "Contact has been updated successfully.", "tempContact": "Temporary Contact", "updateTempContact": "Update Temporary Contact", "addTempContact": "Add Temporary Contact", "errorMessage": "Errors: Verify all the notes below or remove the contact", "syncContactProfile": "Sync Contact", "syncContactConfirm": "Are you sure you want to sync contacts below?", "syncContactSuccessMessage": "Contacts have been synced successfully."}, "emailContactFilePage": {"addContact": "Upload Contact File", "archiveSuccessMessage": "Contact file has been archived successfully.", "archiveContactFile": "Archive Contact File", "archiveContactFileConfirm": "Are you sure you want to archive this contact file?", "uploadSuccessMessage": "Contact file has been uploaded successfully.", "uploadTitle": "Upload Contact File", "uploadDescription": "Upload contact file here in excel.", "contactFileDetails": "Contact File Details", "fileDetailsDescription": "Detailed information about the file", "fileName": "File Name:", "uploadDate": "Upload Date:", "uploadedBy": "Uploaded By:", "originalFileName": "Original File Name:", "contactSource": "Contact Source:", "synced": "Synced:", "no": "No", "syncedBy": "Synced By:", "syncDate": "Sync Date:", "noOfSyncedContacts": "No. of Synced Contacts:"}, "emailAnnouncementPage": {"title1": "Announcements", "title2": "Announcement Details", "tabs": {"tab1Title": "General Information", "tab1Description": "All detail information about your anouncement email.", "tab2Title": "Content", "tab3Title": "Review & Send"}, "addAnnouncement": "Add New Announcement", "review": "Review", "reviewDescription": "Review your announcement.", "emailContent": "Email Content", "emailContentDescription": "Email content for your announcement.", "announcementSuccessMessageP1": "Announcement has been", "announcementSuccessMessageP2": "successfully", "contentSuccessMessage": "Content has been updated successfully.", "sendAnnouncement": "Send Announcement", "sendAnnouncementConfirm": "Are you sure you want to send this announcement?"}, "emailCampaignPage": {"title1": "Campaigns", "title2": "Campaign Details", "tabs": {"tab1Title": "General Information", "tab1Description": "All detail information about your campaign email.", "tab2Title": "Content", "tab3Title": "Review & Send"}, "addCampaign": "Add New Campaign", "review": "Review", "reviewDescription": "Review your campaign.", "emailContent": "Email Content", "emailContentDescription": "Email content for your campaign.", "campaignSuccessMessageP1": "Campaign has been", "campaignSuccessMessageP2": "successfully", "contentSuccessMessage": "Content has been updated successfully.", "campaignNote": "Change in email template will reset the content and subject.", "campaignSentSuccessMessage": "<PERSON><PERSON> has been sent successfully.", "sendCampaign": "Send Campaign", "sendCampaignConfirm": "Are you sure you want to send this campaign?", "warningDescription": "The email campaign content will be updated with the latest email template changes. Are you sure you want to continue?"}, "homepage": {}, "clientPage": {"client": "Client", "clientList": "Client List", "addClient": "Add Client", "addClientContact": "Upload clients'contact list", "tabs": {"tab1Title": "Client Profile", "tab2Title": "Personal Information", "tab3Title": "Interests", "tab4Title": "Subscription", "tab5Title": "Order History"}, "clientProfileTab": {"title": "Client Profile Information", "description": "All detail information about your client.", "clientSuccessMessageP1": "Client has been", "clientSuccessMessageP2": "updated successfully in our system."}, "personalInfoTab": {"clientSuccessMessage": "client's personal information has been updated successfully in our system.", "salutationLabel": "Salutation", "salutationPlaceholder": "Select salutation", "weightLbsLabel": "Weight in Lbs", "weightLbsPlaceholder": "Enter weight in Lbs", "healthIssueNoteLabel": "Health Issue Note", "healthIssueNotePlaceholder": "Enter health issue note"}, "interestTab": {"clientSuccessMessage": "client's interests has been updated successfully in our system.", "outfittersDesc": "Select the outfitters the client are interested in", "interestsLabel": "Interests", "interestsPlaceholder": "Select Interests", "interestsDesc": "Select the interests the client are interested in"}, "communicationTab": {"clientSuccessMessage": "client's communication information has been updated successfully in our system.", "conditionText": "By checking this box, you agree to receive communications, promotions, and other information from us via email and SMS in the language you have selected.", "subscribedDate": "Subscribed Date", "subscribedBy": "Subscribed By", "unsubscribedDate": "Unsubscribed Date", "unsubscribedBy": "Unsubscribed By"}}, "webManagementPage": {"title": "Website Management", "subtitle": "Website List"}, "productPage": {"title": "Products", "subtitle": "Product List", "tabs": {"tab1Title": "General", "tab2Title": "Image"}, "productGeneralTab": {"title": "Product General Information", "description": "Product General Information Description", "productSuccessMessageP1": "Product has been", "productSuccessMessageP2": "updated successfully in our system.", "productIdLabel": "Product ID", "unityLabel": "Unity", "unityPlaceholder": "Select Unity", "productTypeLabel": "Product Type", "productTypePlaceholder": "Select Product Type", "applicableTaxesLabel": "Applicable Taxes", "GSTLabel": "GST", "PSTLabel": "PST", "HSTLabel": "HST", "GSTPlaceholder": "Enter GST", "PSTPlaceholder": "Enter PST", "HSTPlaceholder": "Enter HST", "isAddonLabel": "<PERSON>", "mainProductIdLabel": "Main Product", "mainProductIdPlaceholder": "Select Main Product", "isStockLabel": "Is Stock", "stockPeriodTypeIdLabel": "Stock Period Type", "stockPeriodTypeIdPlaceholder": "Select Stock Period Type", "stockQuantityLabel": "Stock Quantity", "stockQuantityPlaceholder": "Enter Stock Quantity", "productStatusLabel": "Product Status"}, "productImageTab": {"title": "product Image", "description": "Product Image Information Description", "productSuccessMessageP1": "Product Image has been", "productSuccessMessageP2": " successfully in our system."}}, "packagePage": {"title": "Packages", "subtitle": "Package List", "addRoomToPackage": "Add Room To Package", "tabs": {"tab1Title": "General", "tab2Title": "Image", "tab3Title": "Rooms", "tab4Title": "Services", "tab5Title": "Products", "tab6Title": "Contents"}, "packageGeneralTab": {"title": "Package General Information", "description": "Package General Information Description", "packageSuccessMessageP1": "Package has been", "packageSuccessMessageP2": "updated successfully in our system.", "packageIdLabel": "Package ID", "priceNoteLabel": "Price Note", "priceNotePlaceholder": "Enter Price Note", "detailsLabel": "Details", "detailsPlaceholder": "Enter Details", "guideAvailabilityLabel": "Guide Availability", "guideAvailabilityPlaceholder": "Select Guide Availability", "durationLabel": "Duration", "durationPlaceholder": "Enter Duration", "nightsLabel": "Nights", "nightsPlaceholder": "Enter Nights", "quantityLabel": "Quantity", "quantityPlaceholder": "Enter Quantity", "learnMoreLabel": "Learn More", "learnMorePlaceholder": "Enter Learn More", "packageStatusLabel": "Package Status", "packageGroupCreated": "Package group created successfully", "packageGroupUpdated": "Package group updated successfully", "packageGroupsLabel": "Package Groups", "addNewGroup": "Add New Group", "editGroup": "Edit Group", "createPackageGroup": "Create Package group", "editPackageGroup": "Edit Package group", "groupNameLabel": "Group Name", "groupNamePlaceholder": "Enter Group Name", "minGroupSizeLabel": "Min Group Size", "minGroupSizePlaceholder": "Enter Min Group Size", "maxGroupSizeLabel": "Max Group Size", "maxGroupSizePlaceholder": "Enter Max Group Size (Optional)", "applicableTaxesLabel": "Applicable Taxes", "GSTLabel": "GST", "PSTLabel": "PST", "HSTLabel": "HST", "roomLabel": "Room", "roomPlaceholder": "Select Room"}, "packageImageTab": {"title": "Package Image", "description": "Package Image Information Description", "packageSuccessMessageP1": "Package Image has been", "packageSuccessMessageP2": " successfully in our system."}, "packageRoomsTab": {"title": "Package Rooms", "description": "Package Rooms Information Description", "packageRoomAddSuccessMessage": "Package Room has been added successfully", "packageRoomUpdateSuccessMessage": "Package Room has been updated successfully", "packageRoomDeleteSuccessMessage": "Package Room has been deleted successfully", "modalTitle": "Add Package Room", "modalDescription": "Add a new package room.", "roomLabel": "Room", "roomPlaceholder": "Select Room", "packageRoomLabel": "Package Room"}, "packageServicesTab": {"title": "Package Services", "description": "Package Services Information Description", "packageServiceAddSuccessMessage": "Package Service has been added successfully", "packageServiceUpdateSuccessMessage": "Package Service has been updated successfully", "packageServiceDeleteSuccessMessage": "Package Service has been deleted successfully", "packageServiceDeleteErrorMessage": "Failed to delete package room.", "packageServiceStatusSuccessMessage": "Package Service status has been updated successfully", "modalTitle": "Add Package Service", "modalDescription": "Add a new package service.", "serviceLabel": "Service", "servicePlaceholder": "Select Service", "serviceType": "Service Type", "serviceTypePlaceholder": "Select Service Type", "unknown": "Unknown", "durationPlaceholder": "Select Duration"}, "packageProductsTab": {"title": "Package Products", "description": "Package Products Information Description", "packageProductAddSuccessMessage": "Package Product has been added successfully", "packageProductUpdateSuccessMessage": "Package Product has been updated successfully", "packageProductDeleteSuccessMessage": "Package Product has been deleted successfully", "modalTitle": "Add Package Product", "modalDescription": "Add a new package product.", "productLabel": "Product", "productPlaceholder": "Select Product", "packageProductLabel": "Package Product", "cardView": "Card View", "tableView": "Table View", "product": "Product", "service": "Service", "noItems": "No items in the package.", "itemTypeFallback": "Product"}}, "accommodationPage": {"title": "Accommodations", "subTitle": "Accommodations List", "editTitle": "Update the accommodation", "addNewTitle": "Add a new accommodation", "tabs": {"tab1Title": "General", "tab2Title": "Rooms", "tab3Title": "Amenities", "tab4Title": "Services"}, "addNewAccommodation": "Add a new accommodation", "accommodationUpdated": "Accommodation has been updated successfully", "accommodationCreated": "Accommodation has been created successfully", "accommodationDetailPage": {"title": "Accommodation Details", "description": "Description of the accommodation"}, "accommodationGeneralTab": {"title": "General Information on Accommodation", "description": "Description of the general information on accommodation"}, "amenityTab": {"title": "Amenities", "description": "Add amenities to the accommodation", "amenitySuccessMessage": "Accommodation's amenities has been updated successfully"}, "serviceTab": {"title": "Services", "description": "Add services to the accommodation", "serviceSuccessMessage": "Accommodation's services has been updated successfully", "outfitterService": "Available services selected by outfitters"}, "roomTab": {"roomCategories": "Room Categories", "addRoomCategory": "Add Room category", "roomsOf": "rooms of", "roomOf": "room of", "noRoomCategoriesAssociated": "No Room categories Associated", "noRoomsMessage": "There are currently no room categories associated with this accommodation.", "addRoomsHelp": "Adding rooms to your accommodation helps potential guests understand the available options and make informed decisions.", "imageSelectionTitle": "Select a main image for the room", "mainImageSuccessMessage": "Main image has been set successfully", "physicalRooms": "Physical Rooms", "addPhysicalRoom": "Add Physical Room", "roomId": "Room ID", "physicalRoom": "Physical Room", "updatePhysicalRoom": "Update physical room details", "addNewPhysicalRoom": "Add a new physical room", "physicalRoomCreated": "The physical room has been created successfully", "physicalRoomUpdated": "The physical room has been updated successfully", "room": "Room", "roomUpdateDescription": "Update the room details", "roomCreateDescription": "Add a new room", "roomCreated": "The room has been created successfully", "roomUpdated": "The room has been updated successfully"}}, "orderPage": {"title": "Orders", "subTitle": "Orders List", "editTitle": "Order Details", "addNewTitle": "Add a new order", "orderInformation": "Order Information", "guests": "List of Guests", "addGuest": "Add Guest Info", "addGuestModalDescription": "Enter guest details", "firstNameLabel": "First Name", "lastNameLabel": "Last Name", "emailLabel": "Email", "isAdultLabel": "Check box below if guest is over 16 years old", "removeGuest": "<PERSON><PERSON><PERSON>", "removeGuestConfirm": "Are you sure you want to remove this guest?", "archiveSuccessMessage": "Guest has been removed successfully.", "save": "Save", "addGuestSuccessMessage": "Guest info has been added successfully.", "paymentInformation": "Payment Information", "loading": "Loading...", "noPayments": "No Payments", "addPaymentSuccessMessage": "Payment has been added successfully.", "addPayment": "Add Payment", "addPaymentModalDescription": "Enter payment details", "paymentDate": "Payment Date", "amount": "Amount", "remainingBalance": "Remaining Balance", "paymentMethodId": "Payment Method", "description": "Description", "authorizationNumber": "Authorization Number", "cardNumber": "Card Number (last 4 digits)", "checkNumber": "Check Number", "institution": "Institution", "confirmationNum": "Confirmation Number", "selectPaymentMethod": "Select Payment Method", "currentBalance": "Current Balance", "newBalance": "New Balance", "paymentNote": "Once you click Save, this payment will be added, but you won't be able to delete or modify it."}, "requestPage": {"title": "Requests", "subTitle": "Request List", "editTitle": "Request Details", "addNewTitle": "Add a new request", "statusUpdated": "Request status has been updated successfully", "updateRequestDetails": "Update the request status", "update": "Request Status", "assign": "Assign Em<PERSON>loyee", "updateAssign": "Select employee that will handle the request", "titleImportantNote": "Important Note", "updateImportantNote": "Input important note", "uploadTitle": "Upload Quotation File", "uploadDescription": "Please upload your quotation file along with the relevant details below.", "uploadSuccessMessage": "Your quotation has been uploaded successfully.", "updateAmountAndTaxes": "* Update amount and taxes below will be reflected in the deposit schedule.", "transferRequest": "Transfer To Order", "transferRequestConfirm": "Are you sure you want to transfer this request to order?", "requestDetailPage": {"guestTable": {"id": "ID", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "isAdult": "Adult (16+)", "noGuestsFound": "No guests found", "actions": "Actions"}, "internalCommunication": "Internal Communication", "noDeposits": "No deposits required for this request yet. Please upload your quotation.", "editDeposit": "<PERSON>", "depositAmountLabel": "Deposit Amount (CAD $)", "depositAmountPlaceholder": "Enter deposit amount", "dueDateLabel": "Due Date", "dueDatePlaceholder": "Select due date", "saveDeposit": "Save Deposit", "depositSuccessMessage": "Deposit has been saved successfully", "depositDescription": "Fill in the deposit amount and due date. The percentage will be calculated automatically based on the total.", "title": "Request Details", "description": "All detail information about request.", "requestContentSummary": "Request Content Summary", "halfDay": "Half Day", "fullDay": "Full Day", "noItemsInTheRequest": "No items in the request.", "requestInformation": "Request Information", "status": "Status", "assignedTo": "Assigned To", "assignedDate": "Assigned Date", "createdDate": "Created Date", "updatedDate": "Updated Date", "outfitter": "Outfitter", "clientName": "Client Name", "phoneNumber": "Phone Number", "email": "Email", "numberOfAdults": "Number of Adults", "numberOfChildren": "Number of Children", "importantNote": "Important Note", "unassigned": "Unassigned", "noRequestDetails": "No request details available.", "quotations": "Quotations", "uploadNewQuotation": "Upload New Quotation", "viewQuotationHistory": "View Quotation History", "viewEstimatedPrice": "View Estimated Price", "price": "Price", "taxes": "Taxes", "amount": "Amount", "totalPriceBeforeTax": "Total Price Before Tax", "totalFinalPrice": "Total Final Price", "noItemsInRequest": "No items in the request.", "titleInternalNote": "Internal Notes", "addNotePlaceholder": "Add a new internal note", "addNoteButton": "Add Note", "viewNoteHistory": "View Internal Note History", "noNotesAvailable": "No notes available", "successMessage": "Note created successfully!", "writtenBy": "Written by", "updatedBy": "Updated by", "titleExternalMessage": "External Messages", "sendMessagePlaceholder": "Write a message to the client", "sendMessageButton": "Send", "viewMessageHistory": "View External Message History", "noMessagesAvailable": "No messages available", "clientMessagePrefix": "Client -", "adminMessagePrefix": "Admin -", "successMessageExternal": "Message sent successfully!", "noteCreatedSuccess": "Note created successfully!", "on": "on", "depositSchedule": "Deposit Schedule", "currency": "<PERSON><PERSON><PERSON><PERSON>", "totalAfterTax": "Total After Tax", "dueDate": "Due Date", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "addDeposit": "<PERSON><PERSON>", "totalAmount": "Total Amount", "quotationFileHistory": {"id": "Id", "filePath": "File Path", "uploadedAt": "Uploaded At", "uploadedBy": "Uploaded By", "download": "Download", "noHistoryFound": "No quotation file history found"}}}, "speciesPage": {"title": "Species", "subTitle": "Species List", "speciesUpdated": "Species has been updated successfully", "speciesCreated": "Species has been created successfully", "species": "Species", "updateSpeciesDetails": "Update the species details", "addNewSpecies": "Add a new species", "editTitle": "Update the species", "addNewTitle": "Add a new species", "speciesDetailPage": {"title": "Species Details"}, "tabs": {"tab1Title": "General Information", "tab2Title": "Image"}, "imageTab": {"mainImageSuccessMessage": "Species image has been set successfully"}}, "packagesPricesPage": {"title": "Packages Prices", "subtitle": "Packages List", "packageLoadError": "Failed to load packages", "packageInfo": "Package Information", "pricingGroupPlaceholder": "Select a pricing group", "groupPlaceholder": "Select a group"}, "productsPricesPage": {"title": "Products Prices", "subtitle": "Products List", "productLoadError": "Failed to load products", "productInfo": "Product Information"}, "servicePricesPage": {"title": "Service Prices", "subtitle": "Service List", "serviceLoadError": "Failed to load services", "serviceInfo": "Service Information", "fullDayPrice": "Full Day Price", "fullDayPricePlaceholder": "Enter Full Day Price", "halfDayPrice": "Half Day Price", "halfDayPricePlaceholder": "Enter Half Day Price", "outfitterDetailText": " Today Price List", "addService": "Add Service", "accommodationPriceList": "Accommodation Price List", "addNewAccommodation": "Add New Accommodation", "noAccommodation": "No Accommodation Associated", "noAccommodationDescription": "There are currently no accommodation associated with this outfitter.", "noAccommodationHint": "Adding accommodation to your outfitter helps potential guests understand the available options and make informed decisions.", "serviceUpdatedSuccessMessage": "This service has been updated successfully", "serviceFreeAgreement": "* Check the box below if you want to offer this service at no charge.", "serviceFreeAgreementFooter": "Update price at this section will be applied to all accommodations of this outfitter."}, "roomCategoryPricesPage": {"title": "Room Category Prices", "subtitle": "Room Category List", "roomCategoryLoadError": "Failed to load room categories", "roomCategoryInfo": "Room Category Information"}, "outfitterPage": {"title": "Outfitter", "subtitle": "Outfitter List", "tabs": {"tab1Title": "Corporate Information", "tab2Title": "Territory", "tab3Title": "Conservation Info", "tab4Title": "Services", "tab5Title": "Accommodations"}, "corporateInfoTab": {"title": "Corporate Information", "description": "All detail information about your outfitter.", "outfitterSuccessMessageP1": "Outfitter has been", "outfitterSuccessMessageP2": " successfully in our system.", "corporateNameLabel": "Corporate Name", "corporateNamePlaceholder": "Enter Corporate Name", "licenseNumberLabel": "License Number", "licenseNumberPlaceholder": "Enter License Number", "phoneNumberLabel": "Phone Number", "phoneNumberPlaceholder": "Enter Phone Number", "latitudeLabel": "Latitude", "latitudePlaceholder": "Enter Latitude", "longitudeLabel": "Longitude", "longitudePlaceholder": "Enter Longitude", "territoryLabel": "Territory", "territoryPlaceholder": "Select Territory", "symbolLabel": "Symbol", "symbolPlaceholder": "Enter Symbol", "addressLabel": "Address", "addressPlaceholder": "Enter Address", "cityLabel": "City", "cityPlaceholder": "Enter City", "stateLabel": "State", "statePlaceholder": "Enter State", "postalCodeLabel": "Postal Code", "postalCodePlaceholder": "Enter Postal Code", "provinceLabel": "Province", "provincePlaceholder": "Select Province", "countryLabel": "Country", "countryPlaceholder": "Select Country", "mailingAddressLabel": "Mailing Address"}, "territoryTab": {"title": "Territory Information", "description": "All detail information about your outfitter territory.", "outfitterSuccessMessageP1": "Outfitter has been", "outfitterSuccessMessageP2": " successfully in our system.", "descriptionLabel": "Description", "descriptionPlaceholder": "Enter Description", "territorySizeKmLabel": "Territory Size (km)", "territorySizeKmPlaceholder": "Enter Territory Size (km)", "lakeAreaKmLabel": "Lake Area (km)", "lakeAreaKmPlaceholder": "Enter Lake Area (km)", "riversAreaKmLabel": "Rivers Area (km)", "riversAreaKmPlaceholder": "Enter Rivers Area (km)", "numFishingZonesLabel": "Number of Fishing Zones", "numFishingZonesPlaceholder": "Enter Number of Fishing Zones", "numHuntingZonesLabel": "Number of Hunting Zones", "numHuntingZonesPlaceholder": "Enter Number of Hunting Zones"}, "conservationInfoTab": {"title": "Conservation Information", "description": "All detail information about your outfitter conservation.", "outfitterSuccessMessageP1": "Outfitter has been", "outfitterSuccessMessageP2": " successfully in our system.", "detailsLabel": "Details", "detailsPlaceholder": "Enter Details"}, "servicesTab": {"title": "Services", "description": "All detail information about your outfitter services.", "outfitterSuccessMessage": "Outfitter services has been updated successfully", "servicesLabel": "Services", "servicesPlaceholder": "Select Services"}, "accommodationsTab": {"title": "Accommodations", "description": "All detail information about your outfitter accommodations."}}, "seasonPage": {"seasonTitle": "Season", "seasonListTitle": "List of seasons", "season": "Season", "updateSeasonDetails": "Update the season details", "addNewSeasonType": "Add a new season type", "seasonCreated": "Season has been created successfully", "seasonUpdated": "Season has been updated successfully"}, "territoryPage": {"territoriesTitle": "Territoires", "territoriesListTitle": "List of territories", "cluster": "Cluster", "territory": "Territory", "updateTerritoryDetails": "Update the territory details", "addNewTerritory": "Add a new territory", "territoryCreated": "The territory has been created successfully", "territoryUpdated": "The territory has been updated successfully"}, "roomTypePage": {"roomTypeTitle": "Room Type", "roomTypeListTitle": "List of Room Type", "roomType": "Room Type", "updateRoomTypeDetails": "Update room type details", "addNewRoomType": "Add a new room type", "roomTypeCreated": "The room type has been successfully created", "roomTypeUpdated": "The room type has been successfully updated"}, "mediaTypePage": {"mediaTypeTitle": "Media Type", "mediaTypeListTitle": "List of Media Type", "mediaType": "Media Type", "updateMediaTypeDetails": "Update media type details", "addNewMediaType": "Add a new media type", "mediaTypeCreated": "The media type has been successfully created", "mediaTypeUpdated": "The media type has been successfully updated"}, "clusterPage": {"clusterTitle": "Cluster", "clusterListTitle": "List of Cluster", "cluster": "Cluster", "updateClusterDetails": "Update cluster details", "addNewCluster": "Add a new cluster", "clusterCreated": "The cluster has been successfully created", "clusterUpdated": "The cluster has been successfully updated"}, "buildingTypePage": {"buildingTypeTitle": "Building Type", "buildingTypeListTitle": "List of Building Type", "buildingType": "Building Type", "updateBuildingTypeDetails": "Update building type details", "addNewBuildingType": "Add a new building type", "buildingTypeCreated": "The building type has been successfully created", "buildingTypeUpdated": "The building type has been successfully updated"}, "activityTypePage": {"activityTypeTitle": "Activity Type", "activityTypeListTitle": "List of Activity Type", "activityType": "Activity Type", "updateActivityTypeDetails": "Update activity type details", "addNewActivityType": "Add a new activity type", "activityTypeCreated": "The activity type has been successfully created", "activityTypeUpdated": "The activity type has been successfully updated"}, "departmentPage": {"departmentTitle": "Department", "departmentListTitle": "List of Departments", "department": "Department", "updateDepartmentDetails": "Update department details", "addNewDepartment": "Add a new department", "departmentCreated": "Department has been successfully created", "departmentUpdated": "Department has been successfully updated"}, "amenityPage": {"amenityTitle": "Amenity", "amenityListTitle": "List of Amenities", "amenity": "Amenity", "updateAmenityDetails": "Update amenity details", "addNewAmenity": "Add a new amenity", "amenityCreated": "The amenity has been successfully created", "amenityUpdated": "The amenity has been successfully updated"}, "bedConfigPage": {"bedConfigTitle": "Bed Configuration", "bedConfigListTitle": "List of Bed Configurations", "bedConfig": "Bed Configuration", "updateBedConfigDetails": "Update bed configuration details", "addNewBedConfig": "Add a new bed configuration", "bedConfigCreated": "The bed configuration has been successfully created", "bedConfigUpdated": "The bed configuration has been successfully updated"}, "equipmentPage": {"equipmentTitle": "Equipment", "equipmentListTitle": "List of Equipment", "equipment": "Equipment", "updateEquipmentDetails": "Update equipment details", "addNewEquipment": "Add a new equipment", "equipmentCreated": "The equipment has been successfully created", "equipmentUpdated": "The equipment has been successfully updated"}, "equipmentCategoryPage": {"equipmentCategoryTitle": "Equipment Category", "equipmentCategoryListTitle": "List of Equipment Categories", "equipmentCategory": "Equipment Category", "updateEquipmentCategoryDetails": "Update equipment category details", "addNewEquipmentCategory": "Add a new equipment category", "equipmentCategoryCreated": "The equipment category has been successfully created", "equipmentCategoryUpdated": "The equipment category has been successfully updated"}, "interestPage": {"interestTitle": "Interest", "interestListTitle": "List of Interests", "interest": "Interest", "updateInterestDetails": "Update interest details", "addNewInterest": "Add a new interest", "interestCreated": "The interest has been successfully created", "interestUpdated": "The interest has been successfully updated"}, "servicePage": {"serviceTitle": "Service", "serviceListTitle": "List of Services", "service": "Service", "updateServiceDetails": "Update service details", "addNewService": "Add a new service", "serviceCreated": "The service has been successfully created", "serviceUpdated": "The service has been successfully updated"}, "specieClassificationPage": {"specieClassificationTitle": "Specie Classification", "specieClassificationListTitle": "List of Specie Classifications", "specieClassification": "Specie Classification", "updateSpecieClassificationDetails": "Update specie classification details", "addNewSpecieClassification": "Add a new specie classification", "specieClassificationCreated": "The specie classification has been successfully created", "specieClassificationUpdated": "The specie classification has been successfully updated"}, "mealPlanTypePage": {"mealPlanTypeTitle": "Meal Plan Type", "mealPlanTypeListTitle": "List of Meal Plan Types", "mealPlanType": "Meal Plan Type", "updateMealPlanTypeDetails": "Update meal plan type details", "addNewMealPlanType": "Add a new meal plan type", "mealPlanTypeCreated": "The meal plan type has been successfully created", "mealPlanTypeUpdated": "The meal plan type has been successfully updated"}, "taxesPage": {"title": "Taxes", "subtitle": "Taxes List", "addNewTax": "Add a new tax", "updateTaxDetails": "Update tax details", "taxRate": "Tax Rate", "taxCreated": "The tax has been successfully created", "taxUpdated": "The tax has been successfully updated", "taxNameLabel": "Tax Name", "taxNamePlaceholder": "Enter tax name", "taxRateLabel": "Tax Rate", "taxRatePlaceholder": "Enter tax rate", "taxDescriptionLabel": "Tax Description", "taxDescriptionPlaceholder": "Enter tax description", "submitToLabel": "Submit To", "submitToPlaceholder": "Enter where to submit to", "isActiveLabel": "Is Active", "isActivePlaceholder": "Select is active"}, "usersPage": {"title": "Users", "subtitle": "Users List", "addNewEmployee": "Add a new employee", "addNewClient": "Add a new client", "selectRole": "Select role", "updateRole": "Update role", "selectNewRole": "Select a role from the list", "cannotChangeClientRole": "You cannot change the role of a client"}, "employeePage": {"title": "Employee", "subtitle": "Employee List", "addNewEmployee": "Add a new employee", "updateEmployeeDetails": "Update employee details", "employeeCreated": "The employee has been successfully created", "employeeUpdated": "The employee has been successfully updated", "employeeManagement": "Employee Management", "createEmployee": "Create Employee", "updateEmployee": "Update Employee", "form": {"firstNameLabel": "First Name", "firstNamePlaceholder": "Enter first name", "lastNameLabel": "Last Name", "lastNamePlaceholder": "Enter last name", "addressLabel": "Address", "addressPlaceholder": "Enter address", "cityLabel": "City", "cityPlaceholder": "Enter city", "provinceLabel": "Province", "provincePlaceholder": "Select province", "departmentLabel": "Department", "departmentPlaceholder": "Select department", "postalCodeLabel": "Postal Code", "postalCodePlaceholder": "Enter postal code", "emergencyContactLabel": "Emergency Contact", "emergencyContactPlaceholder": "Enter emergency contact", "emergencyPhoneNumberLabel": "Emergency Phone Number", "emergencyPhoneNumberPlaceholder": "Enter emergency phone number", "workEmailLabel": "Work Email", "workEmailPlaceholder": "Enter work email", "workPhoneNumberLabel": "Work Phone Number", "workPhoneNumberPlaceholder": "Enter work phone number", "statusLabel": "Status", "statusPlaceholder": "Select status", "preferredLangLabel": "Preferred Lang", "preferredLangPlaceholder": "Select preferred lang", "salutationLabel": "Salutation", "salutationPlaceholder": "Select salutation", "genderLabel": "Gender", "genderPlaceholder": "Select gender", "birthDateLabel": "Birth Date", "birthDatePlaceholder": "Select birth date", "mobileNumberLabel": "Mobile Number", "mobileNumberPlaceholder": "Enter mobile number", "personalEmailLabel": "Personal Email", "personalEmailPlaceholder": "Enter personal email"}}, "ErrorPage": {"otherErrorsMessage": "Something's went wrong", "notFoundMessage": "Sorry, the page you are looking for doesn't exist.", "goBackText": "Go Back", "goHomeText": "Back to Home"}}