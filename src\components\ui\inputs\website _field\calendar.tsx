'use client';

import * as React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { DayPicker } from 'react-day-picker';

import { cn } from '@/lib/utils';
import { buttonVariants } from '@/components/ui/button';

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn('p-4', className)}
      classNames={{
        months: 'grid grid-cols-2 gap-8', // Display months in a 2-column grid
        month: 'space-y-6',
        caption: 'flex justify-center pt-2 relative items-center',
        caption_label: 'text-xl font-medium',
        nav: 'space-x-2 flex items-center',
        nav_button: cn(
          buttonVariants({ variant: 'outline' }),
          'h-12 w-12 bg-transparent p-0 opacity-50 hover:opacity-100',
        ),
        nav_button_previous: 'absolute left-2',
        nav_button_next: 'absolute right-2',
        table: 'w-full border-collapse space-y-2',
        head_row: 'flex',
        head_cell: 'text-gray-500 rounded-md w-16 font-normal text-md',
        row: 'flex w-full mt-3',
        cell: 'h-14 w-14 text-center text-md p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20',
        day: cn(
          buttonVariants({ variant: 'ghost' }),
          'h-14 w-14 p-0 font-normal text-md aria-selected:opacity-100',
        ),
        day_selected:
          'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',
        day_today: 'bg-accent text-accent-foreground',
        day_outside: 'text-gray-500 opacity-50',
        day_disabled: 'text-gray-500 opacity-50',
        day_range_middle:
          'aria-selected:bg-accent aria-selected:text-accent-foreground',
        day_hidden: 'invisible',
        ...classNames,
      }}
      components={{
        IconLeft: ({}) => <ChevronLeft className="h-8 w-8" />,
        IconRight: ({}) => <ChevronRight className="h-8 w-8" />,
      }}
      numberOfMonths={12}
      {...props}
    />
  );
}

Calendar.displayName = 'Calendar';

export { Calendar };
