import * as z from 'zod';

export const ShowContactSchema = z.object({
  contactTypeId: z
    .string()
    .min(1, { message: 'Contact type is required' })
    .transform(Number),
  locationId: z
    .number({
      required_error: 'Location ID is required',
      invalid_type_error: 'Location ID must be a number',
    })
    .nullable(),
  companyId: z.string().optional().nullable(),
  firstName: z.string().min(1, 'First name is required').nullable(),
  lastName: z.string().min(1, 'Last name is required').nullable(),
  email: z.string().email('Invalid email address').optional().nullable(),
  telephone: z.string().optional().nullable(),
  ext: z.string().optional().nullable(),
  cellphone: z.string().optional().nullable(),
  isArchived: z.boolean().default(false),
});

export type ShowContactData = z.infer<typeof ShowContactSchema>;
