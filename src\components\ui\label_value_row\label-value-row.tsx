import { ReactNode } from 'react';

import { cn } from '@/lib/utils';

interface ILabelValueRow {
  title: string;
  value: string | ReactNode;
  reverse?: boolean;
  direction?: 'row' | 'column';
  gap?: number;
}

function LabelValueRow({
  title,
  value,
  reverse,
  direction = 'row',
  gap = 8,
}: ILabelValueRow) {
  return (
    <div
      className={cn(
        'text-pair flex flex-col item-start ',
        reverse ? 'flex-row-reverse' : '',
        direction == 'column' ? 'sm:flex-col' : 'sm:flex-row',
      )}
      style={{
        gap: gap,
      }}
    >
      <span className="font-bold text-[15px] min-w-[200px] overflow-hidden text-ellipsis     ">
        {title}
      </span>
      {typeof value === 'string' ? (
        <span className="font-normal text-[14px]  overflow-hidden text-ellipsis   ">
          {value}
        </span>
      ) : (
        value
      )}
    </div>
  );
}

export default LabelValueRow;
