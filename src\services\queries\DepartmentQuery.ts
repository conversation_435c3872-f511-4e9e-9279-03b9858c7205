import { BriefDataCode } from '@/models/BriefData';
import fetcher from './fetcher';

const DepartmentQuery = {
  tags: ['Department'] as const,

  getAll: async () => fetcher<BriefDataCode[]>('Department'),

  get: async (id: number) => fetcher<BriefDataCode>(`Department/get/${id}`),

  add: async (data: { name: string }) =>
    fetcher<boolean>('Department/Add', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  update: (id: number) => async (data: { name: string }) =>
    fetcher<boolean>(`Department/Update/${id}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),
};

export default DepartmentQuery;
