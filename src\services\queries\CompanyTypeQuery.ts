import type {
  CompanyType,
  CompanyTypeInList,
  CompanyTypeCreateRequest,
  CompanyTypeUpdateRequest,
} from '@/models/CompanyType';
import fetcher from './fetcher';

const CompanyTypeQuery = {
  tags: ['CompanyType'] as const,

  getAll: async () => fetcher<CompanyTypeInList[]>('CompanyType'),

  getOne: async (id: number) => fetcher<CompanyType>(`CompanyType/${id}`),

  create: async (data: CompanyTypeCreateRequest) =>
    fetcher<number>('CompanyType', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),

  update: (id: number) => async (data: CompanyTypeUpdateRequest) =>
    fetcher<void>(`CompanyType/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),

  delete: async (id: number) =>
    fetcher<boolean>(`CompanyType/${id}`, {
      method: 'DELETE',
    }),
};

export default CompanyTypeQuery;
