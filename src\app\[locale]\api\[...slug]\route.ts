import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';

import { Tokens } from '@/models/Auth';

async function modify(
  request: Request,
  context: { params: Promise<{ slug: string[]; locale: string }> },
) {
  // Await params before destructuring
  const { locale, slug } = await context.params;

  const searchParams = Array.from(new URL(request.url).searchParams.entries())
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

  // Fix: await the cookies() call
  const cookiesStore = await cookies();
  const auth = JSON.parse(cookiesStore.get('AuthStore')?.value ?? '{}')
    ?.state as Tokens | undefined;

  const fullUrl = `${process.env.API_BASE_URL}/${slug.join('/')}?${searchParams}`;

  const requestHeaders = new Headers(request.headers);
  const headers = {
    'content-type': requestHeaders.get('content-type'),
    Authorization: requestHeaders.get('Authorization'),
    'accept-language': locale,
    ...(auth?.accessToken
      ? { Authorization: `Bearer ${auth.accessToken}` }
      : {}),
  };

  const contentType = requestHeaders.get('content-type');
  const isFormData = contentType?.includes('multipart/form-data');
  const respond = await fetch(fullUrl, {
    body: contentType
      ? isFormData
        ? await request.formData()
        : JSON.stringify(await request.json())
      : undefined,
    method: request.method,
    headers: Object.fromEntries(
      Object.entries(headers).filter(
        ([_, value]) => value !== undefined && value !== '',
      ),
    ) as Record<string, string>,
    mode: request.mode,
    integrity: request.integrity,
    redirect: request.redirect,
    referrer: request.referrer,
    referrerPolicy: request.referrerPolicy,
  });
  const eencodedJson = await respond.text();
  return new Response(eencodedJson, {
    headers: { 'content-type': 'application/json' },
    status: respond.status,
    statusText: respond.statusText,
  });
}

// Your GET function already has the correct type
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ slug: string[]; locale: string }> },
) {
  // Await params before destructuring
  const { locale, slug } = await context.params;

  const searchParams = Array.from(new URL(request.url).searchParams.entries())
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

  // Fix: await the cookies() call
  const cookiesStore = await cookies();
  const auth = JSON.parse(cookiesStore.get('AuthStore')?.value ?? '{}')
    ?.state as Tokens | undefined;

  const fullUrl = `${process.env.API_BASE_URL}/${slug.join('/')}?${searchParams}`;

  const requestHeaders = new Headers(request.headers);
  const headers = {
    'content-type': requestHeaders.get('content-type'),
    Authorization: requestHeaders.get('Authorization'),
    'accept-language': locale,
    ...(auth?.accessToken
      ? { Authorization: `Bearer ${auth.accessToken}` }
      : {}),
  };
  const respond = await fetch(fullUrl, {
    ...request,
    headers: Object.fromEntries(
      Object.entries(headers).filter(
        ([_, value]) => value !== undefined && value !== '' && value !== null,
      ),
    ) as Record<string, string>,
  });
  const eencodedJson = await respond.text();
  return new Response(eencodedJson, {
    headers: { 'content-type': 'application/json' },
    status: respond.status,
    statusText: respond.statusText,
  });
}

// Export other methods
export const POST = modify;
export const PATCH = modify;
export const DELETE = modify;
export const PUT = modify;
