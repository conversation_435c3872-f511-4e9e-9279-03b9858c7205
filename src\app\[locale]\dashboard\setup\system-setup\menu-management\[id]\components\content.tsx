'use client';
import AppLayout from '@/components/ui/app_layout';
import Suspense from '@/components/ui/Suspense';
import { useQuery } from '@tanstack/react-query';
import MenuQuery from '@/services/queries/MenuQuery';
import { getMenuName } from '@/models/MenuItem';
import MenuForm from './menu_form';
interface IContent {
  id?: number;
}
export default function Content({ id }: IContent) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: [...MenuQuery.tags, { id: id }],
    queryFn: () => MenuQuery.get(id!),
    enabled: id != undefined,
  });
  const {
    isLoading: menusLoading,
    isError,
    data: menuItems,
  } = useQuery({
    queryKey: [...MenuQuery.tags],
    queryFn: () => MenuQuery.getAll(),
  });
  return (
    <Suspense isLoading={(isLoading && !isPaused) || menusLoading}>
      <AppLayout
        items={[
          {
            title: `Menu Management`,
            link: `/dashboard/setup/system-setup/menu-management`,
          },
          {
            title:
              id && menuItems?.find((m) => m.id == id)
                ? getMenuName(menuItems.find((m) => m.id == id)!)
                : 'Add Menu item',
            link:
              `/dashboard/setup/system-setup/menu-management/` + (id ?? 'add'),
          },
        ]}
      >
        <MenuForm data={data} id={id} isDashboard={true} />
      </AppLayout>
    </Suspense>
  );
}
