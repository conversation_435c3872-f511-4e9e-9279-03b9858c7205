import { IconType } from 'react-icons/lib';

import { PermissionKey } from './Permission';

interface Menu {
  name: string;
  link?: string;
  newTab?: boolean;
  icon?: IconType;
  children?: Menu[];
  permission?: PermissionKey;
}
export type { Menu };

export type MenuItem = {
  menuItemId?: number;
  menuItemLevelId?: number;
  link?: string;
  newTab?: boolean;
  children?: Menu[];
  permissionKey?: string;
  permission?: PermissionKey;
  title: string;
  path: string;
  icon?: string | IconType;
  displayOrder: number;
  section: string;
  submenu?: MenuItem[];
};
