import {
  BasicRoleGroupDetail,
  RoleGroupData,
  RoleInGroup,
  SetRoleGroupRolesData,
} from '@/models/RoleGroup';
import fetcher from './fetcher';

const RoleGroupQuery = {
  tags: ['RoleGroups'] as const,
  getAll: async () => fetcher<BasicRoleGroupDetail[]>(`RoleGroups`),
  getOne: async (id: number) =>
    fetcher<BasicRoleGroupDetail>(`RoleGroups/${id}`),
  create: async (data: RoleGroupData) =>
    fetcher<number>(`RoleGroups`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),
  update: (id: number) => async (data: RoleGroupData) =>
    fetcher<boolean>(`RoleGroups/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),
  getRoles: async (id: number) =>
    fetcher<RoleInGroup[]>(`RoleGroups/${id}/roles`),
  setRoles: (id: number) => async (data: SetRoleGroupRolesData) =>
    fetcher<boolean>(`RoleGroups/${id}/roles`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),
};

export default RoleGroupQuery;
