'use client';
import { ScrollA<PERSON>, ScrollBar } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { useTabItems } from '@/utils/menu-util';
import { useRouter } from 'next/navigation';
interface Props {
  locked?: boolean;
}
export default function TabMenu({ locked }: Props) {
  const { tabs, isLoading } = useTabItems();
  const { push } = useRouter();
  return (
    tabs &&
    tabs.length > 0 && (
      <ScrollArea className="flex items-center justify-start rounded-sm bg-muted px-1  py-2 whitespace-nowrap  text-muted-foreground ">
        {tabs.map(({ name, url, current }, index) => (
          <div
            className={cn(
              'inline-flex items-center cursor-pointer justify-center whitespace-nowrap rounded-sm px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow',
              {
                'cursor-not-allowed': index > 0 && locked,
              },
            )}
            data-state={current ? 'active' : undefined}
            key={name}
            onClick={(e) => {
              e.preventDefault();
              if (current) return;
              push(url);
            }}
          >
            {name}
          </div>
        ))}
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    )
  );
}
