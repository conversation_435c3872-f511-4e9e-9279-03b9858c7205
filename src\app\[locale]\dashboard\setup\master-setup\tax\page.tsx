import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import TaxQuery from '@/services/queries/TaxQuery';
import TaxTable from './components/tax_table';

export const metadata: Metadata = {
  title: 'Goodkey | Tax',
};

export default async function Cluster() {
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery({
    queryKey: TaxQuery.tags,
    queryFn: () => TaxQuery.getAll(),
  });

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'Master-Setup', link: '/dashboard/setup/master-setup' },
        { title: 'Tax Matrix', link: '/dashboard/setup/master-setup/tax' },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <TaxTable />
      </HydrationBoundary>
    </AppLayout>
  );
}
