'use client';

import {
  CreditCard,
  FileText,
  Package,
  Users,
  Bar<PERSON>hart,
  Settings,
  ImageIcon,
  ShoppingCart,
  MessageSquareQuote,
} from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';

interface EventSidebarProps {
  eventId: string;
  activeItem?: string;
}

export function EventSidebar({ eventId, activeItem }: EventSidebarProps) {
  const pathname = usePathname();
  const router = useRouter();

  const menuItems = [
    {
      name: 'RFQs',
      icon: MessageSquareQuote,
      url: `/dashboard/event/${eventId}/rfqs`,
    },
    {
      name: 'ORDERS',
      icon: ShoppingCart,
      url: `/dashboard/event/${eventId}/orders`,
    },
    {
      name: 'ORDER FORMS',
      icon: FileText,
      url: `/dashboard/event/${eventId}/order-forms`,
    },
    {
      name: 'SHOW PACKAGES',
      icon: Package,
      url: `/dashboard/event/${eventId}/show-packages`,
    },
    {
      name: 'EXHIBITORS',
      icon: Users,
      url: `/dashboard/event/${eventId}/exhibitors`,
    },
    {
      name: 'LISTS & REPORTS',
      icon: Bar<PERSON>hart,
      url: `/dashboard/event/${eventId}/reports`,
    },
    {
      name: 'SHOW MANAGEMENT',
      icon: Settings,
      url: `/dashboard/event/${eventId}`,
    },
    {
      name: 'GRAPHICS',
      icon: ImageIcon,
      url: `/dashboard/event/${eventId}/graphics`,
    },
    {
      name: 'PAYMENTS',
      icon: CreditCard,
      url: `/dashboard/event/${eventId}/payments`,
    },
  ];

  const handleItemClick = (url: string) => {
    router.push(url);
  };

  const isActive = (name: string) => {
    return name === activeItem;
  };

  return (
    <div className="w-full md:w-64 bg-white rounded-md border border-slate-200">
      <div className="p-2">
        {menuItems.map((item) => (
          <div key={item.name} className="mb-1">
            <button
              className={`w-full flex items-center p-2 rounded-md text-left ${
                isActive(item.name)
                  ? 'bg-slate-50 text-[#00646C]'
                  : 'text-slate-600 hover:bg-slate-50 hover:text-[#00646C]'
              }`}
              onClick={() => handleItemClick(item.url)}
            >
              <item.icon className="h-4 w-4 mr-2" />
              <span>{item.name}</span>
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
