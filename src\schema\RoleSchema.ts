import * as z from 'zod';

export const RoleSchema = z.object({
  name: z
    .string()
    .min(3, { message: 'Name must be at least 3 characters long' })
    .max(50, { message: 'Name must be at most 50 characters long' }),
  description: z
    .string()
    .min(3, { message: 'Description must be at least 3 characters long' })
    .max(255, {
      message: 'Description must be at most 255 characters long',
    }),
  level: z.coerce.number().min(1, { message: 'Level must be at least 1' }),
});

export type RoleData = z.infer<typeof RoleSchema>;
