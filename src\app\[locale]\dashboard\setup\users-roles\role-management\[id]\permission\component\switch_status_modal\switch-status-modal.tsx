import { useMutation } from '@tanstack/react-query';

import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';
import { ClearIcon, LoadingIcon, SaveIcon } from '@/assets/Icons';
import UsersQuery from '@/services/queries/UsersQuery';
import { getQueryClient } from '@/utils/query-client';

interface ISwitchStatusModal {
  close: () => void;
  isChecked: boolean;
  permissionId: number;
}

export default function SwitchStatusModal({
  close,
  isChecked,
  permissionId,
}: ISwitchStatusModal) {
  const { mutate, isPending } = useMutation({
    mutationKey: UsersQuery.tags,
    mutationFn: UsersQuery.switchStatus,
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({ queryKey: UsersQuery.tags });

      close();
    },
  });

  const titleText = isChecked ? 'Activate permission' : 'Deactivate permission';
  const descriptionText = isChecked
    ? 'Activate the permissions for this role within the system.'
    : 'Deactivate the permissions for this role within the system.';
  const confirmButtonText = isChecked ? 'Activate' : 'Deactivate';
  const confirmButtonVariant = isChecked ? 'primary' : 'destructive';

  return (
    <AlertDialog open={true} onOpenChange={close}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{titleText}</AlertDialogTitle>
        </AlertDialogHeader>
        <div className="py-2 text-slate-700">{descriptionText}</div>
        <AlertDialogFooter>
          <AlertDialogCancel asChild>
            <Button variant="outline" disabled={isPending} iconName="ClearIcon">
              Cancel
            </Button>
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              onClick={() => mutate(permissionId)}
              disabled={isPending}
              variant={confirmButtonVariant}
              iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
              iconProps={{
                className: 'text-white',
              }}
            >
              {isPending ? 'Please wait...' : confirmButtonText}
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
