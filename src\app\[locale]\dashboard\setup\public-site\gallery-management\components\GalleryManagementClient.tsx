'use client';
import { useState } from 'react';
import CategoryManagementSection from './CategoryManagementSection';
import SubcategoryManagementSection from './SubcategoryManagementSection';
import ImageManagementSection from './ImageManagementSection';

const TABS = [
  { label: 'Images', value: 'Images' },
  { label: 'Subcategories', value: 'Subcategories' },
  { label: 'Categories', value: 'Categories' },
] as const;

export default function GalleryManagementClient() {
  const [tab, setTab] = useState<(typeof TABS)[number]['value']>('Images');

  return (
    <div className="flex w-full ">
      {/* Sidebar */}
      <div className="w-48  border-r border-slate-200 pr-4 mr-6 space-y-1 bg-white py-4">
        {TABS.map(({ label, value }) => (
          <button
            key={value}
            className={`w-full text-left px-3 py-2 text-sm font-medium rounded-md transition-colors
              ${tab === value ? 'bg-[#00646C]/10 text-[#00646C] border-l-2 border-[#00646C]' : 'text-slate-600 hover:bg-slate-100'}`}
            onClick={() => setTab(value)}
          >
            {label}
          </button>
        ))}
      </div>
      {/* Content */}
      <div className="flex-1  overflow-hidden ">
        {tab === 'Images' && <ImageManagementSection />}
        {tab === 'Subcategories' && <SubcategoryManagementSection />}
        {tab === 'Categories' && <CategoryManagementSection />}
      </div>
    </div>
  );
}
