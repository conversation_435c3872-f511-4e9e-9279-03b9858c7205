import Link from 'next/link';
import { ReactNode } from 'react';

import { themeColors } from '@/assets/color';
import { cn } from '@/lib/utils';

interface IExternalOption {
  Icon: ReactNode;
  backgroundColor?: string;
  title: string;
  href?: string;
}

function ExternalOption({
  Icon,
  title,
  backgroundColor = themeColors.warm_orange,
  href,
}: IExternalOption) {
  return href ? (
    <Link
      href={href}
      target="_blank"
      rel="noreferrer"
      className={cn(
        ' w-full px-5 flex flex-row gap-5 items-center cursor-pointer hover:brightness-105 hover:scale-105 focus:scale-105 active:scale-100 transition-all ',
      )}
    >
      {Icon}
      <div
        className="flex px-8 self-stretch items-center justify-start rounded-sm flex-grow "
        style={{ backgroundColor }}
      >
        <p className="text-white text-xl font-semibold ">{title}</p>
      </div>
    </Link>
  ) : (
    <div className=" w-full  flex flex-row gap-5 items-center hover:brightness-105  transition-all ">
      {Icon}
      <div
        className="flex px-8 self-stretch items-center justify-start rounded-sm flex-grow "
        style={{ backgroundColor }}
      >
        <p className="text-white text-xl font-semibold ">{title}</p>
      </div>
    </div>
  );
}

export default ExternalOption;
