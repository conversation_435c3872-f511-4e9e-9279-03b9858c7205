import {
  DocumentFileTypeInList,
  DocumentFileTypeDetail,
  DocumentFileTypeCreateData,
  DocumentFileTypeUpdateData,
} from '@/models/DocumentFileType';
import fetcher from './fetcher';

const DocumentFileTypeQuery = {
  tags: ['DocumentFileType'] as const,

  getAll: async () => fetcher<DocumentFileTypeInList[]>('Document/filetypes'),

  getOne: async (id: number) =>
    fetcher<DocumentFileTypeDetail>(`Document/filetypes/${id}`),

  create: async (data: DocumentFileTypeCreateData) => {
    const processedData = {
      ...data,
      extensionCode: data.extensionCode.toUpperCase(),
      extension: data.extension.toLowerCase(),
    };

    return fetcher<number>('Document/filetypes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(processedData),
    });
  },

  update: (id: number) => async (data: DocumentFileTypeUpdateData) => {
    const processedData = {
      ...data,
      extensionCode: data.extensionCode.toUpperCase(),
      extension: data.extension.toLowerCase(),
    };

    return fetcher<void>(`Document/filetypes/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(processedData),
    });
  },

  delete: async (id: number) =>
    fetcher<boolean>(`Document/filetypes/${id}`, {
      method: 'DELETE',
    }),
};

export default DocumentFileTypeQuery;
