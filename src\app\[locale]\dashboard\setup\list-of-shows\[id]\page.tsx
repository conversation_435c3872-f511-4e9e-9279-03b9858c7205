import type { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import ShowFormTabs from '@/components/ShowFormTabs';

interface ShowPageProps {
  params: Promise<{
    id: string;
  }>;
}

export const metadata: Metadata = {
  title: 'Show Management | GOODKEY SHOW SERVICES LTD.',
  description:
    'Create or edit shows in the GOODKEY SHOW SERVICES LTD. system with comprehensive details and scheduling.',
  openGraph: {
    title: 'Show Management | GOODKEY SHOW SERVICES LTD.',
    description:
      'Create or edit shows in the GOODKEY SHOW SERVICES LTD. system with comprehensive details and scheduling.',
  },
};

export default async function ShowPage({ params }: ShowPageProps) {
  const { id } = await params;
  const isAddMode = id === 'add';
  const showId = isAddMode ? undefined : parseInt(id);

  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'List of Shows', link: '/dashboard/setup/list-of-shows' },
        {
          title: isAddMode ? 'Add New Show' : 'Edit Show',
          link: `/dashboard/setup/list-of-shows/${id}`,
        },
      ]}
    >
      <ShowFormTabs showId={showId} />
    </AppLayout>
  );
}
