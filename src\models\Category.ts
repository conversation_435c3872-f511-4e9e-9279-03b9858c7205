export interface CategoryDto {
  id: number;
  group?: string;
  name?: string;
  code?: string;
  displayOrder?: number;
  imagePath?: string;
  isSoldByQ?: boolean;
  isInternalProduct?: boolean;
  isAvailable?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface CategoryProperty {
  selection1?: number;
  selection2?: number;
}

export interface GroupDto {
  id: number;
  groupTypeName?: string;
  name?: string;
  code?: string;
  isAvailable?: boolean;
}
