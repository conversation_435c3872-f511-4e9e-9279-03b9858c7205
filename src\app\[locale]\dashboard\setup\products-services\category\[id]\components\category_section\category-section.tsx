'use client';

import { usePathname, useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';

interface ICategorySection {
  id?: number;
}

export const facilityTabs: {
  label: string;
  value: string;
}[] = [
  {
    label: 'General Information',
    value: '',
  },
  {
    label: 'Description',
    value: 'description',
  },
  {
    label: 'Property',
    value: 'property',
  },
];

function CategorySection({ id }: ICategorySection) {
  const router = useRouter();
  const pathname = usePathname();

  return (
    <div className="w-48 shrink-0 border-r border-slate-200 pr-4 mr-6 space-y-1">
      {facilityTabs.map(({ label, value }, index) => {
        const lastSegment = pathname.split('/').pop();
        const isActive =
          (value === '' &&
            (lastSegment === 'add' || lastSegment === id?.toString())) ||
          lastSegment === value;

        return (
          <button
            key={value}
            className={cn(
              'w-full text-left px-3 py-2 text-sm font-medium rounded-md transition-colors',
              {
                'cursor-pointer': index <= 0 || id,
                'opacity-50 cursor-not-allowed': index > 0 && !id,
                'bg-[#00646C]/10 text-[#00646C] border-l-2 border-[#00646C]':
                  isActive,
                'text-slate-600 hover:bg-slate-100': !isActive,
              },
            )}
            onClick={() => {
              if (id) {
                router.push(
                  `/dashboard/setup/products-services/category/${id}/${value}`,
                );
              }
            }}
            disabled={index > 0 && !id}
          >
            {label}
          </button>
        );
      })}
    </div>
  );
}

export default CategorySection;
