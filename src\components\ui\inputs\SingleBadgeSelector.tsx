import { CheckIcon, ChevronDown, XIcon } from 'lucide-react';
import React, { useState, useEffect, useMemo } from 'react';

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

import { Badge } from '../Badge';
import { useFormField } from '../form';

interface SingleSelectFieldProps {
  options: {
    label: string;
    value: string;
    icon?: React.ReactNode;
  }[];
  defaultValue?: string;
  onValueChange: (value: string) => void;
  disabled?: boolean;
  placeholder: string;
}

const SingleSelectField: React.FC<SingleSelectFieldProps> = ({
  options,
  defaultValue = '',
  onValueChange,
  disabled,
  placeholder,
}) => {
  const [selectedValue, setSelectedValue] = useState(defaultValue);

  useEffect(() => {
    setSelectedValue(defaultValue);
  }, [defaultValue]);

  const toggleOption = (value: string) => {
    const newValue = selectedValue === value ? '' : value;
    setSelectedValue(newValue);
    onValueChange(newValue);
  };

  const clearSelection = (event: React.MouseEvent) => {
    setSelectedValue('');
    onValueChange('');
    event.stopPropagation();
  };

  const { error } = useFormField();

  const displayValue = useMemo(() => {
    return (
      options.find((option) => option.value === selectedValue)?.label || ''
    );
  }, [selectedValue, options]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <div
          className={cn(
            'flex w-full rounded-sm border border-gray-300',
            'min-h-10 h-auto items-center justify-between',
            'focus-visible:outline-none focus-visible:border-[#1a7efb]',
            error && 'border-red-500',
            disabled && 'bg-gray-100 cursor-not-allowed',
          )}
        >
          {selectedValue ? (
            <div className="flex justify-between items-center w-full">
              <div className="flex items-center">
                <Badge variant="outline" className="m-1 bg-card">
                  {
                    options.find((option) => option.value === selectedValue)
                      ?.icon
                  }
                  {displayValue}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <XIcon
                  className="h-4 mx-2 cursor-pointer text-muted-foreground"
                  onClick={clearSelection}
                />
                <Separator
                  orientation="vertical"
                  className="flex min-h-6 h-full"
                />
                <ChevronDown className="h-4 mx-2 cursor-pointer text-muted-foreground" />
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-between w-full mx-auto">
              <span className="text-sm text-foreground mx-3">
                {placeholder}
              </span>
              <ChevronDown className="h-4 cursor-pointer text-muted-foreground mx-2" />
            </div>
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0" align="start">
        <Command>
          <CommandInput placeholder={placeholder} />
          <CommandList>
            <CommandEmpty>No results found.</CommandEmpty>
            <CommandGroup>
              {options.map((option) => {
                const isSelected = selectedValue === option.value;
                return (
                  <CommandItem
                    key={option.value}
                    onSelect={() => {
                      toggleOption(option.value);
                    }}
                  >
                    <div
                      className={cn(
                        'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                        isSelected
                          ? 'bg-primary text-primary-foreground'
                          : 'opacity-50 [&_svg]:invisible',
                      )}
                    >
                      <CheckIcon className={cn('h-4 w-4')} />
                    </div>
                    {option.icon}
                    <span>{option.label}</span>
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default SingleSelectField;
