import {
  GalleryCategory,
  GallerySubcategory,
  GalleryImage,
} from '@/models/Gallery';
import fetcher from './fetcher';
import { urlToFile } from '@/utils/file-helper';

const GalleryQuery = {
  // Categories
  getCategories: async () => fetcher<GalleryCategory[]>('gallery/categories'),
  getCategory: async (id: number) =>
    fetcher<GalleryCategory>(`gallery/categories/${id}`),
  createCategory: async (data: {
    name: string;
    description: string;
    displayOrder: number;
  }) =>
    fetcher('gallery/categories', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),
  updateCategory:
    (id: number) =>
    async (data: { name: string; description: string; displayOrder: number }) =>
      fetcher(`gallery/categories/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      }),
  deleteCategory: async (id: number) =>
    fetcher(`gallery/categories/${id}`, { method: 'DELETE' }),

  // Subcategories
  getSubcategories: async () =>
    fetcher<GallerySubcategory[]>('gallery/subcategories'),
  getCategorySubcategories: async (categoryId: number) =>
    fetcher<GallerySubcategory[]>(
      `gallery/categories/${categoryId}/subcategories`,
    ),
  createSubcategory: async (data: {
    categoryId: number;
    name: string;
    description: string;
    displayOrder: number;
  }) =>
    fetcher('gallery/subcategories', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),
  updateSubcategory:
    (id: number) =>
    async (data: {
      name: string;
      description: string;
      displayOrder: number;
      categoryId: number;
    }) =>
      fetcher(`gallery/subcategories/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      }),
  deleteSubcategory: async (id: number) =>
    fetcher(`gallery/subcategories/${id}`, { method: 'DELETE' }),

  // Images
  getImages: async () => fetcher<GalleryImage[]>('gallery/images'),
  getSubcategoryImages: async (subcategoryId: number) =>
    fetcher<GalleryImage[]>(`gallery/subcategories/${subcategoryId}/images`),
  getImage: async (id: number) => {
    const data = await fetcher<GalleryImage>(`gallery/images/${id}`);
    let image: File[] = [];
    if (data.url) {
      const file = await urlToFile(
        data.url.startsWith('/images') ? data.url : '/images' + data.url,
      );
      if (file) image = [file];
    }
    return {
      subcategoryId: data.subcategoryId,
      name: data.name,
      alt: data.alt,
      image,
      url: data.url,
      displayOrder: data.displayOrder,
    };
  },
  downloadImage: async (id: number) =>
    fetcher(`gallery/images/${id}/download`, {}, true),
  createImage: async (formData: FormData) =>
    fetcher(
      'gallery/images',
      {
        method: 'POST',
        body: formData,
      },
      true,
    ),
  updateImage: (id: number) => async (formData: FormData) =>
    fetcher(
      `gallery/images/${id}`,
      {
        method: 'PUT',
        body: formData,
      },
      true,
    ),
  deleteImage: async (id: number) =>
    fetcher(`gallery/images/${id}`, { method: 'DELETE' }),
};

export default GalleryQuery;
