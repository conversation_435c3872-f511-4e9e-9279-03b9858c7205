'use client';

import * as React from 'react';

import { cn } from '@/lib/utils';

import { Button } from '../button';
import { OptionType } from './field/field';

interface TextSwitchProps {
  items: OptionType[];

  onChange?: (value: string) => void;
  defaultValue?: string;
}
export default function TextSwitch({
  items,
  onChange,
  defaultValue,
}: TextSwitchProps) {
  const [value, setValue] = React.useState(defaultValue ?? items[0].value);
  return (
    <div
      className={cn(
        'p-0 w-fit h-fit border-border  border rounded-sm overflow-hidden self-end flex ',
      )}
    >
      {items.map((item) => (
        <Button
          key={item.value}
          className={cn(
            'rounded-sm [&:not(:last-child)]:border-r border-border cursor-pointer bg-transparent text-primary hover:bg-border',
            {
              'bg-primary text-primary-foreground hover:bg-primary':
                value === item.value,
            },
          )}
          onClick={() => {
            setValue(item.value);
            onChange?.(item.value);
          }}
        >
          {item.label}
        </Button>
      ))}
    </div>
  );
}
