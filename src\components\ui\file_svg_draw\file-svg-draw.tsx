import { useTranslations } from 'next-intl';
import { Accept } from 'react-dropzone';
import { UploadIcon } from '@/assets/Icons';

interface IFileSvgDraw {
  accept?: Accept;
}

function FileSvgDraw({
  accept = { 'image/*': ['.jpg', '.jpeg', '.png'] },
}: IFileSvgDraw) {
  const c = useTranslations('Common');
  return (
    <>
      <UploadIcon size={32} className="mb-3 text-gray-500 dark:text-gray-400" />
      <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
        <span className="font-semibold">{c('clickToUpload')}</span>
        &nbsp; {c('dragAndDrop')}
      </p>
      <p className="text-xs text-gray-500 dark:text-gray-400 uppercase">
        {Object.keys(accept)
          .map((key) => accept[key])
          .join(', ')}
      </p>
    </>
  );
}

export default FileSvgDraw;
