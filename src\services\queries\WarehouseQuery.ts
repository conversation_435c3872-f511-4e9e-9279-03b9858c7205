import { WarehouseDetailDto, WarehouseSummaryDto } from '@/models/Warehouse';
import fetcher from './fetcher';
import { WarehouseData } from '@/schema/WarehouseSchema';
import { BriefData } from '@/models/BriefData';

const WarehouseQuery = {
  tags: ['Warehouse'] as const,

  getAllType: async () => fetcher<BriefData[]>('Warehouse/GetAllType'),

  // GET: api/warehouse
  getAll: async (warehouseTypeId?: number) => {
    const query = warehouseTypeId ? `?warehouseTypeId=${warehouseTypeId}` : '';
    return fetcher<WarehouseSummaryDto[]>(`Warehouse${query}`);
  },

  // GET: api/warehouse/{id}
  get: async (id: number) => {
    return fetcher<WarehouseDetailDto>(`Warehouse/${id}`);
  },

  // POST: api/warehouse
  add: async (data: WarehouseData) => {
    return fetcher<boolean>('Warehouse', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
  },

  // PUT: api/warehouse/{id}
  update: (id: number) => async (data: WarehouseData) => {
    return fetcher<boolean>(`Warehouse/${id}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
  },
};

export default WarehouseQuery;
