import { z } from 'zod';

export const ScheduleSchema = z.object({
  name: z
    .string()
    .min(3, { message: 'Name must be at least 3 characters long' })
    .max(100, { message: 'Name must be at most 100 characters long' }),
  description: z
    .string()
    .min(3, { message: 'Description must be at least 3 characters long' })
    .max(255, { message: 'Description must be at most 255 characters long' }),
  notes: z
    .string()
    .max(500, { message: 'Notes must be at most 500 characters long' })
    .optional(),
  isActive: z.boolean().default(true),
});

export type ScheduleData = z.infer<typeof ScheduleSchema>;
