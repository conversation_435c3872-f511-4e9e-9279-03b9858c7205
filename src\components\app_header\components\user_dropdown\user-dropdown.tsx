import './style/index.scss';
import { useQuery } from '@tanstack/react-query';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { User } from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Spinner } from '@/components/ui/spinner';
import AuthQuery from '@/services/queries/AuthQuery';
import { useAuthStore } from '@/services/zustand/authStore';
import useConnectionStore from '@/services/zustand/ConnectionStore';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface IUserDropdown {}

function UserDropdown({}: IUserDropdown) {
  const { data, isLoading, isSuccess } = useQuery({
    queryKey: [AuthQuery.tags.me],
    queryFn: AuthQuery.me,
  });

  const { push } = useRouter();
  const c = useTranslations('Common');

  return isLoading ? (
    <Spinner />
  ) : isSuccess ? (
    <div className="user-dropdown">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className={cn(
              ' px-6 rounded-none text-base font-medium transition-colors',
              'text-slate-700 hover:text-slate-900 flex items-center gap-2 uppercase',
            )}
          >
            <User className="h-5 w-5 " />
            Profile
            {/* <Avatar className="h-8 w-8">
              <AvatarImage src="" />
              <AvatarFallback>
                {data.name?.split(' ')?.[0][0]} {data.name?.split(' ')?.[1][0]}
              </AvatarFallback>
            </Avatar>
            <span className="hidden md:inline uppercase">
              {data.name?.split(' ')?.[0]}
            </span> */}
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent className="w-56">
          <DropdownMenuLabel>
            <div className="px-2 py-1.5 text-sm font-normal">
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">{data.name}</p>
                <p className="text-xs leading-none text-muted-foreground">
                  {data.username}
                </p>
              </div>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <Link href="/dashboard/profile">
              <DropdownMenuItem>{c('profile')}</DropdownMenuItem>
            </Link>
          </DropdownMenuGroup>

          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => {
              useAuthStore.getState().discard();
              useConnectionStore.getState().setConnected(false);
              push('/logout');
            }}
          >
            {c('logOut')}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  ) : (
    <></>
  );
}

export default UserDropdown;
