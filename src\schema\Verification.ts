import * as z from 'zod';

import { PasswordSchema } from './common';

const VerificationSchema = z
  .object({
    password: PasswordSchema,
    confirmPassword: PasswordSchema,
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });
export type VerificationData = z.infer<typeof VerificationSchema>;
export default VerificationSchema;
