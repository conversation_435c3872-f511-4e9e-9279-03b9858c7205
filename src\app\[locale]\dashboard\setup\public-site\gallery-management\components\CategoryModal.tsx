'use client';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';

const schema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  displayOrder: z.coerce.number().min(1, 'Display order is required'),
});

type CategoryFormValues = z.infer<typeof schema>;

function FormContent({
  defaultValues,
  loading,
  onSubmit,
  onCancel,
}: {
  defaultValues?: Partial<CategoryFormValues>;
  loading?: boolean;
  onSubmit: (values: CategoryFormValues) => void;
  onCancel: () => void;
}) {
  const form = useForm<CategoryFormValues>({
    resolver: zodResolver(schema),
    defaultValues: defaultValues || {
      name: '',
      description: '',
      displayOrder: 1,
    },
  });

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <Field
          type="text"
          control={form.control}
          name="name"
          label="Name"
          required
          disabled={loading}
        />
        <Field
          type="textarea"
          control={form.control}
          name="description"
          label="Description"
          disabled={loading}
        />
        <Field
          type="number"
          control={form.control}
          name="displayOrder"
          label="Display Order"
          required
          disabled={loading}
        />
        <div className="flex justify-end gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
            iconName="ClearIcon"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="main"
            disabled={loading}
            iconName="SaveIcon"
            iconProps={{
              className: 'text-white',
            }}
          >
            Save
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default function CategoryModal({
  open,
  onOpenChange,
  onSubmit,
  initialData,
  loading,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (values: CategoryFormValues) => void;
  initialData?: Partial<CategoryFormValues>;
  loading?: boolean;
}) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="">
        <DialogHeader>
          <DialogTitle>
            {initialData ? 'Edit Category' : 'Add Category'}
          </DialogTitle>
        </DialogHeader>
        <FormContent
          defaultValues={initialData}
          loading={loading}
          onSubmit={onSubmit}
          onCancel={() => onOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
