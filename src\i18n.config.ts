// Import the correct type
import { createNavigation } from 'next-intl/navigation';

export const locales = ['en', 'fr'] as const;

export const pathnames = {
  '/': '/',
  // Add other paths as needed
};

export const localePrefix = 'never';

// Use createSharedPathnamesNavigation instead of satisfies Pathnames
export const { Link, redirect, usePathname, useRouter } = createNavigation({
  locales,
  pathnames,
});

export type AppPathnames = keyof typeof pathnames;
