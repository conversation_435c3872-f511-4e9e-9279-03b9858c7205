import { create } from 'zustand';
import { persist } from 'zustand/middleware';
interface ConnectionStore {
  connectionStatus: 'Connected' | 'Disconnected' | 'expired';
  isConnected: boolean;
  setConnected: (
    isConnected: boolean,
    connectionStatus?: 'Connected' | 'Disconnected' | 'expired',
  ) => void;
}
const useConnectionStore = create<ConnectionStore>()(
  persist(
    (set) => ({
      isConnected: false,
      connectionStatus: 'Disconnected',
      setConnected: (isConnected, connectionStatus) =>
        set({
          isConnected,
          connectionStatus:
            connectionStatus ?? (isConnected ? 'Connected' : 'Disconnected'),
        }),
    }),
    {
      name: 'ConnectionStore',
    },
  ),
);

export default useConnectionStore;
