'use client';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import GalleryQuery from '@/services/queries/GalleryQuery';
import { GallerySubcategory } from '@/models/Gallery';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import SubcategoryModal from './SubcategoryModal';
import { toast } from '@/components/ui/use-toast';

import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';

function ShowMoreCell({
  text,
  maxLength = 60,
}: {
  text: string;
  maxLength?: number;
}) {
  const [showMore, setShowMore] = useState(false);
  if (!text) return '-';
  if (text.length <= maxLength) return text;
  return (
    <span>
      {showMore ? text : text.slice(0, maxLength) + '...'}{' '}
      <button
        type="button"
        className="text-blue-600 underline text-xs ml-1"
        onClick={() => setShowMore((v) => !v)}
      >
        {showMore ? 'Show less' : 'Show more'}
      </button>
    </span>
  );
}

export default function SubcategoryManagementSection() {
  const queryClient = useQueryClient();
  const { data: categories, isLoading: loadingCategories } = useQuery({
    queryKey: ['gallery', 'categories'],
    queryFn: GalleryQuery.getCategories,
  });
  const { data: subcategories, isLoading } = useQuery({
    queryKey: ['gallery', 'subcategories'],
    queryFn: GalleryQuery.getSubcategories,
  });

  // Modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [editData, setEditData] = useState<GallerySubcategory | null>(null);

  // Mutations
  const addMutation = useMutation({
    mutationFn: GalleryQuery.createSubcategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gallery', 'subcategories'] });
      toast({ title: 'Subcategory created', variant: 'success' });
      setModalOpen(false);
    },
    onError: (e: any) =>
      toast({
        title: e.message || 'Failed to create subcategory',
        variant: 'destructive',
      }),
  });
  const editMutation = useMutation({
    mutationFn: (data: {
      id: number;
      categoryId: number;
      name: string;
      description: string;
      displayOrder: number;
    }) =>
      GalleryQuery.updateSubcategory(data.id)({
        name: data.name,
        description: data.description ?? '',
        displayOrder: data.displayOrder,
        categoryId: data.categoryId,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gallery', 'subcategories'] });
      toast({ title: 'Subcategory updated', variant: 'success' });
      setModalOpen(false);
      setEditData(null);
    },
    onError: (e: any) =>
      toast({
        title: e.message || 'Failed to update subcategory',
        variant: 'destructive',
      }),
  });

  // Table columns
  const columns = useMemo<ColumnDef<GallerySubcategory>[]>(() => {
    return generateTableColumns<GallerySubcategory>(
      {
        name: { name: 'Name', type: 'text', sortable: true },
        description: {
          name: 'Description',
          type: {
            type: 'node',
            render: ({ cell }) => <ShowMoreCell text={cell} maxLength={60} />,
          },
        },
        categoryName: { name: 'Category', type: 'text' },
        displayOrder: { name: 'Display Order', type: 'text', sortable: true },
      },
      {
        actions: {
          name: 'Actions',
          type: {
            type: 'node',
            render: ({ row }) => (
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="secondary"
                  iconName="EditIcon"
                  onClick={() => {
                    setEditData(row);
                    setModalOpen(true);
                  }}
                ></Button>
                <Button
                  variant="remove"
                  size="sm"
                  iconName="RemoveIcon"
                  onClick={() => {
                    modal(
                      ({ close }) => (
                        <MutationConfirmModal
                          close={close}
                          title="Delete Subcategory"
                          description="Are you sure you want to delete this subcategory?"
                          mutateFn={async () =>
                            GalleryQuery.deleteSubcategory(row.id)
                          }
                          mutationKey={['gallery', 'subcategories']}
                          onSuccess={() => {
                            queryClient.invalidateQueries({
                              queryKey: ['gallery', 'subcategories'],
                            });
                            toast({
                              title: 'Subcategory deleted',
                              variant: 'success',
                            });
                          }}
                          onError={(e: Error) =>
                            toast({
                              title:
                                e.message || 'Failed to delete subcategory',
                              variant: 'destructive',
                            })
                          }
                          variant="destructive"
                          confirmButtonText="Delete"
                          confirmIconName="DeleteIcon"
                          loadingIconName="LoadingIcon"
                        />
                      ),
                      DEFAULT_MODAL,
                    ).open();
                  }}
                ></Button>
              </div>
            ),
          },
        },
      },
      false,
    );
  }, []);

  // Table filters
  const filters = useMemo(
    () =>
      generateTableFilters<GallerySubcategory>({
        name: { name: 'Name', type: 'text' },
        description: { name: 'Description', type: 'text' },
        categoryName: {
          name: 'Category',
          type: {
            type: 'select',
            options: (categories || []).map((cat) => ({
              label: cat.name,
              value: cat.name,
            })),
          },
        },
      }),
    [categories],
  );

  return (
    <div>
      <DataTable
        columns={columns}
        data={subcategories || []}
        isLoading={isLoading}
        filterFields={filters}
        controls={
          <div className="flex gap-2 items-center">
            <Button
              variant="main"
              onClick={() => {
                setEditData(null);
                setModalOpen(true);
              }}
              iconName="AddIcon"
            >
              Add Subcategory
            </Button>
          </div>
        }
      />

      {/* Add/Edit Modal */}
      <SubcategoryModal
        open={modalOpen}
        onOpenChange={(open) => {
          setModalOpen(open);
          if (!open) setEditData(null);
        }}
        onSubmit={(values) => {
          if (editData) {
            editMutation.mutate({
              id: editData.id,
              ...values,
              categoryId: Number(values.categoryId),
              description: values.description ?? '',
              displayOrder: values.displayOrder,
            });
          } else {
            addMutation.mutate({
              ...values,
              categoryId: Number(values.categoryId),
              description: values.description ?? '',
              displayOrder: values.displayOrder,
            });
          }
        }}
        initialData={
          editData
            ? {
                categoryId: String(editData.categoryId),
                name: editData.name,
                description: editData.description,
                displayOrder: editData.displayOrder,
              }
            : undefined
        }
        categories={categories || []}
        loading={
          addMutation.status === 'pending' || editMutation.status === 'pending'
        }
      />
    </div>
  );
}
