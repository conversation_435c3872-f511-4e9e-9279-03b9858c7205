export async function urlToFile(url: string) {
  // Fetch the file from the URL
  const response = await fetch(url);

  // Check if the request was successful
  if (!response.ok) {
    // throw new Error(
    //   `Failed to fetch the file from the URL: ${response.statusText}`,
    // );
    return undefined;
  }

  // Get the MIME type from the Content-Type header
  const mimeType =
    response.headers.get('Content-Type') || 'application/octet-stream';

  // Get the file name from the Content-Disposition header
  let fileName = url.split('/').pop()?.split('?')[0] ?? 'unknown';
  const contentDisposition = response.headers.get('Content-Disposition');

  if (contentDisposition) {
    const match = contentDisposition.match(/filename="?(.+?)"?(\s*;|$)/);
    if (match) {
      fileName = match[1];
    }
  }
  const blob = await response.blob();
  const file = new File([blob], fileName, { type: mimeType });

  return file;
}
