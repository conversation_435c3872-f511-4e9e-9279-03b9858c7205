import type {
  Company,
  CompanyInList,
  CompanyCreateRequest,
  CompanyUpdateRequest,
} from '@/models/Company';
import type {
  Contact,
  ContactInList,
  ContactCreateRequest,
  ContactUpdateRequest,
} from '@/models/Contact';
import fetcher from './fetcher';

const CompanyQuery = {
  tags: ['Company'] as const,

  getAll: async (companyGroupName?: string) => {
    const params = new URLSearchParams();
    if (companyGroupName) {
      params.append('companyGroupName', companyGroupName);
    }
    const queryString = params.toString();
    const url = queryString ? `Company?${queryString}` : 'Company';
    return fetcher<CompanyInList[]>(url);
  },

  getOne: async (id: number) => fetcher<Company>(`Company/Get/${id}`),

  create: async (data: CompanyCreateRequest) =>
    fetcher<boolean>('Company/Add', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  update: (id: number) => async (data: CompanyUpdateRequest) =>
    fetcher<boolean>(`Company/Update/${id}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  delete: async (id: number) =>
    fetcher<boolean>(`Company/${id}`, {
      method: 'DELETE',
    }),

  // Contact methods
  contacts: {
    getAll: async (companyId: number) =>
      fetcher<ContactInList[]>(`Company/${companyId}/contacts`),

    getOne: async (companyId: number, contactId: number) =>
      fetcher<Contact>(`Company/${companyId}/contacts/${contactId}`),

    create: async (companyId: number, data: ContactCreateRequest) =>
      fetcher<boolean>(`Company/${companyId}/contacts/add`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      }),

    update:
      (companyId: number, contactId: number) =>
      async (data: ContactUpdateRequest) =>
        fetcher<boolean>(`Company/${companyId}/contacts/update/${contactId}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data),
        }),

    delete: async (companyId: number, contactId: number) =>
      fetcher<boolean>(`Company/${companyId}/contacts/delete/${contactId}`, {
        method: 'DELETE',
      }),
  },
};

export default CompanyQuery;
