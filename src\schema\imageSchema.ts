import * as z from 'zod';

import { langSchema } from './common';

const imageSchema = z.object({
  name: z.string(),
  altText: z
    .string()
    .min(1, 'Alt text is required')
    .max(200, { message: 'Alt text must be at most 200 characters long' }),
  fileUpload: z
    .array(
      z.instanceof(File).refine((file) => file.size < 4 * 1024 * 1024, {
        message: 'Total file size must be less than 4MB',
      }),
    )
    .min(1, 'Please select at least one image.')
    .max(5, 'Please select at most 5 images.'),
});

export type ImageData = z.infer<typeof imageSchema>;
export default imageSchema;

export const addImageSchema = z.object({
  image: z
    .array(
      z.instanceof(File).refine((file) => file.size < 4 * 1024 * 1024, {
        message: 'File size must be less than 4MB',
      }),
    )
    .min(1, 'Please select at least one image.')
    .max(1),
  altText: langSchema(z.string().min(1, 'Alt text is required')),
  name: langSchema(z.string().min(1, 'Name is required')),
});
export type AddImageData = z.infer<typeof addImageSchema>;

export const addMainImageSchema = z.object({
  image: z.string().min(1, 'Image is required'),
});
export type AddMainImageData = z.infer<typeof addMainImageSchema>;
