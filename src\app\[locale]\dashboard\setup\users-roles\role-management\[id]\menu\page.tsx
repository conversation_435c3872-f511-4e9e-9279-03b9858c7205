import { redirect } from 'next/navigation';
import { MenuAccessControl } from './component/menu_access_control';
import { getQueryClient } from '@/utils/query-client';
import RoleQuery from '@/services/queries/RoleQuery';
import MenuQuery from '@/services/queries/MenuQuery';

export default async function Permission({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const resolvedParams = await params;

  if (
    resolvedParams.id === 'add' &&
    isNaN(Number.parseInt(resolvedParams.id))
  ) {
    redirect('/dashboard/setup/users-roles/role-management/add');
  }

  const menuItems = await getQueryClient().fetchQuery({
    queryKey: [...MenuQuery.tags],
    queryFn: MenuQuery.getAll,
  });
  const sections = await getQueryClient().fetchQuery({
    queryKey: [...MenuQuery.tags, 'sections'],
    queryFn: MenuQuery.getSections,
  });
  const selectedItems = await getQueryClient().fetchQuery({
    queryKey: [...RoleQuery.tags, 'menu', { id: resolvedParams.id }],
    queryFn: () => RoleQuery.getMenu(Number.parseInt(resolvedParams.id)),
  });

  return (
    <div>
      <MenuAccessControl
        sections={sections.filter((c) => c.isDashboard).map((c) => c.name)}
        roleId={Number.parseInt(resolvedParams.id)}
        menuItems={menuItems.filter((m) => !m.parent && m.isDashboard)}
        selectedItems={selectedItems}
      />
    </div>
  );
}
