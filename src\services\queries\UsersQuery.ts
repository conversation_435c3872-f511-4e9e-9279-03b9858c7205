import { Invite<PERSON>ser, UserBriefData, UserDetail } from '@/models/User';
import { UserSchemaData } from '@/schema/UserSchema';

import fetcher from './fetcher';
import { BriefData } from '@/models/BriefData';

const UsersQuery = {
  tags: ['Users'] as const,
  getAll: async () => fetcher<UserBriefData[]>(`Users`),
  get: async (id: number) => fetcher<UserDetail>(`Users/${id}`),
  getCurrent: async () => fetcher<UserBriefData>(`Users/getCurrent`),
  getStatuses: async () => fetcher<BriefData[]>(`Users/Statuses`),

  update: (id: number) => async (data: UserSchemaData) =>
    fetcher<boolean>(`Users/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),
  updateCurrent: async (data: {
    firstName: string;
    lastName: string;
    workPhoneNumber?: string;
    mobileNumber?: string;
  }) =>
    fetcher<boolean>(`Users/updateCurrent`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),
  create: async (data: UserSchemaData) =>
    fetcher<boolean>(`Users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),

  updateRole: (userId: number, data: { roleId: number }) =>
    fetcher<boolean>(`Users/${userId}/role`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),
  switchStatus: async (id: number) =>
    fetcher<boolean>(`Users/${id}/toggle-archive`, {
      method: 'PATCH',
    }),
  invite: async (data: InviteUser) =>
    fetcher<boolean>(`Users/invite`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),
  getUsersByGroup: async (groupIds: number[]) =>
    fetcher<BriefData[]>(
      `Users/GetUsersByGroup?${groupIds.map((id) => `groupIds=${id}`).join('&')}`,
    ),
};

export default UsersQuery;
