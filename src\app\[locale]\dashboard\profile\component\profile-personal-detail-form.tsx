import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { UserBriefData } from '@/models/User';
import AuthQuery from '@/services/queries/AuthQuery';
import UsersQuery from '@/services/queries/UsersQuery';
import { getQueryClient } from '@/utils/query-client';

const schema = z.object({
  firstName: z.string().min(2),
  lastName: z.string().min(2),
  workPhoneNumber: z.string().optional(),
  mobileNumber: z.string().optional(),
});
interface Props {
  user: UserBriefData;
}
export default function ProfilePersonalInformationForm({ user }: Props) {
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      firstName: user.name.split(' ')[0],
      lastName: user.name.split(' ')[1],
      workPhoneNumber: user.workPhoneNumber,
      mobileNumber: user.mobileNumber,
    },
  });
  const { mutate, isPending } = useMutation({
    mutationKey: ['profile'],
    mutationFn: UsersQuery.updateCurrent,
    onSuccess: async () => {
      await getQueryClient().invalidateQueries({ queryKey: ['profile'] });
      await getQueryClient().invalidateQueries({ queryKey: UsersQuery.tags });
      await getQueryClient().invalidateQueries({
        queryKey: [AuthQuery.tags.me],
      });
    },
  });
  const { control, handleSubmit } = form;
  return (
    <Form {...form}>
      <form
        className="flex flex-col gap-6 w-full"
        onSubmit={handleSubmit((data) => mutate(data))}
      >
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div className="space-y-2">
            <Field
              control={control}
              name="firstName"
              label="First name"
              type="text"
            />
          </div>
          <div className="space-y-2">
            <Field
              control={control}
              name="lastName"
              label="Last name"
              type="text"
            />
          </div>
        </div>
        <div className="space-y-2">
          <Field
            control={control}
            name="workPhoneNumber"
            label="Work phone number"
            type="phone"
          />
          <Field
            control={control}
            name="mobileNumber"
            label="Mobile phone number"
            type="phone"
          />
        </div>
        <div className="flex justify-end">
          <Button type="submit" disabled={isPending}>
            {isPending ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
