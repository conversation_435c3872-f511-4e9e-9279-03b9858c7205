'use client';
import { useQuery } from '@tanstack/react-query';
import AppLayout from '@/components/ui/app_layout';
import Suspense from '@/components/ui/Suspense';
import EmployeeForm from './employee_form';
import UsersQuery from '@/services/queries/UsersQuery';

interface IContent {
  id?: number;
}
export default function Content({ id }: IContent) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: [...UsersQuery.tags, { id }],
    queryFn: () => UsersQuery.get(id!),
    enabled: id != undefined,
  });
  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <AppLayout
        items={[
          {
            title: 'User Management',
            link: '/dashboard/setup/user-management',
          },
          {
            title:
              data?.firstName && data?.lastName
                ? `${data.firstName} ${data.lastName}`
                : 'Add User',
            link: '/dashboard/setup/user-management/' + (data?.id ?? 'add'),
          },
        ]}
      >
        <EmployeeForm data={data} />
      </AppLayout>
    </Suspense>
  );
}
