import { dehydrate, HydrationBoundary } from '@tanstack/react-query';

import AppLayout from '@/components/ui/app_layout';
import RoleGroupQuery from '@/services/queries/RoleGroupQuery';
import { getQueryClient } from '@/utils/query-client';

import RoleGroupTable from './components/role_group_table';

async function RoleManagement() {
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery({
    queryKey: RoleGroupQuery.tags,
    queryFn: () => RoleGroupQuery.getAll(),
  });
  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'Users & Roles', link: '/dashboard/setup' },
        {
          title: 'Role management',
          link: '/dashboard/setup/users-roles/role-management',
        },
        {
          title: 'Role Groups',
          link: '/dashboard/setup/users-roles/role-management',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        <RoleGroupTable />
      </HydrationBoundary>
    </AppLayout>
  );
}

export default RoleManagement;
