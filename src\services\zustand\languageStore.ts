import { create } from 'zustand';

import Language from '@/models/Language';

interface LanguageState {
  languages: Language[];
  setLanguages: (languages?: string[]) => void;
  reset: () => void;
}
const DEFAULT: Language[] = [
  { langCode: 'fr', name: 'Français' },
  { langCode: 'en', name: 'English' },
  { langCode: 'ja', name: '日本語' },
];

const useLanguageStore = create<LanguageState>((set) => ({
  languages: DEFAULT,
  setLanguages: (languages) =>
    set({
      languages:
        languages && languages.length > 0
          ? DEFAULT.filter((i) => languages.includes(i.langCode))
          : DEFAULT,
    }),
  reset: () => set({ languages: DEFAULT }),
}));

export default useLanguageStore;
