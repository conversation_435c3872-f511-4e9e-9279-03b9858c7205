// Type for role within a role group
export interface RoleInGroup {
  id: number;
  name: string;
  userCount: number;
  level: number;
}

// Type for the basic role group detail response
export interface BasicRoleGroupDetail {
  id: number;
  name: string;
  minLevel: number;
  maxLevel: number;
  roles: RoleInGroup[];
}

// Type for creating/updating role group
export interface RoleGroupData {
  name: string;
  minLevel: number;
  maxLevel: number;
}

export interface SetRoleGroupRolesData {
  roleIds: number[];
}
