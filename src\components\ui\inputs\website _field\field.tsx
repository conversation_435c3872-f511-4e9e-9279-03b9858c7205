import { FileText } from 'lucide-react';
import Image from 'next/image';
import { HTMLInputTypeAttribute, InputHTMLAttributes, ReactNode } from 'react';
import { DropzoneOptions } from 'react-dropzone';
import {
  Control,
  ControllerRenderProps,
  FieldPath,
  FieldValues,
} from 'react-hook-form';

import { cn } from '@/lib/utils';

import FileSvgDraw from '../../file_svg_draw';
import {
  FileInput,
  FileUploader,
  FileUploaderContent,
  FileUploaderItem,
} from '../../FileInput';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../../form';
import Checkbox from '../checkbox';
import MultiSelectFormField from '../MultiBadgeSelector';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../select';
import { Textarea } from '../textarea';
import PhoneNumberInput from '../phone_number_input';
import DatePickerInput from '../date_picker_input';
import { RadioGroup, RadioGroupItem } from '../../radio-group';
import { PasswordInput } from '../password-input';
import { Switch } from '../../switch';
import { InputTags } from '../input-tags';
import DatePickerWithRange from './date_range_picker_input/date-range-picker-input';
import { Input } from './input';
import { TimePicker12Hour } from '../time_picker_input/time-input';
import SimpleEditor from '../../Shad_Rich_Text/simple-editor';

export type OptionType = { label: string; value: string };
type RenderTypeOption = {
  label: string;
  value: string | number;
  render?: (
    field: ControllerRenderProps<FieldValues, FieldPath<FieldValues>>,
  ) => React.ReactNode;
  isDisabled?: boolean;
};

export interface MultiBadgeSelectOptionType extends OptionType {
  icon?: React.ReactNode;
}

type multiBadgeSelectType = {
  type: 'multiBadgeSelect';
  props: {
    options: MultiBadgeSelectOptionType[];
    placeholder: string;
  };
};
type multiSelectType = {
  type: 'multiSelect';
  props: {
    className?: string;
    options: RenderTypeOption[];
    placeholder?: string;
  };
};
type selectType = {
  type: 'select';
  props: {
    options: OptionType[];
    placeholder: string;
  };
};

type RadioGroupType = {
  type: 'RadioGroup';
  props: {
    options: RenderTypeOption[];
    optionContainer?: string;
  };
};

type fileType = {
  type: 'file';
  props: {
    dropzoneOptions: DropzoneOptions;
  };
};
type TimeType = {
  type: 'time';
  props?: any;
};
export type InputType =
  | HTMLInputTypeAttribute
  | (
      | 'text'
      | 'phone'
      | 'textarea'
      | 'richText'
      | 'simpleRichText'
      | 'ShadRichText'
      | 'checkbox'
      | RadioGroupType
      | 'month'
      | fileType
      | multiBadgeSelectType
      | multiSelectType
      | selectType
      | 'date'
      | 'dateRangePicker'
      | 'switch'
      | 'tags'
      | TimeType
    );

interface IField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> {
  type: InputType;
  control: Control<TFieldValues>;
  name: TName;
  label?: string | ReactNode;
  description?: string | ReactNode;
  required?: boolean;
  containerClassName?: string;
  containerStyle?: React.CSSProperties;
}
function Field<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  type,
  name,
  label,
  containerClassName,
  containerStyle,
  description,
  required,
  control,
  ...otherInputProps
}: IField<TFieldValues, TName> &
  Omit<InputHTMLAttributes<HTMLInputElement>, 'type'>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem
          className={cn('gap-1', containerClassName)}
          style={containerStyle}
        >
          {label && (
            <FormLabel
              className={cn('leading-[2.143]', {
                'text-muted-foreground cursor-not-allowed':
                  otherInputProps.disabled,
              })}
            >
              <span className="text-lg font-semibold">{label}</span>
              {required && <span className="text-red-500"> *</span>}
            </FormLabel>
          )}
          {description && <FormDescription>{description}</FormDescription>}
          <FormControl>
            {type == 'textarea' ? (
              <Textarea {...field} />
            ) : type == 'phone' ? (
              <PhoneNumberInput {...field} />
            ) : type == 'password' ? (
              <PasswordInput {...field} />
            ) : type == 'date' ? (
              <DatePickerInput
                onValueChange={field.onChange}
                date={field.value}
              />
            ) : type == 'dateRangePicker' ? (
              <DatePickerWithRange onValueChange={field.onChange} />
            ) : // type == 'month' ? (
            //   <MonthPicker onValueChange={field.onChange} date={field.value} />
            // ) :
            // type == 'richText' ? (
            //   <PlateController>
            //     <PlateEditor
            //       onChange={(value) => field.onChange(value)}
            //       value={field.value}
            //     />
            //   </PlateController>
            // ) :
            type == 'ShadRichText' ? (
              <SimpleEditor
                onChange={(value) => field.onChange(value)}
                value={field.value}
              />
            ) : // type == 'simpleRichText' ? (
            //   <PlateController>
            //     <SimpleEditor
            //       onChange={(value) => field.onChange(value)}
            //       value={field.value}
            //     />
            //   </PlateController>
            // ) :
            type == 'checkbox' ? (
              <Checkbox
                checked={field.value}
                onCheckedChange={field.onChange}
                disabled={otherInputProps.disabled}
              ></Checkbox>
            ) : type == 'switch' ? (
              <Switch
                checked={field.value}
                onCheckedChange={field.onChange}
              ></Switch>
            ) : type == 'tags' ? (
              <InputTags
                value={field.value}
                onChange={field.onChange}
                placeholder="Enter values, comma separated..."
                className="max-w-[500px]"
              />
            ) : typeof type == 'object' ? (
              type.type == 'time' ? (
                <TimePicker12Hour
                  date={field.value}
                  setDate={(data) => {
                    field.onChange(data);
                  }}
                />
              ) : type.type == 'file' ? (
                <FileUploader
                  value={field.value}
                  onValueChange={field.onChange}
                  reSelect={true}
                  {...type.props}
                  className="  rounded-sm"
                >
                  {field.value &&
                  field.value.length > 0 &&
                  Array.isArray(field.value) ? (
                    <FileUploaderContent>
                      {field.value &&
                        field.value.length > 0 &&
                        field.value?.map?.((file: File, i: number) => (
                          <FileUploaderItem key={i} index={i}>
                            <div className="flex flex-row items-center gap-2 !text-foreground  ">
                              {file.type.startsWith('image') ? (
                                <Image
                                  src={URL.createObjectURL(file)}
                                  alt={file.name}
                                  width={100}
                                  height={100}
                                  className="object-cover rounded-sm size-[72px] "
                                />
                              ) : (
                                <div className="size-[72px] flex items-center justify-center rounded-sm bg-gray-600">
                                  <FileText className="size-full p-4 text-primary " />
                                </div>
                              )}

                              <div className="flex flex-col gap-2    ">
                                <span className="text-sm w-fit  truncate  max-w-[300px]  ">
                                  {file.name}
                                </span>
                                <span className="text-sm w-fit">
                                  {Math.round(file.size / 1024)} KB
                                </span>
                              </div>
                            </div>
                          </FileUploaderItem>
                        ))}
                    </FileUploaderContent>
                  ) : (
                    <FileInput className="outline-dashed outline-1 outline-gray-300">
                      <div className="flex items-center justify-center flex-col pt-3 pb-4 w-full ">
                        <FileSvgDraw
                          accept={
                            type.props.dropzoneOptions.accept ?? undefined
                          }
                        />
                      </div>
                    </FileInput>
                  )}
                </FileUploader>
              ) : type.type == 'multiSelect' ? (
                <div
                  className={cn(
                    'flex flex-col gap-8 flex-wrap w-full',
                    type.props.className,
                  )}
                >
                  {type.props.options.map((option, index) => (
                    <FormField
                      key={index}
                      control={control}
                      name={name}
                      render={({ field: checkboxField }) => {
                        return (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <label
                                className={cn(
                                  'checkbox-container flex items-center cursor-pointer relative text-sm',
                                  !option.render && 'flex-row-reverse gap-2 ',
                                )}
                              >
                                {!option.render && option.label}
                                <Checkbox
                                  className={cn(
                                    option.render &&
                                      'absolute right-4 top-4 size-6 z-10',
                                  )}
                                  checked={checkboxField.value?.includes(
                                    option.value,
                                  )}
                                  disabled={option.isDisabled}
                                  onCheckedChange={(checked) => {
                                    return checked
                                      ? checkboxField.onChange([
                                          ...checkboxField.value,
                                          option.value,
                                        ])
                                      : checkboxField.onChange(
                                          checkboxField.value?.filter(
                                            (v: string) => v !== option.value,
                                          ),
                                        );
                                  }}
                                ></Checkbox>
                                {option.render &&
                                  option.render(checkboxField as any)}
                              </label>
                            </FormControl>
                          </FormItem>
                        );
                      }}
                    />
                  ))}
                </div>
              ) : type.type == 'multiBadgeSelect' ? (
                <MultiSelectFormField
                  onValueChange={field.onChange}
                  placeholder={type.props.placeholder}
                  options={type.props.options}
                  {...otherInputProps}
                  defaultValue={
                    typeof field.value == 'string' ||
                    typeof field.value == 'number'
                      ? [String(field.value)]
                      : (field.value as any[])?.map(String)
                  }
                />
              ) : type.type == 'RadioGroup' ? (
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className={cn(
                    'flex flex-row gap-8 flex-wrap w-full',
                    containerClassName,
                  )}
                >
                  {type.props.options.map((option, index) => (
                    <FormItem
                      key={index}
                      className={cn(
                        'flex flex-row items-center w-fit  relative ',
                        type.props.optionContainer,
                      )}
                    >
                      <FormLabel className="font-normal">
                        {option.label}
                        {option.render && option.render(field as any)}
                      </FormLabel>
                      <FormControl>
                        <RadioGroupItem
                          className={cn(
                            option.render && 'absolute top-4 right-4  z-10 ',
                          )}
                          value={String(option.value)}
                        />
                      </FormControl>
                    </FormItem>
                  ))}
                </RadioGroup>
              ) : (
                type.type == 'select' && (
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={type.props.placeholder} />
                    </SelectTrigger>
                    <SelectContent>
                      {type.props.options.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )
              )
            ) : (
              <Input {...field} {...otherInputProps} />
            )}
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export default Field;
