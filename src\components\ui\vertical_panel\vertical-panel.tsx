import { MouseEvent, ReactNode } from 'react';

import { themeColors } from '@/assets/color';
interface VerticalPanelProps {
  Icon?: ReactNode;
  title?: string;
  description?: string;
  action?: {
    text: string;
    onClick?: (e: MouseEvent<HTMLButtonElement>) => void;
  };
  IconBtn?: ReactNode;
  backgroundColor?: string;
  padding?: number | string;
  alignSelf?: 'center' | 'stretch' | 'start' | 'end';
  height?: number | string;
  flexGrow?: true;
  bottomControls?: ReactNode;
}
export default function VerticalPanel({
  Icon,
  title,
  description,
  action,
  IconBtn,
  backgroundColor,
  padding = 20,
  alignSelf,
  height,
  flexGrow,
  bottomControls,
}: VerticalPanelProps) {
  return (
    <div
      className="vertical-panel flex flex-col justify-center items-center gap-2 rounded-lg h-fit relative  "
      style={{
        background: backgroundColor,
        padding: padding,
        boxShadow: backgroundColor
          ? `0 0 5px 2px ${themeColors.muted}`
          : undefined,
        alignSelf: alignSelf,
        height,
        maxHeight: height,
        minHeight: height,

        flexGrow: flexGrow ? 1 : 0,
      }}
    >
      {Icon}
      {title && (
        <span className="title-div  text-lg text-foreground ">{title}</span>
      )}
      {(description || action) && (
        <div className="description-div flex flex-row gap-[5px]  ">
          <span className="text-foreground  text-center text-sm font-medium ">
            {description}
            {action && (
              <span
                className="font-medium text-sm text-blue-500 cursor-pointer whitespace-nowrap hover:underline  "
                onClick={action.onClick}
              >
                {action.text}
              </span>
            )}
          </span>
        </div>
      )}
      {bottomControls}
      {IconBtn && <div className="top-left-btn">{IconBtn}</div>}
    </div>
  );
}
