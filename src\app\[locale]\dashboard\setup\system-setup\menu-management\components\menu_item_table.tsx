'use client';

import Link from 'next/link';
import { generateTableColumns } from '@/lib/tableUtils';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { useQuery } from '@tanstack/react-query';
import { LayoutGrid, Table } from 'lucide-react';
import { EditIcon } from '@/assets/Icons';
import { useMemo, useState } from 'react';
import { MenuTabs } from './menu_tabs';
import MenuItemManagement from './menu_item_management';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import MenuQuery from '@/services/queries/MenuQuery';
import { getMenuName, MenuItem } from '@/models/MenuItem';

export default function MenuItemTable({ sections }: { sections: string[] }) {
  const { data, isPending, isLoading } = useQuery({
    queryKey: [...MenuQuery.tags],
    queryFn: () => MenuQuery.getAll(),
  });

  const newColumns = generateTableColumns<MenuItem>(
    {
      name: { name: 'Name', type: 'text', sortable: true },
      parent: {
        name: 'Parent',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <span className="px-0">
              {cell ? getMenuName(cell) : 'No Parent'}
            </span>
          ),
        },
      },
      displayOrder: {
        name: 'Display Order',
        type: {
          type: 'node',
          render: ({ cell }) => <span className="px-0">{cell}</span>,
        },
        sortable: true,
      },
      section: { name: 'Section', type: 'text', sortable: true },
      isVisible: {
        name: 'Visibility',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <span
              className={`px-0 ${cell ? 'text-success' : 'text-destructive'}`}
            >
              {cell ? 'Visible' : 'Hidden'}
            </span>
          ),
        },
      },
    },
    {
      'edit-action': {
        name: 'Edit',
        value: <EditIcon size={16} />,
        type: {
          type: 'node',
          render: ({ row, cell }) => (
            <Link
              href={`/dashboard/setup/system-setup/menu-management/${row.id ?? 'add'} `}
            >
              <Button variant="secondary" size="icon">
                {cell}
              </Button>
            </Link>
          ),
        },
      },
    },
    false,
  );

  const [activeSection, setActiveSection] = useState(sections[0]);
  const [viewMode, setViewMode] = useState('grid');

  const filteredItems = useMemo(() => {
    // Helper function to recursively filter items
    const filterItemsRecursively = (items: MenuItem[]): MenuItem[] => {
      return items
        .map((item) => {
          // Create a new item with filtered children
          if (item.children && item.children.length > 0) {
            return {
              ...item,
              children: filterItemsRecursively(item.children),
            };
          }
          return item;
        })
        .filter((item) => {
          // Keep items that match the section directly
          if (item.section === activeSection) return true;

          // Or keep items that have children matching the section
          return item.children && item.children.length > 0;
        });
    };

    // Apply the recursive filter to the data
    return filterItemsRecursively(data ?? []);
  }, [activeSection, data]);

  const ViewSwitcher = () => (
    <ToggleGroup
      type="single"
      value={viewMode}
      onValueChange={(value) => value && setViewMode(value)}
    >
      <ToggleGroupItem value="table" aria-label="Table view">
        <Table className="size-4" />
      </ToggleGroupItem>
      <ToggleGroupItem value="grid" aria-label="Grid view">
        <LayoutGrid className="size-4" />
      </ToggleGroupItem>
    </ToggleGroup>
  );

  return (
    <>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <MenuTabs
            sections={sections}
            activeSection={activeSection}
            onSectionChange={setActiveSection}
          />
          <ViewSwitcher />
        </div>

        <div className="space-y-4">
          {viewMode === 'grid' ? (
            <MenuItemManagement data={filteredItems} />
          ) : (
            <DataTable
              filterFields={[{ id: 'name', name: 'Name', type: 'text' }]}
              columns={newColumns}
              data={filteredItems}
              isLoading={isLoading || isPending}
              controls={
                <div className="flex flex-row gap-2 justify-end">
                  <Link
                    href={`/dashboard/setup/system-setup/menu-management/add`}
                  >
                    <Button variant="primary" iconName="AddIcon">
                      Add new item
                    </Button>
                  </Link>
                </div>
              }
            />
          )}
        </div>
      </div>
    </>
  );
}
