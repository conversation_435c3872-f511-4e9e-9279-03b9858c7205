'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { AlertCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AvatarFallback, Avatar } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { PasswordInput } from '@/components/ui/inputs/password-input';
import { UserData } from '@/models/User';
import VerificationSchema, { VerificationData } from '@/schema/Verification';
import AuthQuery from '@/services/queries/AuthQuery';
import { useAuthStore } from '@/services/zustand/authStore';
import useConnectionStore from '@/services/zustand/ConnectionStore';
import { getQueryClient } from '@/utils/query-client';

interface IVerificationForm {
  token: string;
  user: UserData;
}
export default function VerificationForm({ user, token }: IVerificationForm) {
  const t = useTranslations('contact');
  const c = useTranslations('Common');

  const form = useForm<VerificationData>({
    resolver: zodResolver(VerificationSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  });
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = form;
  const { setTokens } = useAuthStore();
  const { push } = useRouter();
  const { isSuccess, isError, isPending, mutate } = useMutation({
    mutationKey: [AuthQuery.tags.auth],
    mutationFn: AuthQuery.verify(token),
    onSuccess: async (data) => {
      setTokens({
        accessToken: data.accessToken,
        refreshToken: data.refreshToken,
      });
      await getQueryClient().invalidateQueries();
      useConnectionStore.getState().setConnected(true);
      push('/dashboard');
    },
  });
  return (
    <Form {...form}>
      <form
        onSubmit={handleSubmit((data) => mutate(data))}
        className="space-y-4 md:space-y-6"
      >
        {(isError || errors.root) && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>{c('error')}</AlertTitle>
            <AlertDescription>
              {errors.root?.message ?? c('somethingWentWrong')}
            </AlertDescription>
          </Alert>
        )}
        <div className="border border-gray-200 dark:border-gray-800 p-4 rounded-md shadow-md">
          <div className="flex items-center gap-3">
            <Avatar className="h-9 w-9">
              <AvatarFallback>
                {user.name?.split(' ')?.[0][0]} {user.name?.split(' ')?.[1][0]}
              </AvatarFallback>
            </Avatar>
            <div className="grid gap-0.5 text-xs">
              <div className="font-medium">{user.name}</div>
              <div className="text-gray-500 dark:text-gray-400">
                {user.email}
              </div>
            </div>
          </div>
        </div>
        <Alert>{t('accountEnterPassword')}</Alert>
        <FormField
          control={control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('password')}</FormLabel>
              <FormControl>
                <PasswordInput {...field} disabled={isPending || isSuccess} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('confirmPassword')}</FormLabel>
              <FormControl>
                <PasswordInput {...field} disabled={isPending || isSuccess} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          className="w-full"
          type="submit"
          disabled={isPending || isSuccess}
        >
          {isPending
            ? t('signInLoading')
            : isSuccess
              ? c('pleaseWait')
              : t('createAccount')}
        </Button>
      </form>
    </Form>
  );
}
