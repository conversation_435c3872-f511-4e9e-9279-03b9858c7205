import { BriefData } from '@/models/BriefData';
import fetcher from './fetcher';
import { GroupTypeWithGroupDto } from '@/models/Offering';

const GroupTypeQuery = {
  tags: ['Group Type'] as const,

  // GET: /GroupType
  getAll: async (): Promise<BriefData[]> => {
    return fetcher<BriefData[]>('GroupType');
  },
  getAllByGroupIdHierarchical: async (
    groupId: number,
  ): Promise<GroupTypeWithGroupDto> => {
    return fetcher<GroupTypeWithGroupDto>(`GroupType/${groupId}/get`);
  },
};
export default GroupTypeQuery;
