import type { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import ShowsTable from '@/components/shows-table';

export const metadata: Metadata = {
  title: 'List of Shows | GOODKEY SHOW SERVICES LTD.',
  description:
    'Browse and manage all shows in the GOODKEY SHOW SERVICES LTD. system.',
  openGraph: {
    title: 'List of Shows | GOODKEY SHOW SERVICES LTD.',
    description:
      'Browse and manage all shows in the GOODKEY SHOW SERVICES LTD. system.',
  },
};

export default function ListOfShowsPage() {
  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        { title: 'List of Shows', link: '/dashboard/setup/list-of-shows' },
      ]}
    >
      <ShowsTable />
    </AppLayout>
  );
}
