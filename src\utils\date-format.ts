export const formatDate = (date: Date | string | undefined): string => {
  if (!date) return 'N/A'; // Return 'N/A' if no date is provided
  if (typeof date === 'string') {
    const parsedDate = new Date(date); // Parse the ISO 8601 string into a Date object
    if (isNaN(parsedDate.getTime())) return 'Invalid Date'; // Handle invalid date strings
    date = parsedDate;
  }

  // Formatting Date to Month Day, Year Time format
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true, // Use 12-hour time format
  };
  return date.toLocaleString('en-US', options); // Formatting Date object
};

export const formatDateTime = (date: Date | string | undefined): string => {
  if (!date) return 'N/A'; // Return 'N/A' if no date is provided
  if (typeof date === 'string') {
    const parsedDate = new Date(date); // Parse the ISO 8601 string into a Date object
    if (isNaN(parsedDate.getTime())) return 'Invalid Date'; // Handle invalid date strings
    date = parsedDate;
  }

  // Formatting Date to Month Day, Year Time format
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  };
  return date.toLocaleString('en-US', options); // Formatting Date object
};

export const getDate = (date: Date | string | undefined): string => {
  if (!date) return 'N/A'; // Return 'N/A' if no date is provided
  if (typeof date === 'string') {
    const parsedDate = new Date(date); // Parse the ISO 8601 string into a Date object
    if (isNaN(parsedDate.getTime())) return 'Invalid Date'; // Handle invalid date strings
    date = parsedDate;
  }

  // Ensure that the date is handled in UTC, avoiding local timezone offset
  const utcDate = new Date(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    date.getUTCDate(),
  );

  // Formatting Date to Month Day, Year format
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };

  return utcDate.toLocaleString('en-US', options); // Format the UTC date
};

export const getUTCDate = (date: Date | string | undefined): string => {
  if (!date) return 'N/A'; // Return 'N/A' if no date is provided
  if (typeof date === 'string') {
    const parsedDate = new Date(date); // Parse the ISO 8601 string into a Date object
    if (isNaN(parsedDate.getTime())) return 'Invalid Date'; // Handle invalid date strings
    date = parsedDate;
  }

  // Ensure that the date is handled in UTC, avoiding local timezone offset
  const utcDate = new Date(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    date.getUTCDate(),
  );

  // Formatting Date to Month Day, Year format
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };

  return utcDate.toLocaleString('en-US', options); // Format the UTC date
};
