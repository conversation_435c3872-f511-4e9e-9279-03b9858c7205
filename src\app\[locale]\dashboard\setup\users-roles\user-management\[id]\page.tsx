import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { getQueryClient } from '@/utils/query-client';
import Content from './components/content';
import UsersQuery from '@/services/queries/UsersQuery';

export const revalidate = 0;
export const fetchCache = 'force-no-store';

export default async function Page(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const queryClient = getQueryClient();

    if (params.id !== 'add') {
      if (isNaN(Number.parseInt(params.id))) throw new Error();

      await queryClient.fetchQuery({
        queryKey: [...UsersQuery.tags, { id: Number.parseInt(params.id) }],
        queryFn: () => UsersQuery.get(Number.parseInt(params.id)),
      });
    }

    return (
      <HydrationBoundary state={dehydrate(queryClient)}>
        <Content
          id={
            isNaN(Number.parseInt(params.id))
              ? undefined
              : Number.parseInt(params.id)
          }
        />
      </HydrationBoundary>
    );
  } catch (e) {
    redirect('/dashboard/setup/users-roles/user-management/add');
  }
}
