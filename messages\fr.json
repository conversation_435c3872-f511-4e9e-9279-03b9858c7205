{"Common": {"ValidationMessages": {"minErrorMessage": "{field} est trop court", "maxErrorMessage": "{field} est trop long", "invalidEmailErrorMessage": "Veuillez entrer une adresse e-mail valide", "Required": "{field} est requis"}, "tooltip": {"edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "view": "Voir", "copy": "<PERSON><PERSON><PERSON>", "archive": "Archiver", "report": "Rapport"}, "tables": {"id": "ID", "name": "Nom", "status": "Statut", "phoneNumber": "Numéro de téléphone", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le", "actions": "Actions", "firstName": "Prénom", "lastName": "Nom", "email": "E-mail", "mobile": "Mobile", "preferredLang": "Langue préférée", "address1": "<PERSON><PERSON><PERSON>", "city": "Ville", "province": "Province", "postalCode": "Code postal", "country": "Pays", "clientType": "Type de client", "clientStatus": "Statut du client", "clientStatusActive": "Actif", "clientStatusInactive": "Inactif", "outfitters": "Pourvoiries", "accommodationType": "Type d'hébergement", "subject": "Sujet", "source": "Source", "originalFileName": "Nom du fichier d'origine", "uploadDate": "Date de téléchargement", "title": "Titre", "company": "Entreprise", "verified": "Vérifié", "footer": "Pied de page", "clientName": "Nom du client", "assignedTo": "Affecté à", "importantNote": "Note importante", "quantity": "Quantité", "duration": "<PERSON><PERSON><PERSON>", "description": "Description", "itemType": "Type d'article", "action": "Action", "areYouSureToRemove": "Voulez-vous vraiment supprimer "}, "status": {"published": "<PERSON><PERSON><PERSON>", "draft": "Brouillon"}, "email": "E-mail", "pleaseWait": "Veuillez patienter...", "errorMessage": "Nous n'avons pas pu traiter votre demande. Veuillez réessayer.", "message": "Message", "submit": "Envoyer", "cancel": "Annuler", "failedCaptcha": "La vérification du Captcha a échoué.", "error": "<PERSON><PERSON><PERSON>", "somethingWentWrong": "Un problème est survenu. Veuillez réessayer.", "done": "<PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openSidebar": "<PERSON>u<PERSON><PERSON>r la barre latérale", "Language": "<PERSON><PERSON>", "english": "<PERSON><PERSON><PERSON>", "french": "Français", "edit": "Modifier", "addNew": "Ajouter nouveau", "inactive": "Inactif", "active": "Actif", "prospect": "Prospect", "profileId": "ID du profil", "createdBy": "C<PERSON><PERSON> par", "updatedBy": "Mis à jour par", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le", "success": "Su<PERSON>ès", "created": "c<PERSON><PERSON>", "updated": "mis à jour", "next": "Suivant", "prev": "Précédent", "update": "Mise à jour", "add": "Ajouter", "delete": "<PERSON><PERSON><PERSON><PERSON>", "view": "Voir", "search": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "upload": "Importer", "successTitle": "Su<PERSON>ès", "symbol": "Symbole", "outfitters": "Pourvoyeurs", "agree": "J'accepte", "preview": "<PERSON><PERSON><PERSON><PERSON>", "live": "En direct", "product": "Produit", "package": "forfait", "outfitter": "Pourvoirie", "room": "chambre", "service": "service", "offerings": "Offres", "accommodation": "Hébergement", "territory": "<PERSON><PERSON><PERSON>", "autoGenerated": "Auto-généré", "published": "<PERSON><PERSON><PERSON>", "unpublished": "Non publié", "archived": "Archivé", "notArchived": "Non archivé", "create": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Reinitialiser", "saveLoading": "Sauvegarde en cours...", "minSize": "<PERSON><PERSON>e", "maxSize": "<PERSON><PERSON> maximale", "clickToUpload": "Cliquez pour télécharger", "dragAndDrop": "ou faites glisser et déposez", "included": "Inclus", "notIncluded": "Non inclus", "continue": "<PERSON><PERSON><PERSON>", "dialogConfirmTitle": "Êtes-vous absolument sûr ?", "dialogConfirmDesc": "Cette action est irréversible. Cela supprimera définitivement le", "lastModifiedBy": "Dernière modification par", "loading": "Chargement...", "saving": "Sauvegarde en cours...", "selectMainImage": "Sélectionner l'image principale", "editImage": "Modifier l'image", "deleteImage": "Supprimer l'image", "noImagesUploaded": "Aucune image téléchargée pour le moment", "addNewImage": "Ajouter une nouvelle image", "imageGalleryTitle": "Liste des images", "addingImageToast": "L'image a été ajoutée avec succès", "imageVerification": "La taille maximale autorisée pour l'image est de 4 Mo. Seuls les fichiers avec les extensions suivantes sont acceptés : .jpg, .jpeg, .png.", "available": "Disponible", "imageRemoveToast": "L'image a été supprimée avec succès", "documentManagement": "Gestion de documents", "documentGroups": "Groupes de documents", "documentGroup": "Groupe de documents", "addNewDocumentGroup": "Ajouter un nouveau groupe de documents", "editDocumentGroup": "Modifier le groupe de documents", "documentCategories": "Catégories de documents", "documentCategory": "Catégorie de document", "addNewDocumentCategory": "Ajouter une nouvelle catégorie de documents", "editDocumentCategory": "Modifier la catégorie de document", "documentFileTypes": "Types de fichiers de document", "documentFileType": "Type de fichier de document", "addNewDocumentFileType": "Ajouter un nouveau type de fichier de document", "editDocumentFileType": "Modifier le type de fichier de document", "documents": "Documents", "document": "Document", "addNewDocument": "Ajouter un nouveau document", "editDocument": "Modifier le document", "fileExtension": "Extension de fichier", "mimeType": "Type MIME", "deleteImageTitle": "Supprimer l'image", "deleteImageDescription": "Êtes-vous sûr de vouloir supprimer cette image ?", "documentGroupCreatedSuccess": "Groupe de documents créé avec succès", "documentGroupUpdatedSuccess": "Groupe de documents mis à jour avec succès", "documentGroupDeletedSuccess": "Groupe de documents supprimé avec succès", "documentCategoryCreatedSuccess": "Catégorie de document créée avec succès", "documentCategoryUpdatedSuccess": "Catégorie de document mise à jour avec succès", "documentCategoryDeletedSuccess": "Catégorie de document supprimée avec succès", "documentFileTypeCreatedSuccess": "Type de fichier de document créé avec succès", "documentFileTypeUpdatedSuccess": "Type de fichier de document mis à jour avec succès", "documentFileTypeDeletedSuccess": "Type de fichier de document supprimé avec succès", "documentCreatedSuccess": "Document créé avec succès", "documentUpdatedSuccess": "Document mis à jour avec succès", "documentDeletedSuccess": "Document supprimé avec succès", "deleteButtonPending": "Veuillez patienter...", "bedConfig": "Configuration des lits", "main": "<PERSON><PERSON>", "imageSelectionTitle": "Sélectionner une image principale", "backToList": "Retour à la liste", "pricePeriodCreated": "La période de tarification a été créée avec succès", "pricePeriodDeleted": "La période de tarification a été supprimée avec succès", "priceSectionTitle": "Section des prix", "priceCalendarTitle": "Calendrier des prix", "dateRange": "Plage de dates", "weekday": "<PERSON><PERSON> <PERSON>", "weekdayPlaceholder": "Sélectionnez un jour de la semaine", "price": "Prix", "pricePlaceholder": "Entrez le prix", "deleteRange": "Supprimer la plage", "addRange": "Ajouter une plage", "free": "<PERSON><PERSON><PERSON>", "filterBy": "Filtrer par", "back": "Retour", "todayPrice": "Prix de aujourd'hui", "fullDay": "<PERSON><PERSON> entier", "halfDay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isHalfDay": "Est demi-journee", "lastModified": "Dernière modification", "modifiedBy": "Modifié par", "profile": "Profil", "logOut": "Se déconnecter", "login": "Se connecter", "sessionExpiredTitle": "Session expirée", "sessionExpiredDesc": "Votre session a expiré. Veuillez vous reconnecter.", "applicableTaxesLabel": "Taxes applicables", "GSTLabel": "TPS", "PSTLabel": "TVP", "HSTLabel": "TVH", "department": "Département", "personalEmail": "Courriel personnel", "workEmail": "<PERSON><PERSON><PERSON>", "role": "Role", "verified": "<PERSON><PERSON><PERSON><PERSON>", "notVerified": "Non verifié", "username": "Nom d'utilisateur", "warning": "Avertissement", "yes": "O<PERSON>", "no": "Non", "archive": "Archiver", "archiveEmail": "Archiver l'email", "archiveEmailConfirm": "Êtes-vous sûr de vouloir archiver cet email ?", "archiveSuccessMessage": "L'email a été archivé avec succès.", "removeEmail": "Supprimer l'email", "removeEmailConfirm": "Êtes-vous sûr de vouloir supprimer cet email ?", "removeSuccessMessage": "L'email a été supprimé avec succès.", "testEmail": "Tester l'email", "testEmailConfirm": "Êtes-vous sûr de vouloir envoyer un test à cet email ?", "testSuccessMessage": "L'email a été envoyé avec succès.", "sendEmail": "Envoyer l'email", "sendEMailConfirm": "Êtes-vous sûr de vouloir envoyer cet email ?", "sendSuccessMessage": "L'email a été envoyé avec succès.", "announcementReport": "Rapport d'annonce", "emailCampaignReport": "Rapport de campagne par email", "detailedInformationCampaign": "Veuillez consulter ci-dessous pour des informations détaillées sur cette campagne.", "detailedInformationAnnouncement": "Veuillez consulter ci-dessous pour des informations détaillées sur cette annonce.", "announcement": "<PERSON><PERSON><PERSON>", "emailCampaign": "campagne par email", "createdDate": "Date de création :", "lastUpdatedBy": "Dernière mise à jour par :", "lastUpdatedDate": "Dernière mise à jour le :", "sentBy": "Envoyé par :", "sentDate": "Date d'envoi :", "fromEmail": "Email de l'expéditeur :", "fromName": "Nom de l'expéditeur :", "totalSendRequest": "Total des demandes d'envoi :", "numberSentSuccess": "Nombre d'envois réussis :", "numberSentError": "Nombre d'envois échoués :", "total": "Total", "noProcessed": "Nombre traité", "noDelivered": "Nombre livré", "noOpen": "Nombre ouvert", "noClick": "Nombre cliqué", "noBounce": "Nombre de rebonds", "noDeferred": "Nombre différé", "noDropped": "Nombre abandonné", "report": "Rapport", "or": "Ou", "footer": "Pied de page", "tableNote": "Veuillez appliquer des filtres pour voir les données.", "noResults": "Aucun resultats", "client": "Clients", "numberOfAdults": "Numero d'adultes", "numberOfChildren": "Numero de enfants", "note": "Notes", "createRequest": "<PERSON><PERSON><PERSON> une demande", "createRequestDesc": "<PERSON><PERSON><PERSON>z remplir le formulaire suivant pour créer une demande.", "requestCreated": "La demande a bien été crée.", "Product": "Produit", "Service": "Service", "Room": "Chambre", "Package": "Pack", "Quantity": "Quantité", "FullDay": "<PERSON><PERSON><PERSON>", "HalfDay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "removeRequestLine": "Supprimer l'article", "removeRequestLineConfirm": "Êtes-vous sûr de vouloir supprimer cet article?", "remove": "<PERSON><PERSON><PERSON><PERSON>", "editing": "Modification", "updateRange": "Mettre à jour la plage", "pricePeriodUpdated": "La période de tarification a été mise à jour avec succès", "PriceBreakdown": "Détail du prix", "PricePerUnit": "Prix/Unité", "AccTax": "Taxe hébergement", "items": "Articles", "from": "De", "to": "a", "Guests": "Clients", "currency": "<PERSON><PERSON>", "cad": "CAD", "removeDeposit": "Supprimer le deposit", "removeDepositConfirm": "Êtes-vous sûr de vouloir supprimer ce deposit?", "archiveDepositSuccessMessage": "Le deposit a bien été archivé.", "send": "Envoyer", "unassigned": "Non assigné"}, "contact": {"usernamePlaceholder": "Entrez votre nom d'utilisateur", "rememberMe": "Se souvenir de moi", "enterCredentials": "Veuillez entrer vos identifiants pour accéder à votre compte", "amount": "Amount", "metaDataTitle": "Contactez-nous", "metaDataDesc": "Contactez-nous | <PERSON>", "bannerTitle": "Contactez-nous", "bannerSubTitle": "Contactez-nous", "contactTitle": "Envoyez-nous un message", "contactSubTitle": "<PERSON><PERSON><PERSON>z remplir le formulaire suivant si vous avez des questions sur nos produits.", "contactUsa": "États Unis", "contactName": "Nom ", "contactCompany": "Compagnie ", "contactPhone": "Numéro de téléphone ", "contactEmail": "<PERSON><PERSON><PERSON> ", "contactMessage": "Message ", "contactBtn": "Envoyer le formulaire", "contactSuccessmsg": "Merci pour votre message. Nous vous contacterons bientôt", "contactErrormsg": "Quelque chose s'est mal passé. Veuillez réessayer plus tard.", "placeholderName": "Nom", "placeholderCompany": "Compagnie", "placeholderPhone": "Numéro de téléphone", "placeholderMail": "Addresse courriel", "placeholderMessage": "Votre message", "contactInfoTitleP1": "Infos de contact", "contactInfoTitleP2": "contactez-nous", "contactInfoDescription": "AVous êtes à la recherche d’un partenaire pour vous aider à faire progresser votre stratégie numérique? Nous avons exactement ce dont vous avez besoin! Nous analyserons et évaluerons les projets afin de pouvoir formuler des recommandations adaptées aux besoins particuliers de chaque client.", "formTitle": "Soumettre une demande", "firstName": "Prénom", "lastName": "Nom", "name": "Nom", "description": "Description", "company": "Entreprise", "email": "<PERSON><PERSON><PERSON>", "message": "Message", "subject": "Sujet", "submit": "Envoyer votre message", "contactSubmit": "So<PERSON><PERSON><PERSON>", "phoneNumber": "Numéro de téléphone", "mobileNumber": "Numéro de mobile", "telephone": "Téléphone", "leaveComment": "Laissez un commentaire", "selectInquiry": "Sélectionnez l'inquête", "country": "Pays", "city": "Ville", "countryPlaceholder": "Sélectionnez un pays", "postalCode": "Code postal", "address": "<PERSON><PERSON><PERSON>", "preferredLang": "Langue préférée", "preferredLangPlaceholder": "Sélectionnez la langue préférée", "clientType": "Type de client", "clientTypePlaceholder": "Sélectionnez le type de client", "clientStatus": "Statut du client", "clientStatusPlaceholder": "Sélectionnez le statut du client", "province": "Province", "companyName": "Nom de l'entreprise", "comments": "Commentaires", "namePlaceholder": "Entrez votre nom", "firstNamePlaceholder": "Entrez votre prénom", "lastNamePlaceholder": "Entrez votre nom de famille", "emailPlaceholder": "Entrez votre adresse e-mail", "phonePlaceholder": "Entrez votre numéro de téléphone", "messagePlaceholder": "Entrez votre message", "requestQuote": "Obtenez une soumission gratuite", "sendRequest": "Envoyer la demande", "movingFrom": "Déménagement De", "movingTo": "Déménagement Vers", "movingDate": "Date de déménagement", "setAppointment": "Prendre un rendez-vous", "appointmentDate": "Date du rendez-vous", "jobSubTitle": "Pour soumettre une demande d'emploi, ve<PERSON><PERSON><PERSON> remplir les cases ci-dessous :", "UploadCVLabel": "Téléchargez votre CV", "UploadCVPlaceholder": "<PERSON><PERSON> le <PERSON>", "UploadOtherLabel": "Téléchargez un autre document", "UploadOtherPlaceholder": "<PERSON><PERSON> le <PERSON>", "emailCardNote": "(Pour toute question ou information supplémentaire, n'hésitez pas à envoyer votre demande à cet e-mail.)", "datePlaceholder": "Sélectionnez la date", "cityPlaceholder": "Ville/Province", "gender": "<PERSON>e", "genderPlaceholder": "Sélectionnez le sexe", "genderMale": "<PERSON><PERSON>", "genderFemale": "<PERSON>mme", "genderOther": "<PERSON><PERSON>", "birthDate": "Date de naissance", "birthDatePlaceholder": "Sélectionnez la date de naissance", "outfittersLabel": "Pourvoyeurs", "outfittersPlaceholder": "Sélectionnez un pourvoyeur", "Submit": "So<PERSON><PERSON><PERSON>", "accountVerification": "Vérification du compte", "accountEnterPassword": "Veuillez saisir un mot de passe pour votre compte.", "userName": "Nom d'utilisateur", "password": "Mot de passe", "confirmPassword": "Confirmez le mot de passe", "newPasswordPlaceholder": "Veuillez entrer votre nouveau mot de passe", "signIn": "Se connecter", "signInLoading": "Connexion en cours...", "createAccount": "<PERSON><PERSON><PERSON> un compte", "credentialsError": "Le nom d'utilisateur ou le mot de passe est incorrect", "forgetPassword": "Mot de passe oublié", "sessionExpired": "Session expirée", "passwordResetRequestedSuccess": "Réinitialisation du mot de passe demandée avec succès", "emailVerificationLinkText": "Vous recevrez un e-mail contenant le lien de vérification", "forgetPasswordQuestion": "Vous avez oublié votre mot de passe ?", "resetPassword": "Réinitialiser le mot de passe", "selectCluster": "Sélectionner un cluster", "maxFileUploadText": "La taille maximale autorisée pour une image est de 4 Mo. Seuls les fichiers avec les extensions suivantes sont acceptés : .jpg, .jpeg, .png.", "titleLabel": "Titre", "altTextLabel": "Texte alternatif", "imageLabel": "Image", "titlePlaceholder": "Entrez le titre", "altTextPlaceholder": "Entrez le texte alternatif", "imagePlaceholder": "<PERSON><PERSON> le <PERSON>", "selectCategory": "Sélectionner un catégorie", "displayOrder": "Ordre d'affichage", "activityType": "Type d'activité", "department": "Département", "selectActivityType": "Sélectionner un type d'activité", "unity": "Unité", "selectUnity": "Sélectionner l'unité", "symbol": "Symbole", "applicableTaxes": "Taxes applicables", "isAddon": "Est un complément", "mainService": "Service principal", "selectMainService": "Sélectionner un service principal", "accommodationType": "Type d'hébergement", "selectAccommodationType": "Sélectionner un type d'hébergement", "outfitter": "Pourvoiries", "selectOutfitter": "Sélectionner un pourvoirie", "accommodationId": "ID de l'hébergement", "autoGenerated": "Auto-generé", "amenities": "Amenities", "selectAmenities": "Sélectionner des amenities", "service": "Service", "selectService": "Sélectionner un service", "title": "Titre", "altText": "Texte alternatif", "roomIdentity": "Identité de la chambre", "roomId": "ID de la chambre", "roomType": "Type de chambre", "selectRoomType": "Sélectionner un type de chambre", "quantity": "Quantité", "capacity": "Capacité", "speciesClassification": "Classification des espèces", "selectSpeciesClassification": "Sélectionner la classification des espèces", "hasRestriction": "A des restrictions ?", "conservationDetail": "Détails de conservation", "profileImage": "Image de profil", "content": "Contenu", "send": "Envoyer", "useDefaultFooter": "Utiliser le pied de page par défaut ?", "date": "Date", "type": "Type", "from": "De", "source": "Source", "language": "<PERSON><PERSON>", "selectProvince": "Sélectionner la province", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "langPlaceholder": "Sélectionner la langue", "sendTo": "Envoyer à", "template": "<PERSON><PERSON><PERSON><PERSON>", "templatePlaceholder": "Sélectionner un modèle", "uploadImage": "Télécharger l'image", "addToExistingSource": "Ajouter à une source existante", "addToExistingSourcePlaceholder": "Sélectionner une source de contact", "newSourceName": "Nom de la nouvelle source", "file": "<PERSON><PERSON><PERSON>", "salutation": "Salutation", "applyExistDesign": "Appliquer ce modèle aux campagnes d'email non envoyées ?", "statusLabel": "Statut", "statusPlaceholder": "Sélectionnez un statut", "employeeLabel": "Employé", "employeePlaceholder": "Sélectionnez un employé", "serviceType": "Type de service", "selectServiceType": "Sélectionner un type de service", "serviceDuration": "Durée du service", "selectServiceDuration": "Sélectionnez la durée du service"}, "navigation": {"menuLinks": {"dashboard": "Tableau de bord", "client": "Client", "clientList": "Liste des clients", "addClient": "Ajouter un client", "requests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "webManagement": "Gestion Web", "orders": "Commandes", "offerings": "Offres", "products": "Produits", "inventory": "Inventaire", "packages": "Forfaits", "roleManagement": "Gestion des rôles", "packagesPrices": "Tarifs des forfaits", "productsPrices": "Tarifs des produits", "servicePrices": "Tarifs des services", "roomCategoryPrices": "Tarifs des catégories de chambre", "operationalSeasons": "Saisons opérationnelles", "communications": "Communications", "emailCampaigns": "Campagnes d'e-mail", "emailTemplates": "Modèles d'e-mail", "contactList": "Liste des contacts clients", "uploadContactFile": "<PERSON><PERSON>léverser un fichier de contacts", "locations": "Emplacements", "cluster": "Cluster", "territory": "<PERSON><PERSON><PERSON>", "outfitters": "Pourvoiries", "buildingTypes": "Types de bâtiments", "roomTypes": "Types de chambres", "accommodation&facilities": "Hébergement & Installations", "accommodations": "Hébergements", "bedConfigurations": "Configurations de lit", "services": "Services", "amenities": "Équipements", "mealPlanTypes": "Types de plan repas", "activities": "Activités", "interests": "Centres d'intérêt", "activityTypes": "Types d'activités", "departments": "Départements", "equipment&supplies": "Équipement & Fournitures", "equipment": "Équipement", "equipmentCategories": "Catégories d'équipement", "wildlife&nature": "Faune & Nature", "species": "Espèces", "speciesCategories": "Catégories d'espèces", "season&scheduling": "Saisons & Planification", "seasons": "Saisons", "userManagement": "Gestion des utilisateurs", "roles": "<PERSON><PERSON><PERSON>", "users": "Utilisateurs", "masterSetup": "Configuration principale", "systemSetup": "Configuration du système", "mediaTypes": "Types de médias", "taxes": "Taxes", "businessRelated": "Affaire commerciale", "department": "Département", "employeeManagement": "Gestion des employés", "emailAnnouncement": "Annonce d'e-mail", "emailCampaign": "Campagne par email", "emailCampaignDetail": "<PERSON>étail du campagne par email", "emailContactFile": "<PERSON><PERSON>er de contacts par email", "emailTemporary": "Contacts temporaires par email", "emailTemplate": "<PERSON>d<PERSON><PERSON> d'email", "emailTemplateDetail": "<PERSON>é<PERSON> du modèle d'email", "emailTemplateFooter": "Pied de page du modèle d'email", "emailContactList": "Liste des contacts clients"}}, "emailDesignPage": {"updateFooterSuccessMessage": "Le pied de page a été enregistré avec succès.", "generalInformation": "Informations Générales", "descriptionFooter": "Les informations générales sur le pied de page de l'email.", "description": "Les informations générales sur le modèle d'email.", "archiveSuccessMessage": "Le modèle d'email a été archivé avec succès.", "archiveEmailTemplate": "Archiver le modèle d'email", "archiveEmailTemplateConfirm": "Êtes-vous sûr de vouloir archiver ce modèle d'email ?", "addTemplate": "Ajouter un nouveau modèle", "warning": "Le contenu du modèle d'email sera appliqué à la campagne d'email non envoyée utilisant ce modèle. Êtes-vous sûr de vouloir continuer ?", "applyExistDesign": "Appliquer ce modèle aux campagnes d'email non envoyées ?", "updateTemplateSuccessMessage": "Le modèle d'email a été enregistré avec succès."}, "emailTemporaryContactPage": {"removeContactConfirm": "Êtes-vous sûr de vouloir supprimer ce contact ?", "removeContact": "Supprimer le contact", "removeContactSuccessMessage": "Le contact a été supprimé avec succès.", "confirmContactSuccessMessage": "Le contact a été confirmé avec succès.", "verifyContactProfile": "Vérifier le profil du contact", "confirmContact": "Quel profil de contact souhaitez-vous utiliser ?", "newClientProfile": "Nouveau profil client", "existingClientProfile": "Profil client existant", "note1": "Une fois confirmé, le contact du nouveau profil client remplacera le contact du profil client d'origine lors de la synchronisation de la liste.", "note2": "Une fois confirmé, le contact du nouveau profil client ne sera pas ajouté à votre liste de contacts client lors de la synchronisation de la liste", "note3": "sauf si vous mettez à jour leur email", "updateContactSuccessMessage": "Le contact a été mis à jour avec succès.", "tempContact": "Contact temporaire", "updateTempContact": "Mettre à jour le contact temporaire", "addTempContact": "Ajouter un contact temporaire", "errorMessage": "Erreurs : V<PERSON>ri<PERSON><PERSON> toutes les notes ci-dessous ou supprimez le contact", "syncContactProfile": "Synchroniser le contact", "syncContactConfirm": "Êtes-vous sûr de vouloir synchroniser les contacts ci-dessous ?", "syncContactSuccessMessage": "Les contacts ont été synchronisés avec succès."}, "emailContactFilePage": {"addContact": "Télécharger le fichier de contacts", "archiveSuccessMessage": "Le fichier de contacts a été archivé avec succès.", "archiveContactFile": "<PERSON><PERSON> le fichier de contacts", "archiveContactFileConfirm": "Êtes-vous sûr de vouloir archiver ce fichier de contacts ?", "uploadSuccessMessage": "Le fichier de contacts a été téléchargé avec succès.", "uploadTitle": "Télécharger le fichier de contacts", "uploadDescription": "Téléchargez le fichier de contacts ici en excel.", "contactFileDetails": "<PERSON><PERSON><PERSON> du fichier de contacts", "fileDetailsDescription": "Informations détaillées sur le fichier", "fileName": "Nom du fichier :", "uploadDate": "Date de téléchargement :", "uploadedBy": "Téléchargé par :", "originalFileName": "Nom du fichier original :", "contactSource": "Source du contact :", "synced": "Synchronisé :", "no": "Non", "syncedBy": "Synchronisé par :", "syncDate": "Date de synchronisation :", "noOfSyncedContacts": "Nombre de contacts synchronisés :"}, "emailAnnouncementPage": {"title1": "Annonces", "title2": "Détails de l'annonce", "tabs": {"tab1Title": "Informations Générales", "tab1Description": "Toutes les informations détaillées sur votre email d'annonce.", "tab2Title": "Contenu", "tab3Title": "Révision & Envoi"}, "addAnnouncement": "Ajouter une nouvelle annonce", "review": "<PERSON><PERSON><PERSON><PERSON>", "reviewDescription": "Révisez votre annonce.", "emailContent": "Contenu de l'email", "emailContentDescription": "Contenu de l'email pour votre annonce.", "announcementSuccessMessageP1": "L'annonce a été", "announcementSuccessMessageP2": "envoy<PERSON> avec succès", "contentSuccessMessage": "Le contenu a été mis à jour avec succès.", "sendAnnouncement": "Envoyer l'annonce", "sendAnnouncementConfirm": "Êtes-vous sûr de vouloir envoyer cette annonce ?"}, "emailCampaignPage": {"title1": "Campagnes", "title2": "<PERSON>é<PERSON> de la campagne", "tabs": {"tab1Title": "Informations Générales", "tab1Description": "Toutes les informations détaillées sur votre email de campagne.", "tab2Title": "Contenu", "tab3Title": "Révision & Envoi"}, "addCampaign": "Ajouter une nouvelle campagne", "review": "<PERSON><PERSON><PERSON><PERSON>", "reviewDescription": "Révisez votre campagne.", "emailContent": "Contenu de l'email", "emailContentDescription": "Contenu de l'email pour votre campagne.", "campaignSuccessMessageP1": "La campagne a été", "campaignSuccessMessageP2": "envoy<PERSON> avec succès", "contentSuccessMessage": "Le contenu a été mis à jour avec succès.", "campaignNote": "Un changement dans le modèle d'email réinitialisera le contenu et le sujet.", "campaignSentSuccessMessage": "L'email a été envoyé avec succès.", "sendCampaign": "Envoyer la campagne", "sendCampaignConfirm": "Êtes-vous sûr de vouloir envoyer cette campagne ?", "warningDescription": "Le contenu de la campagne d'email sera mis à jour avec les derniers changements du modèle d'email. Êtes-vous sûr de vouloir continuer ?"}, "clientPage": {"client": "Client", "clientList": "Liste des clients", "addClient": "Ajouter un client", "addClientContact": "Télécharger la liste des clients", "tabs": {"tab1Title": "<PERSON>il du <PERSON>", "tab2Title": "Informations personnelles", "tab3Title": "Centres d'intérêt", "tab4Title": "Abonnement", "tab5Title": "Historique des commandes"}, "clientProfileTab": {"title": "Informations du profil client", "description": "Toutes les informations détaillées sur votre client.", "clientSuccessMessageP1": "Le client a été", "clientSuccessMessageP2": " avec succès dans notre système."}, "personalInfoTab": {"clientSuccessMessage": "Les informations personnelles du client ont été mises à jour avec succès dans notre système.", "salutationLabel": "Salutation", "salutationPlaceholder": "Sélectionnez une salutation", "weightLbsLabel": "Poids en livres", "weightLbsPlaceholder": "Entrez le poids en livres", "healthIssueNoteLabel": "Note sur les problèmes de santé", "healthIssueNotePlaceholder": "Entrez une note sur les problèmes de santé"}, "interestTab": {"clientSuccessMessage": "Les centres d'intérêt du client ont été mis à jour avec succès dans notre système.", "outfittersDesc": "Sélectionnez les pourvoiries qui intéressent le client", "interestsLabel": "Centres d'intérêt", "interestsPlaceholder": "Sélectionnez les centres d'intérêt", "interestsDesc": "Sélectionnez les centres d'intérêt qui intéressent le client"}, "communicationTab": {"clientSuccessMessage": "Les informations de communication du client ont été mises à jour avec succès dans notre système.", "conditionText": "En cochant cette case, vous acceptez de recevoir des communications, des promotions et d'autres informations de notre part par e-mail et SMS dans la langue que vous avez sélectionnée.", "subscribedDate": "Date d'abonnement", "subscribedBy": "Abonné par", "unsubscribedDate": "Date de désabonnement", "unsubscribedBy": "Désabonné par"}}, "webManagementPage": {"title": "Gestion du site Web", "subtitle": "Liste des sites Web"}, "productPage": {"title": "Produits", "subtitle": "Liste des produits", "tabs": {"tab1Title": "Général", "tab2Title": "Image"}, "productGeneralTab": {"title": "Informations générales sur le produit", "description": "Description des informations générales sur le produit", "productSuccessMessageP1": "Le produit a été", "productSuccessMessageP2": "mis à jour avec succès dans notre système.", "productIdLabel": "ID du produit", "unityLabel": "Unité", "unityPlaceholder": "Sélectionnez une unité", "productTypeLabel": "Type de produit", "productTypePlaceholder": "Sélectionnez un type de produit", "applicableTaxesLabel": "Taxes applicables", "GSTLabel": "TPS", "PSTLabel": "TVP", "HSTLabel": "TVH", "GSTPlaceholder": "Entrez la TPS", "PSTPlaceholder": "Entrez la TVP", "HSTPlaceholder": "Entrez la TVH", "isAddonLabel": "Est un complément", "mainProductIdLabel": "Produit principal", "mainProductIdPlaceholder": "Sélectionnez le produit principal", "isStockLabel": "Est en stock", "stockPeriodTypeIdLabel": "Type de période de stock", "stockPeriodTypeIdPlaceholder": "Sélectionnez un type de période de stock", "stockQuantityLabel": "Quantité en stock", "stockQuantityPlaceholder": "Entrez la quantité en stock", "productStatusLabel": "Statut du produit"}, "productImageTab": {"title": "image du produit", "description": "Description des informations sur l'image du produit", "productSuccessMessageP1": "L'image du produit a été", "productSuccessMessageP2": " avec succès dans notre système."}}, "packagePage": {"title": "Forfaits", "subtitle": "Liste des forfaits", "addRoomToPackage": "Ajouter une chambre à un forfait", "tabs": {"tab1Title": "Général", "tab2Title": "Image", "tab3Title": "<PERSON><PERSON><PERSON>", "tab4Title": "Services", "tab5Title": "Produits", "tab6Title": "Contents"}, "packageGeneralTab": {"title": "Informations générales sur le forfait", "description": "Description des informations générales sur le forfait", "packageSuccessMessageP1": "Le forfait a été", "packageSuccessMessageP2": "mis à jour avec succès dans notre système.", "packageIdLabel": "ID du forfait", "priceNoteLabel": "Note sur le prix", "priceNotePlaceholder": "Entrez une note sur le prix", "detailsLabel": "Détails", "detailsPlaceholder": "Entrez les détails", "guideAvailabilityLabel": "Disponibilité du guide", "guideAvailabilityPlaceholder": "Sélectionnez la disponibilité du guide", "durationLabel": "<PERSON><PERSON><PERSON>", "durationPlaceholder": "Entrez la durée", "nightsLabel": "Nuits", "nightsPlaceholder": "Entrez le nombre de nuits", "quantityLabel": "Quantité", "quantityPlaceholder": "Entrez la quantité", "learnMoreLabel": "En savoir plus", "learnMorePlaceholder": "Entrez le lien pour en savoir plus", "packageStatusLabel": "Statut du forfait", "packageGroupCreated": "Groupe de forfaits créé avec succès", "packageGroupUpdated": "Groupe de forfaits mis à jour avec succès", "packageGroupsLabel": "Groupes de forfaits", "addNewGroup": "Ajouter un nouveau groupe", "editGroup": "Modifier le groupe", "createPackageGroup": "Créer un groupe de forfaits", "editPackageGroup": "Modifier un groupe de forfaits", "groupNameLabel": "Nom du groupe", "groupNamePlaceholder": "Entrez le nom du groupe", "minGroupSizeLabel": "Taille minimale du groupe", "minGroupSizePlaceholder": "Entrez la taille minimale du groupe", "maxGroupSizeLabel": "Taille maximale du groupe", "maxGroupSizePlaceholder": "Entrez la taille maximale du groupe (optionnel)", "applicableTaxesLabel": "Taxes applicables", "GSTLabel": "TPS", "PSTLabel": "TVP", "HSTLabel": "TVH", "roomLabel": "Chambre", "roomPlaceholder": "Sélectionnez une chambre"}, "packageImageTab": {"title": "image du forfait", "description": "Description des informations sur l'image du forfait", "packageSuccessMessageP1": "L'image du forfait a été", "packageSuccessMessageP2": " avec succès dans notre système."}, "packageRoomsTab": {"title": "Chambres du forfait", "description": "Description des informations sur les chambres du forfait", "packageRoomAddSuccessMessage": "La chambre du forfait a été ajoutée avec succès", "packageRoomUpdateSuccessMessage": "La chambre du forfait a été mise à jour avec succès", "packageRoomDeleteSuccessMessage": "La chambre du forfait a été supprimée avec succès", "modalTitle": "Ajouter une chambre au forfait", "modalDescription": "A<PERSON><PERSON>z une nouvelle chambre au forfait.", "roomLabel": "Chambre", "roomPlaceholder": "Sélectionnez une chambre"}, "packageServicesTab": {"title": "Services du forfait", "description": "Description des informations sur les services du forfait", "packageServiceAddSuccessMessage": "Le service du forfait a été ajouté avec succès", "packageServiceUpdateSuccessMessage": "Le service du forfait a été mis à jour avec succès", "packageServiceDeleteSuccessMessage": "Le service du forfait a été supprimé avec succès", "packageServiceDeleteErrorMessage": "Échec de la suppression de la chambre du forfait.", "packageServiceStatusSuccessMessage": "Le statut du service du forfait a été mis à jour avec succès", "modalTitle": "Ajouter un service au forfait", "modalDescription": "Ajoutez un nouveau service au forfait.", "serviceLabel": "Service", "servicePlaceholder": "Sélectionnez un service", "serviceType": "Type de service", "serviceTypePlaceholder": "Sélectionnez un type de service", "unknown": "Inconnu", "durationPlaceholder": "Sélectionnez la durée"}, "packageProductsTab": {"title": "Produits du forfait", "description": "Description des informations sur les produits du forfait", "packageProductAddSuccessMessage": "Le produit du forfait a été ajouté avec succès", "packageProductUpdateSuccessMessage": "Le produit du forfait a été mis à jour avec succès", "packageProductDeleteSuccessMessage": "Le produit du forfait a été supprimé avec succès", "modalTitle": "Ajouter un produit au forfait", "modalDescription": "Ajoutez un nouveau produit au forfait.", "productLabel": "Produit", "productPlaceholder": "Sélectionnez un produit", "packageProductLabel": "Produit du forfait", "cardView": "Vue carte", "tableView": "<PERSON><PERSON> tableau", "product": "Produit", "service": "Service", "noItems": "Aucun élément dans le forfait.", "itemTypeFallback": "Produit"}}, "orderPage": {"title": "Commandes", "subTitle": "Liste des commandes", "editTitle": "<PERSON><PERSON><PERSON> de la commande", "addNewTitle": "Ajouter une nouvelle commande", "orderInformation": "Informations sur la commande", "guests": "List des invités", "addGuest": "Ajouter un invité", "addGuestModalDescription": "Ajou<PERSON>z un nouvel invité", "firstNameLabel": "Prénom", "lastNameLabel": "Nom", "emailLabel": "E-mail", "isAdultLabel": "Marker cette case si l'invité est plus de 16 ans", "removeGuest": "Supprimer l'invité", "removeGuestConfirm": "Êtes-vous sûr de vouloir supprimer cet invité ?", "archiveSuccessMessage": "L'invité a été supprimé avec succès.", "save": "<PERSON><PERSON><PERSON><PERSON>", "addGuestSuccessMessage": "L'information de l'invité a été ajouté avec succès.", "paymentInformation": "Informations de paiement", "loading": "Chargement...", "noPayments": "Aucun paiement", "addPaymentSuccessMessage": "Le paiement a été ajouté avec succès.", "addPayment": "Ajouter un paiement", "addPaymentModalDescription": "Ajoutez un nouveau paiement", "paymentDate": "Date de paiement", "amount": "<PERSON><PERSON>", "remainingBalance": "Solde restant", "paymentMethodId": "Méthode de paiement", "description": "Description", "authorizationNumber": "Numéro d'autorisation", "cardNumber": "Carte (4 derniers chiffres)", "checkNumber": "Numéro de chèque", "institution": "Institution", "confirmationNum": "Num<PERSON><PERSON>", "selectPaymentMethod": "Sélectionnez une methode de paiement", "currentBalance": "Solde actuel", "newBalance": "Nouveau solde", "paymentNote": "Une fois que vous cliquez sur Enregistrer, ce paiement sera ajou<PERSON>, mais vous ne pourrez pas le supprimer ni le modifier."}, "requestPage": {"title": "<PERSON><PERSON><PERSON>", "subTitle": "Liste des demandes", "editTitle": "<PERSON><PERSON><PERSON> de la demande", "addNewTitle": "Ajouter une nouvelle demande", "statusUpdated": "Request status has been updated successfully", "updateRequestDetails": "Ajouter statut de la demande", "update": "Statut de la demande", "assign": "Assigner un employé", "updateAssign": "Selectionnez l'employé qui va traiter la demande", "titleImportantNote": "Note importante", "updateImportantNote": "Entrer une note importante", "uploadTitle": "Télécharger le fichier de devis", "uploadDescription": "Veuillez télécharger votre fichier de devis ainsi que les détails pertinents ci-dessous.", "uploadSuccessMessage": "Votre devis a été téléchargé avec succès.", "updateAmountAndTaxes": "* La mise à jour du montant et des taxes ci-dessous sera reflétée dans le calendrier des dépôts.", "transferRequest": "Transferer vers une commande", "transferRequestConfirm": "Voulez-vous vraiment transferrer cette demande vers une commande?", "requestDetailPage": {"guestTable": {"id": "ID", "firstName": "Prénom", "lastName": "Nom", "email": "E-mail", "isAdult": "<PERSON>e (16+)", "noGuestsFound": "Au<PERSON><PERSON> invité trouvé", "actions": "Actions"}, "noDeposits": "Aucun depot requis pour cette demande. Veuillez télécharger votre devis.", "editDeposit": "Modifier le dépôt", "depositAmountLabel": "Montant du dépôt (CAD $)", "depositAmountPlaceholder": "En<PERSON><PERSON> le montant du dépô<PERSON>", "dueDateLabel": "Date d'échéance", "dueDatePlaceholder": "Sélectionnez la date d'échéance", "saveDeposit": "Enregistrer le dépô<PERSON>", "depositSuccessMessage": "Le dépôt a été enregistré avec succès", "depositDescription": "Remplissez le montant du dépôt et la date d'échéance. Le pourcentage sera calculé automatiquement en fonction du montant total.", "title": "<PERSON><PERSON><PERSON> de la demande", "internalCommunication": "Communication interne", "description": "Toutes les informations détaillées sur la demande.", "requestContentSummary": "Sommaire de la demande", "halfDay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullDay": "<PERSON><PERSON><PERSON>", "noItemsInTheRequest": "Aucun article dans la demande.", "requestInformation": "Informations sur la demande", "status": "Statut", "assignedTo": "Attribué à", "assignedDate": "Date d'attribution", "createdDate": "Date de création", "updatedDate": "Date de mise à jour", "outfitter": "Pourvoiries", "clientName": "Nom du client", "phoneNumber": "Numéro de téléphone", "email": "E-mail", "numberOfAdults": "Nombre d'adultes", "numberOfChildren": "Nombre d'enfants", "importantNote": "Note importante", "unassigned": "Non attribué", "noRequestDetails": "Au<PERSON>n dé<PERSON> de demande disponible.", "edit": "Modifier", "quotations": "<PERSON><PERSON>", "uploadNewQuotation": "Télécharger un nouveau devis", "viewQuotationHistory": "Voir l'historique des devis", "viewEstimatedPrice": "Voir le prix estimé", "price": "Prix", "taxes": "Taxes", "amount": "<PERSON><PERSON>", "totalPriceBeforeTax": "Prix total avant taxes", "totalFinalPrice": "Prix total final", "noItemsInRequest": "Aucun article dans la demande.", "titleInternalNote": "Notes internes", "addNotePlaceholder": "Ajouter une nouvelle note interne", "addNoteButton": "Ajouter une note", "viewNoteHistory": "Voir l'historique des notes internes", "noNotesAvailable": "Aucune note disponible", "successMessage": "Note créée avec succès!", "writtenBy": "Écrit par", "updatedBy": "Mis à jour par", "titleExternalMessage": "Messages externes", "sendMessagePlaceholder": "Écrire un message au client", "sendMessageButton": "Envoyer", "viewMessageHistory": "Voir l'historique des messages externes", "noMessagesAvailable": "Aucun message disponible", "clientMessagePrefix": "Client -", "adminMessagePrefix": "Admin -", "successMessageExternal": "Message envoyé avec succès!", "noteCreatedSuccess": "Note créée avec succès!", "on": "le", "depositSchedule": "Calendrier des Dépôts", "currency": "<PERSON><PERSON>", "totalAfterTax": "Total Après Taxes", "dueDate": "Date d'Échéance", "deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addDeposit": "A<PERSON>ter un Dépôt", "totalAmount": "Montant Total", "quotationFileHistory": {"id": "ID", "filePath": "<PERSON><PERSON><PERSON> du <PERSON>", "uploadedAt": "Télécha<PERSON><PERSON> le", "uploadedBy": "Téléchargé par", "download": "Télécharger", "noHistoryFound": "Aucun historique de fichier de devis trouvé"}}}, "accommodationPage": {"title": "Hébergement", "subTitle": "Liste des hébergements", "editTitle": "Modifier l'hébergement", "addNewTitle": "Ajouter un nouvel hébergement", "tabs": {"tab1Title": "Général", "tab2Title": "<PERSON><PERSON><PERSON>", "tab3Title": "Amenities", "tab4Title": "Services"}, "addNewAccommodation": "Ajouter un nouvel hébergement", "accommodationUpdated": "L'hébergement a été mis à jour avec succès", "accommodationCreated": "L'hébergement a été créé avec succès", "accommodationDetailPage": {"title": "Informations sur l'accommodation", "description": "Description des informations sur l'accommodation"}, "accommodationGeneralTab": {"title": "Informations générales sur l'accommodation", "description": "Description des informations générales sur l'accommodation"}, "amenityTab": {"title": "Amenities", "description": "Ajoutez des amenities pour l'hébergement", "amenitySuccessMessage": "L'hébergement's amenities a mis à jour avec suceusement"}, "serviceTab": {"title": "Services", "description": "Ajoutez des amenities pour l'hébergement", "serviceSuccessMessage": "L'hébergement's services a mis à jour avec suceusement", "outfitterService": "Services de pourvoirie"}, "roomTab": {"roomCategories": "Catégories de chambres", "addRoomCategory": "Ajouter une catégorie de chambre", "roomsOf": "chambres de", "roomOf": "chambre de", "noRoomCategoriesAssociated": "Aucune catégorie de chambre associée", "noRoomsMessage": "Il n'y a actuellement aucune catégorie de chambre associée à cet hébergement.", "addRoomsHelp": "L'ajout de chambres à votre hébergement permet aux clients potentiels de comprendre les options disponibles et de prendre des décisions éclairées.", "imageSelectionTitle": "Sélectionner une image principale pour la chambre", "mainImageSuccessMessage": "L'image principale a été définie avec succès", "physicalRooms": "<PERSON><PERSON><PERSON> physiques", "addPhysicalRoom": "Ajouter une chambre physique", "roomId": "ID ch.", "physicalRoom": "Chambre physique", "updatePhysicalRoom": "Mettre à jour les détails de la chambre physique", "addNewPhysicalRoom": "Ajouter une nouvelle chambre physique", "physicalRoomCreated": "La chambre physique a été créée avec succès", "physicalRoomUpdated": "La chambre physique a été mise à jour avec succès", "room": "Chambre", "roomUpdateDescription": "Mettre à jour les détails de la chambre", "roomCreateDescription": "Ajouter une nouvelle chambre", "roomCreated": "La chambre a été créée avec succès", "roomUpdated": "La chambre a été mise à jour avec succès"}}, "speciesPage": {"title": "Espèces", "subTitle": "Liste des espèces", "speciesUpdated": "L'espèce a été mise à jour avec succès", "speciesCreated": "L'espèce a été créée avec succès", "species": "Espèce", "updateSpeciesDetails": "Mettre à jour les détails de l'espèce", "addNewSpecies": "Ajouter une nouvelle espèce", "editTitle": "Mettre à jour l'espèce", "addNewTitle": "Ajouter une nouvelle espèce", "speciesDetailPage": {"title": "Détails de l'espèce", "description": "Description de l'espèce"}, "tabs": {"tab1Title": "Informations générales", "tab2Title": "Image"}, "imageTab": {"mainImageSuccessMessage": "L'image de l'espèce a été définie avec succès"}}, "packagesPricesPage": {"title": "Tarifs des forfaits", "subtitle": "Liste des forfaits", "packageLoadError": "Échec du chargement des forfaits", "packageInfo": "Informations sur le forfait", "pricingGroupPlaceholder": "Sélectionnez un groupe de tarification", "groupPlaceholder": "Sélectionnez un groupe"}, "productsPricesPage": {"title": "Tarifs des produits", "subtitle": "Liste des produits", "productLoadError": "Échec du chargement des produits", "productInfo": "Informations sur le produit"}, "servicePricesPage": {"title": "Tarifs des services", "subtitle": "Liste des services", "serviceLoadError": "Échec du chargement des services", "serviceInfo": "Informations sur le service", "fullDayPrice": "Prix pour la journée complète", "fullDayPricePlaceholder": "Entrez le prix pour la journée complète", "halfDayPrice": "Prix pour la demi-journée", "halfDayPricePlaceholder": "Entrez le prix pour la demi-journée", "outfitterDetailText": "Liste des prix du jour", "addService": "Ajouter un service", "accommodationPriceList": "Liste des prix de l'hébergement", "addNewAccommodation": "Ajouter un nouvel hébergement", "noAccommodation": "Aucun hébergement associé", "noAccommodationDescription": "Il n'y a actuellement aucun hébergement associé à ce pourvoyeur.", "noAccommodationHint": "Ajouter un hébergement à votre pourvoyeur aide les futurs clients à comprendre les options disponibles et à prendre des décisions éclairées.", "serviceUpdatedSuccessMessage": "Ce service a été mis à jour avec succès", "serviceFreeAgreement": "* Cochez la case ci-dessous si vous souhaitez offrir ce service gratuitement.", "serviceFreeAgreementFooter": "La mise à jour du prix dans cette section sera appliquée à tous les hébergements de ce pourvoyeur."}, "roomCategoryPricesPage": {"title": "Tarifs des categories de chambre", "subtitle": "Liste des categories de chambre", "roomCategoryLoadError": "Échec du chargement des categories de chambre", "roomCategoryInfo": "Informations sur la category de chambre"}, "outfitterPage": {"title": "Pourvoirie", "subtitle": "Liste des pourvoyeurs", "tabs": {"tab1Title": "Informations sur l'entreprise", "tab2Title": "<PERSON><PERSON><PERSON>", "tab3Title": "Informations sur la conservation", "tab4Title": "Services", "tab5Title": "Hébergements"}, "corporateInfoTab": {"title": "Informations sur l'entreprise", "description": "Toutes les informations détaillées sur votre pourvoyeur.", "outfitterSuccessMessageP1": "Le pourvoirie a été", "outfitterSuccessMessageP2": "mis à jour avec succès dans notre système.", "corporateNameLabel": "Nom de l'entreprise", "corporateNamePlaceholder": "Entrez le nom de l'entreprise", "licenseNumberLabel": "Numéro de licence", "licenseNumberPlaceholder": "Entrez le numéro de licence", "phoneNumberLabel": "Numéro de téléphone", "phoneNumberPlaceholder": "Entrez le numéro de téléphone", "latitudeLabel": "Latitude", "latitudePlaceholder": "Entrez la latitude", "longitudeLabel": "Longitude", "longitudePlaceholder": "Entrez la longitude", "territoryLabel": "<PERSON><PERSON><PERSON>", "territoryPlaceholder": "Sélectionnez un territoire", "symbolLabel": "Symbole", "symbolPlaceholder": "Entrez un symbole", "addressLabel": "<PERSON><PERSON><PERSON>", "addressPlaceholder": "Entrez l'adresse", "cityLabel": "Ville", "cityPlaceholder": "Entrez la ville", "stateLabel": "État", "statePlaceholder": "Entrez l'état", "postalCodeLabel": "Code postal", "postalCodePlaceholder": "Entrez le code postal", "provinceLabel": "Province", "provincePlaceholder": "Sélectionnez une province", "countryLabel": "Pays", "countryPlaceholder": "Sélectionnez un pays", "mailingAddressLabel": "Adresse postale"}, "territoryTab": {"title": "Informations sur le territoire", "description": "Toutes les informations détaillées sur le territoire de votre pourvoyeur.", "outfitterSuccessMessageP1": "Le pourvoirie a été", "outfitterSuccessMessageP2": " avec succès dans notre système.", "descriptionLabel": "Description", "descriptionPlaceholder": "Entrez la description", "territorySizeKmLabel": "Superficie du territoire (km)", "territorySizeKmPlaceholder": "Entrez la superficie du territoire (km)", "lakeAreaKmLabel": "Superficie des lacs (km)", "lakeAreaKmPlaceholder": "Entrez la superficie des lacs (km)", "riversAreaKmLabel": "Superficie des rivières (km)", "riversAreaKmPlaceholder": "Entrez la superficie des rivières (km)", "numFishingZonesLabel": "Nombre de zones de pêche", "numFishingZonesPlaceholder": "Entrez le nombre de zones de pêche", "numHuntingZonesLabel": "Nombre de zones de chasse", "numHuntingZonesPlaceholder": "Entrez le nombre de zones de chasse"}, "conservationInfoTab": {"title": "Informations sur la conservation", "description": "Toutes les informations détaillées sur la conservation de votre pourvoyeur.", "outfitterSuccessMessageP1": "Le pourvoirie a été", "outfitterSuccessMessageP2": " avec succès dans notre système.", "detailsLabel": "Détails", "detailsPlaceholder": "Entrez les détails"}, "servicesTab": {"title": "Services", "description": "Toutes les informations détaillées sur les services de votre pourvoyeur.", "outfitterSuccessMessage": "Les services du pourvoyeur ont été mis à jour avec succès", "servicesLabel": "Services", "servicesPlaceholder": "Sélectionnez des services"}, "accommodationsTab": {"title": "Hébergements", "description": "Toutes les informations détaillées sur les hébergements de votre pourvoyeur."}}, "seasonPage": {"seasonTitle": "<PERSON><PERSON>", "seasonListTitle": "Liste des Saisons", "season": "<PERSON><PERSON>", "updateSeasonDetails": "Mettre à jour les détails de la saison", "addNewSeasonType": "Ajouter un nouveau type de saison", "seasonCreated": "La saison a été créée avec succès", "seasonUpdated": "La saison a été mise à jour avec succès"}, "territoryPage": {"territoriesTitle": "Territoires", "territoriesListTitle": "Liste des Territoires", "cluster": "Cluster", "territory": "<PERSON><PERSON><PERSON>", "updateTerritoryDetails": "Mettre à jour les détails du territoire", "addNewTerritory": "Ajouter un nouveau territoire", "territoryCreated": "Le territoire a été créé avec succès", "territoryUpdated": "Le territoire a été mis à jour avec succès"}, "roomTypePage": {"roomTypeTitle": "Type de chambre", "roomTypeListTitle": "Liste des Type de chambre", "roomType": "Type de chambre", "updateRoomTypeDetails": "Mettre à jour les détails du type de chambre", "addNewRoomType": "Ajouter un nouveau type de chambre", "roomTypeCreated": "Le type de chambre a été créé avec succès", "roomTypeUpdated": "Le type de chambre a été mis à jour avec succès"}, "mediaTypePage": {"mediaTypeTitle": "Type de média", "mediaTypeListTitle": "Liste des Type de média", "mediaType": "Type de média", "updateMediaTypeDetails": "Mettre à jour les détails du type de média", "addNewMediaType": "Ajouter un nouveau type de média", "mediaTypeCreated": "Le type de média a été créé avec succès", "mediaTypeUpdated": "Le type de média a été mis à jour avec succès"}, "clusterPage": {"clusterTitle": "Cluster", "clusterListTitle": "Liste des Cluster", "cluster": "Cluster", "updateClusterDetails": "Mettre à jour les détails du cluster", "addNewCluster": "Ajouter un nouveau cluster", "clusterCreated": "Le cluster a été créé avec succès", "clusterUpdated": "Le cluster a été mis à jour avec succès"}, "buildingTypePage": {"buildingTypeTitle": "Type de bâtiment", "buildingTypeListTitle": "Liste des Type de bâtiment", "buildingType": "Type de bâtiment", "updateBuildingTypeDetails": "Mettre à jour les détails du type de bâtiment", "addNewBuildingType": "Ajouter un nouveau type de bâtiment", "buildingTypeCreated": "Le type de bâtiment a été créé avec succès", "buildingTypeUpdated": "Le type de bâtiment a été mis à jour avec succès"}, "activityTypePage": {"activityTypeTitle": "Type d'Activité", "activityTypeListTitle": "Liste des Types d'Activités", "activityType": "Type d'Activité", "updateActivityTypeDetails": "Mettre à jour les détails du type d'activité", "addNewActivityType": "Ajouter un nouveau type d'activité", "activityTypeCreated": "Le type d'activité a été créé avec succès", "activityTypeUpdated": "Le type d'activité a été mis à jour avec succès"}, "departmentPage": {"departmentTitle": "Département", "departmentListTitle": "Liste des Département", "department": "Département", "updateDepartmentDetails": "Mettre à jour les détails du département", "addNewDepartment": "Ajouter un nouveau département", "departmentCreated": "Département a été créé avec succès", "departmentUpdated": "Département a été mis à jour avec succès"}, "amenityPage": {"amenityTitle": "Aménité", "amenityListTitle": "Liste des Aménités", "amenity": "Aménité", "updateAmenityDetails": "Mettre à jour les détails de l'aménité", "addNewAmenity": "Ajouter un nouveau aménité", "amenityCreated": "L'aménité a été créée avec succès", "amenityUpdated": "L'aménité a été mise à jour avec succès"}, "bedConfigPage": {"bedConfigTitle": "Configuration de Lit", "bedConfigListTitle": "Liste des Configurations de Lit", "bedConfig": "Configuration de Lit", "updateBedConfigDetails": "Mettre à jour les détails de la configuration de lit", "addNewBedConfig": "Ajouter une nouvelle configuration de lit", "bedConfigCreated": "La configuration de lit a été créée avec succès", "bedConfigUpdated": "La configuration de lit a été mise à jour avec succès"}, "equipmentPage": {"equipmentTitle": "Équipement", "equipmentListTitle": "Liste des Équipements", "equipment": "Équipement", "updateEquipmentDetails": "Mettre à jour les détails de l'équipement", "addNewEquipment": "Ajouter un nouvel équipement", "equipmentCreated": "L'équipement a été créé avec succès", "equipmentUpdated": "L'équipement a été mis à jour avec succès"}, "equipmentCategoryPage": {"equipmentCategoryTitle": "Catégories d'équipement", "equipmentCategoryListTitle": "Liste des catégories d'équipement", "equipmentCategory": "Catégorie d'équipement", "updateEquipmentCategoryDetails": "Mettre à jour les détails de la catégorie d'équipement", "addNewEquipmentCategory": "Ajouter une nouvelle catégorie d'équipement", "equipmentCategoryCreated": "La catégorie d'équipement a été créée avec succès", "equipmentCategoryUpdated": "La catégorie d'équipement a été mise à jour avec succès"}, "interestPage": {"interestTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interestListTitle": "Liste des Intérêts", "interest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateInterestDetails": "Mettre à jour les détails de l'intérêt", "addNewInterest": "Ajouter un nouvel intérêt", "interestCreated": "L'intérêt a été créé avec succès", "interestUpdated": "L'intérêt a été mis à jour avec succès"}, "servicePage": {"serviceTitle": "Service", "serviceListTitle": "Liste des Services", "service": "Service", "updateServiceDetails": "Mettre à jour les détails du service", "addNewService": "Ajouter un nouveau service", "serviceCreated": "Le service a été créé avec succès", "serviceUpdated": "Le service a été mis à jour avec succès"}, "specieClassificationPage": {"specieClassificationTitle": "Classification des espèces", "specieClassificationListTitle": "Liste des classifications des espèces", "specieClassification": "Classification des espèces", "updateSpecieClassificationDetails": "Mettre à jour les détails de la classification des espèces", "addNewSpecieClassification": "Ajouter une nouvelle classification des espèces", "specieClassificationCreated": "La classification des espèces a été créée avec succès", "specieClassificationUpdated": "La classification des espèces a été mise à jour avec succès"}, "mealPlanTypePage": {"mealPlanTypeTitle": "Type de Plan de Repas", "mealPlanTypeListTitle": "Liste des Types de Plans de Repas", "mealPlanType": "Type de Plan de Repas", "updateMealPlanTypeDetails": "Mettre à jour les détails du type de plan de repas", "addNewMealPlanType": "Ajouter un nouveau type de plan de repas", "mealPlanTypeCreated": "Le type de plan de repas a été créé avec succès", "mealPlanTypeUpdated": "Le type de plan de repas a été mis à jour avec succès"}, "taxesPage": {"title": "Taxes", "subtitle": "Liste des taxes", "addNewTax": "Ajouter une nouvelle taxe", "updateTaxDetails": "Mettre à jour les détails de la taxe", "taxRate": "Taux de taxe", "taxCreated": "La taxe a été créée avec succès", "taxUpdated": "La taxe a été mise à jour avec succès", "taxNameLabel": "Nom de la taxe", "taxNamePlaceholder": "Entrez le nom de la taxe", "taxRateLabel": "Taux de taxe", "taxRatePlaceholder": "Entrez le taux de taxe", "taxDescriptionLabel": "Description de la taxe", "taxDescriptionPlaceholder": "Entrez la description de la taxe", "submitToLabel": "<PERSON><PERSON><PERSON><PERSON> à", "submitToPlaceholder": "Entrez le lieu de soumettre à", "isActiveLabel": "Est actif", "isActivePlaceholder": "Choisissez est actif"}, "usersPage": {"title": "Utilisateurs", "subtitle": "Liste des utilisateurs", "addNewEmployee": "Ajouter un nouvel employé", "addNewClient": "Ajouter un nouveau client", "selectRole": "Sélectionnez un rôle", "updateRole": "Mettre à jour le rôle", "selectNewRole": "Sélectionnez un rôle depuis la liste", "cannotChangeClientRole": "Vous ne pouvez pas changer le rôle d'un client"}, "employeePage": {"title": "Employé", "subtitle": "Liste des employés", "addNewEmployee": "Ajouter un nouvel employé", "updateEmployeeDetails": "Mettre à jour les détails de l'employé", "employeeCreated": "L'employé a been successfully created", "employeeUpdated": "L'employé a been successfully updated", "employeeManagement": "Gestion des employés", "createEmployee": "<PERSON><PERSON><PERSON> un employé", "updateEmployee": "Mettre à jour l'employé", "form": {"firstNameLabel": "Prénom", "firstNamePlaceholder": "Entrez le prénom", "lastNameLabel": "Nom", "lastNamePlaceholder": "Entrez le nom", "addressLabel": "<PERSON><PERSON><PERSON>", "addressPlaceholder": "Entrez l'adresse", "cityLabel": "Ville", "cityPlaceholder": "Entrez la ville", "provinceLabel": "Province", "provincePlaceholder": "Sélectionnez une province", "departmentLabel": "Département", "departmentPlaceholder": "Sélectionnez un département", "postalCodeLabel": "Code postal", "postalCodePlaceholder": "Entrez le code postal", "emergencyContactLabel": "Contact d'urgence", "emergencyContactPlaceholder": "Entrez un contact d'urgence", "emergencyPhoneNumberLabel": "Numéro de téléphone d'urgence", "emergencyPhoneNumberPlaceholder": "Entrez le numéro de téléphone d'urgence", "workEmailLabel": "E-mail professionnel", "workEmailPlaceholder": "Entrez l'e-mail professionnel", "workPhoneNumberLabel": "Numéro de téléphone professionnel", "workPhoneNumberPlaceholder": "Entrez le numéro de téléphone professionnel", "statusLabel": "Statut", "statusPlaceholder": "Sélectionnez un statut", "preferredLangLabel": "Langue préférée", "preferredLangPlaceholder": "Sélectionnez une langue préférée", "salutationLabel": "Salutation", "salutationPlaceholder": "Sélectionnez une salutation", "genderLabel": "Genre", "genderPlaceholder": "Sélectionnez un genre", "birthDateLabel": "Date de naissance", "birthDatePlaceholder": "Sélectionnez une date de naissance", "mobileNumberLabel": "Numéro de mobile", "mobileNumberPlaceholder": "Entrez le numéro de mobile", "personalEmailLabel": "E-mail personnel", "personalEmailPlaceholder": "Entrez l'e-mail personnel"}}, "ErrorPage": {"otherErrorsMessage": "Une erreur est survenue", "notFoundMessage": "<PERSON><PERSON><PERSON>, la page que vous recherchez n'existe pas.", "goBackText": "<PERSON><PERSON><PERSON>", "goHomeText": "Accueil"}}