import * as z from 'zod';

export const UserSchema = z.object({
  statusId: z.string().optional(),
  salutationId: z.string().optional(),
  firstName: z.string(),
  lastName: z.string(),
  mobileNumber: z.string().optional(),
  departmentId: z.string().optional(),
  verificationEmail: z.string(),
  workEmail: z.string().optional(),
  workPhoneNumber: z.string().optional(),
  archive: z.boolean(),
});
export type UserSchemaData = z.infer<typeof UserSchema>;
