import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import PropertyQuery from '@/services/queries/PropertyQuery';
import { getQueryClient } from '@/utils/query-client';
import PropertyGeneralInfo from './components/property_general_info';

export default async function PropertyPage({
  params,
}: {
  params: Promise<{ id: string; locale: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;

    const client = getQueryClient();

    if (id !== 'add') {
      const numericId = Number(id);
      if (Number.isNaN(numericId)) throw new Error();

      await client.prefetchQuery({
        queryKey: ['Property', { id: numericId }],
        queryFn: () => PropertyQuery.get(numericId),
      });
    }

    return (
      <div>
        <HydrationBoundary state={dehydrate(client)}>
          <PropertyGeneralInfo
            id={Number.isNaN(Number(id)) ? undefined : Number(id)}
          />
        </HydrationBoundary>
      </div>
    );
  } catch (error) {
    redirect('/dashboard/setup/master-setup/property/add');
  }
}
