import * as React from 'react';
import { cn } from '@/lib/utils';

export interface StandaloneInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
}

const StandaloneInput = React.forwardRef<
  HTMLInputElement,
  StandaloneInputProps
>(({ className, type, error, ...props }, ref) => {
  return (
    <input
      type={type}
      className={cn(
        'flex w-full text-foreground text-sm shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:border-primary disabled:cursor-not-allowed disabled:opacity-50',
        'p-2 border border-gray-300 rounded-sm',
        error && 'border-red-500',
        className,
      )}
      ref={ref}
      {...props}
    />
  );
});
StandaloneInput.displayName = 'StandaloneInput';

export { StandaloneInput };
