import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import CompanyQuery from '@/services/queries/CompanyQuery';
import { getQueryClient } from '@/utils/query-client';
import CompanyForm from './components/company_form/company-form';
import AppLayout from '@/components/ui/app_layout';

export default async function CompanyPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;
    const isAdd = id === 'add';

    const client = getQueryClient();

    if (!isAdd) {
      if (Number.isNaN(Number(id))) throw new Error();
      await client.prefetchQuery({
        queryKey: [...CompanyQuery.tags, { id: Number(id) }],
        queryFn: () => CompanyQuery.getOne(Number(id)),
      });
    }

    const breadcrumbItems = [
      { title: 'Setup', link: '/dashboard/setup' },
      { title: 'Master-Setup', link: '/dashboard/setup/master-setup' },
      {
        title: 'Exhibitor Companies',
        link: '/dashboard/setup/master-setup/exhibitor-company',
      },
      {
        title: isAdd ? 'Add Company' : 'Edit Company',
        link: `/dashboard/setup/master-setup/exhibitor-company/${id}`,
      },
    ];

    return (
      <AppLayout items={breadcrumbItems}>
        <HydrationBoundary state={dehydrate(client)}>
          <CompanyForm id={isAdd ? undefined : Number(id)} />
        </HydrationBoundary>
      </AppLayout>
    );
  } catch (error) {
    redirect('/dashboard/setup/master-setup/exhibitor-company/add');
  }
}
