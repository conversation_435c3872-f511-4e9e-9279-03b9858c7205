'use client';

import { useQuery } from '@tanstack/react-query';
import { Edit2 } from 'lucide-react';
import { FaPlus } from 'react-icons/fa';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Link } from '@/utils/navigation';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import PropertyQuery from '@/services/queries/PropertyQuery';
import { Property } from '@/models/Property';

export const PropertyTable = () => {
  const { data, isLoading } = useQuery({
    queryKey: PropertyQuery.tags,
    queryFn: PropertyQuery.getAll,
  });

  const columns = generateTableColumns<Property>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      code: { name: 'Code', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      description: { name: 'Description', type: 'text', sortable: false },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <Link
              href={`/dashboard/setup/master-setup/property/${row.id ?? 'add'}`}
            >
              <Button
                size="sm"
                variant="secondary"
                iconName="EditIcon"
              ></Button>
            </Link>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<Property>({
    name: { name: 'Name', type: 'text' },
    code: { name: 'Code', type: 'text' },
    description: { name: 'Description', type: 'text' },
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      filterFields={filters}
      isLoading={isLoading}
      controls={
        <Link href={`/dashboard/setup/master-setup/property/add`}>
          <Button variant="primary" iconName="AddIcon">
            Add New Property
          </Button>
        </Link>
      }
    />
  );
};

export default PropertyTable;
