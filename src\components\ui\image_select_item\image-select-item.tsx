import { cn } from '@/lib/utils';
import { ImageData } from '@/models/ImageData';

import AppImage from '../app_image';
interface IImageSelectItem {
  checked?: boolean;
  image: ImageData;
  useBorder?: boolean;
  imageSize?: number;
  controls?: React.ReactNode;
  badges?: React.ReactNode;
}

function ImageSelectItem({
  checked,
  image,
  useBorder,
  imageSize = 250,
  controls,
  badges,
}: IImageSelectItem) {
  return (
    <div
      className={cn(
        'flex flex-col gap-6 items-center   shadow-md transition-all duration-300 outline outline-transparent   ',
        '  relative group  ',
        checked &&
          useBorder &&
          'outline-blue-900 outline-dashed p-6 hover:outline-2 hover:outline- hover:outline-blue-700 ',
      )}
      style={{ width: imageSize, height: imageSize }}
    >
      <AppImage
        image={image}
        width={imageSize}
        height={imageSize}
        fill={false}
      />
      <div className="absolute top-4 left-4  flex flex-row gap-3  ">
        {badges}
      </div>
      {controls && (
        <div
          className={cn(
            'hidden group-hover:flex flex-row justify-between px-5  absolute bottom-[-1px] py-3 ',
            'w-full items-center transition-all duration-300 backdrop-blur-sm backdrop-brightness-125 ',
          )}
        >
          {image.name && (
            <span className="text-white text-sm font-medium  ">
              {image.name}
            </span>
          )}
          {controls}
        </div>
      )}
    </div>
  );
}

export default ImageSelectItem;
