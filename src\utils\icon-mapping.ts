import * as ReactIconsBi from 'react-icons/bi';
import * as ReactIconsFa from 'react-icons/fa';
import * as ReactIconsFa6 from 'react-icons/fa6';
import { GiDeerHead, GiTakeMyMoney } from 'react-icons/gi';
import * as ReactIconsIo from 'react-icons/io';
import { LuPackageOpen } from 'react-icons/lu';
import * as ReactIconsMd from 'react-icons/md';

export const iconMapping: {
  [key: string]: React.ComponentType<any> | undefined;
} = {
  BiSolidDashboard: ReactIconsBi.BiSolidDashboard,
  BiCategory: ReactIconsBi.BiCategory,
  BiMailSend: ReactIconsBi.BiMailSend,
  BiPackage: ReactIconsBi.BiPackage,
  BiSolidBuildingHouse: ReactIconsBi.BiSolidBuildingHouse,

  MdDesignServices: ReactIconsMd.MdDesignServices,
  MdFolderOpen: ReactIconsMd.MdFolderOpen,
  MdOutlineMyLocation: ReactIconsMd.MdOutlineMyLocation,
  MdOutlineSensorOccupied: ReactIconsMd.MdOutlineSensorOccupied,
  MdRoomService: ReactIconsMd.MdRoomService,

  FaBoxOpen: ReactIconsFa.FaBoxOpen,
  FaBuilding: ReactIconsFa.FaBuilding,
  FaCamera: ReactIconsFa.FaCamera,
  FaCanadianMapleLeaf: ReactIconsFa.FaCanadianMapleLeaf,
  FaDog: ReactIconsFa.FaDog,
  FaDollarSign: ReactIconsFa.FaDollarSign,
  FaFileImage: ReactIconsFa.FaFileImage,
  FaGlobe: ReactIconsFa.FaGlobe,
  FaHammer: ReactIconsFa.FaHammer,
  FaLeaf: ReactIconsFa.FaLeaf,
  FaShoePrints: ReactIconsFa.FaShoePrints,
  FaStore: ReactIconsFa.FaStore,
  FaSun: ReactIconsFa.FaSun,
  FaToolbox: ReactIconsFa.FaToolbox,
  FaTree: ReactIconsFa.FaTree,
  FaTv: ReactIconsFa.FaTv,
  FaWrench: ReactIconsFa.FaWrench,

  IoMdCloudUpload: ReactIconsIo.IoMdCloudUpload,
  IoMdPeople: ReactIconsIo.IoMdPeople,
  IoMdPerson: ReactIconsIo.IoMdPerson,
  IoMdPhonePortrait: ReactIconsIo.IoMdPhonePortrait,
  BiSpeaker: ReactIconsBi.BiSpeaker,
  FaBedPulse: ReactIconsFa6.FaBedPulse,
  FaBox: ReactIconsFa6.FaBox,
  FaSuitcase: ReactIconsFa6.FaSuitcase,
  FaCakeCandles: ReactIconsFa6.FaCakeCandles,
  FaWatchmanMonitoring: ReactIconsFa.FaWatchmanMonitoring,
  FaGears: ReactIconsFa6.FaGears,
  FaMapLocationDot: ReactIconsFa6.FaMapLocationDot,
  FaMountainSun: ReactIconsFa6.FaMountainSun,
  FaObjectGroup: ReactIconsFa.FaObjectGroup,
  FaPeopleGroup: ReactIconsFa6.FaPeopleGroup,
  FaScrewdriverWrench: ReactIconsFa6.FaScrewdriverWrench,
  FaTelegram: ReactIconsFa6.FaTelegram,
  FaStar: ReactIconsFa6.FaStar,
  FaTent: ReactIconsFa6.FaTent,
  FaPercent: ReactIconsFa.FaPercent,
  FaFileInvoice: ReactIconsFa.FaFileInvoice,
  GiDeerHead: GiDeerHead,

  LuPackageOpen: LuPackageOpen,
  GiTakeMyMoney: GiTakeMyMoney,
};
