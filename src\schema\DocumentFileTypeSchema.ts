import { z } from 'zod';

export const DocumentFileTypeCreateSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  extensionCode: z
    .string()
    .min(3, 'Extension code must be exactly 3 characters')
    .max(3, 'Extension code must be exactly 3 characters')
    .regex(/^[A-Z]{3}$/, 'Extension code must be exactly 3 uppercase letters'),
  extension: z.string().min(1, 'Extension is required'),
  isImage: z.boolean(),
});

export const DocumentFileTypeUpdateSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  extensionCode: z
    .string()
    .min(3, 'Extension code must be exactly 3 characters')
    .max(3, 'Extension code must be exactly 3 characters')
    .regex(/^[A-Z]{3}$/, 'Extension code must be exactly 3 uppercase letters'),
  extension: z.string().min(1, 'Extension is required'),
  isAvailable: z.boolean(),
  isImage: z.boolean(),
});

export type DocumentFileTypeCreateData = z.infer<
  typeof DocumentFileTypeCreateSchema
>;
export type DocumentFileTypeUpdateData = z.infer<
  typeof DocumentFileTypeUpdateSchema
>;
