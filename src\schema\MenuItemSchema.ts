import * as z from 'zod';
import { NumberString } from './common';

export const MenuItemSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  keywords: z.string().optional(),
  metaDescription: z.string().optional(),
  url: z.string().optional(),
  displayOrder: NumberString,
  permissionKey: z.string().optional(),
  level: z.string().optional(),
  roleId: z.string().optional(),
  menuIds: z.array(z.string()).optional(),
  iconName: z.string().optional(),
  target: z.literal('_self').or(z.literal('_blank')).optional(),
  isVisible: z.boolean(),
  isParent: z.boolean(),
  parentId: z.string().optional(),
  sectionId: z.string(),
  direction: z.string().optional(),
  isStatic: z.boolean(),
  isDashboard: z.boolean(),
  image: z
    .array(
      z.instanceof(File).refine((file) => file.size < 4 * 1024 * 1024, {
        message: 'File size must be less than 4MB',
      }),
    )
    .optional(),
});
export type MenuItemData = z.infer<typeof MenuItemSchema>;
