import { filterMenuItems, findClosestMenuItem } from '@/models/MenuItem';
import { getQueryClient } from './query-client';
import MenuQuery from '@/services/queries/MenuQuery';
import { useParams, usePathname } from 'next/navigation';
import { useQueries } from '@tanstack/react-query';
import { useMemo } from 'react';
import AuthQuery from '@/services/queries/AuthQuery';

export async function getTabItems({
  defaults,
  dynamicValues,
}: {
  dynamicValues?: { [key: string]: string };
  defaults?: { name: string; url: string; current?: boolean }[];
}) {
  const items = await getQueryClient().fetchQuery({
    queryKey: [...MenuQuery.tags],
    queryFn: MenuQuery.getAll,
  });
  const headersList = await import('next/headers').then((mod) => mod.headers());
  const url = headersList.get('X-next-path') ?? undefined;
  const children = findClosestMenuItem(items, url)
    ?.children?.filter((i) => i.section == 'Tab' && i.isVisible)
    ?.sort((a, b) => a.displayOrder - b.displayOrder);
  const tabs =
    children?.map((i) => {
      const path =
        '/' +
        (i.url?.replace(
          /{([^}]+)}/g,
          (match, key) => dynamicValues?.[key] ?? match,
        ) ?? '');

      return { name: i.name, url: path, current: path === url };
    }) ?? defaults;
  return tabs;
}
export function useTabItems(
  defaults?: { name: string; url: string; current?: boolean }[],
) {
  const [
    { data: authorization, isLoading: isAuthLoading, isError: isAuthError },
    { data: menuItems, isLoading, isError },
  ] = useQueries({
    queries: [
      {
        queryKey: [AuthQuery.tags.me],
        queryFn: () => AuthQuery.me(),
      },
      {
        queryKey: [...MenuQuery.tags],
        queryFn: () => MenuQuery.getAll(),
      },
    ],
  });
  console.log('authorization', authorization);
  console.log('menuItems', menuItems);
  const url = usePathname();
  const params = useParams();
  const items = useMemo(() => {
    if (!menuItems || !authorization) return [];
    return filterMenuItems(menuItems, authorization);
  }, [menuItems, authorization]);
  const children = useMemo(() => {
    return items
      ? findClosestMenuItem(items, url)
          ?.children?.filter((i) => i.section === 'Tabs' && i.isVisible)
          ?.sort((a, b) => a.displayOrder - b.displayOrder)
      : undefined;
  }, [items, url]);
  console.log('children', children);
  const tabs = useMemo(() => {
    return children
      ? children.map((i) => {
          const path =
            '/' +
            (i.url?.replace(
              /{([^}]+)}/g,
              (match, key) => params?.[key]?.toString() ?? match,
            ) ?? '');
          console.log('path', path);
          console.log('url', url);
          return { name: i.name, url: path, current: path === url };
        })
      : defaults;
  }, [children, defaults, url, params]);
  console.log('tabs', tabs);
  return {
    tabs,
    isLoading: isLoading || isAuthLoading,
    isError: isError || isAuthError,
  };
}
