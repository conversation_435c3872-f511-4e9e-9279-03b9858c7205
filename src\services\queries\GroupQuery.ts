import { BriefData } from '@/models/BriefData';
import fetcher from './fetcher';
import { GroupDto } from '@/models/Category';
import { GroupData } from '@/schema/GroupSchema';
import { GroupWithCategoriesDto } from '@/models/Offering';

const GroupQuery = {
  tags: ['Group'] as const,
  getAllByGroupIdHierarchical: async (
    groupId: number,
  ): Promise<GroupWithCategoriesDto> => {
    return fetcher<GroupWithCategoriesDto>(`Group/${groupId}/get`);
  },

  getAll: async () => fetcher<GroupDto[]>('Group'),

  getBrief: async () => fetcher<BriefData[]>('Group/Brief'),

  get: async (id: number) => fetcher<GroupData>(`Group/${id}`),

  add: async (data: GroupData) =>
    fetcher<boolean>('Group', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  update: (id: number) => async (data: GroupData) =>
    fetcher<boolean>(`Group/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),
};

export default GroupQuery;
