'use client';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { type MenuItem } from '@/models/MenuItem';
import Checkbox from '@/components/ui/inputs/checkbox';
import { useMutation } from '@tanstack/react-query';
import { getQueryClient } from '@/utils/query-client';
import { useToast } from '@/components/ui/use-toast';
import RoleQuery from '@/services/queries/RoleQuery';
import AuthQuery from '@/services/queries/AuthQuery';
import { Badge } from '@/components/ui/Badge';

export function MenuAccessControl({
  menuItems = [],
  selectedItems,
  roleId,
  sections,
}: {
  menuItems: MenuItem[];
  selectedItems: number[];
  roleId: number;
  sections: string[];
}) {
  const [checkedItems, setCheckedItems] = useState<Set<number>>(
    new Set(selectedItems),
  );
  const { toast } = useToast();
  const { mutate, isPending } = useMutation({
    mutationFn: RoleQuery.setMenu(roleId),
    onSuccess: async (data) => {
      await getQueryClient().invalidateQueries({
        queryKey: [...RoleQuery.tags, { id: roleId }],
      });
      await getQueryClient().invalidateQueries({
        queryKey: [AuthQuery.tags.me],
      });

      toast({
        variant: 'success',
        title: typeof data == 'string' ? 'Role created' : 'Role updated',
      });
    },
  });
  useEffect(() => {
    setCheckedItems(new Set(selectedItems));
  }, [selectedItems]);

  useEffect(() => {
    const updatedCheckedItems = new Set(checkedItems);
    let hasChanged = false;

    const checkParentSelection = (item: MenuItem) => {
      if (item.isParent && item.children && item.children.length > 0) {
        // Check if any child is selected, not just all children
        const anyChildSelected = item.children.some((child) =>
          checkedItems.has(child.id),
        );
        const parentIsSelected = checkedItems.has(item.id);

        if (anyChildSelected && !parentIsSelected) {
          // If any child is selected, parent should be selected
          updatedCheckedItems.add(item.id);
          hasChanged = true;
        }
      }
    };

    // Process all items and their children
    const processItems = (items: MenuItem[]) => {
      items.forEach((item) => {
        checkParentSelection(item);
        if (item.children) {
          processItems(item.children);
        }
      });
    };

    processItems(menuItems);

    if (hasChanged) {
      setCheckedItems(updatedCheckedItems);
    }
  }, [checkedItems, menuItems]);

  const handlePermissionChange = (itemId: number, isChecked: boolean) => {
    setCheckedItems((prev) => {
      const newSet = new Set(prev);
      if (isChecked) {
        newSet.add(itemId);
      } else {
        newSet.delete(itemId);
      }
      return newSet;
    });
  };

  const handleParentChange = (parent: MenuItem, isChecked: boolean) => {
    const allChildrenIds = parent.children?.map((child) => child.id) || [];
    setCheckedItems((prev) => {
      const newSet = new Set(prev);
      if (isChecked) {
        newSet.add(parent.id);
        allChildrenIds.forEach((id) => newSet.add(id));
      } else {
        newSet.delete(parent.id);
        allChildrenIds.forEach((id) => newSet.delete(id));
      }
      return newSet;
    });
  };

  const isItemChecked = (item: MenuItem): boolean => checkedItems.has(item.id);

  const renderMenuItem = (item: MenuItem) => {
    const isChecked = isItemChecked(item);

    return (
      <div key={item.id} className="py-2 border-b last:border-none">
        <div className="flex items-center space-x-2 group hover:bg-gray-50 rounded-md p-2">
          <Checkbox
            id={item.id.toString()}
            checked={isChecked}
            onCheckedChange={(checked) => {
              if (item.isParent) {
                handleParentChange(item, checked as boolean);
              } else {
                handlePermissionChange(item.id, checked as boolean);
              }
            }}
          />
          <Label
            htmlFor={item.id.toString()}
            className="text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {item.name}
            <Badge className="ml-2">{item.section}</Badge>
          </Label>
        </div>
        {item.children && item.children.length > 0 && (
          <div className="ml-6 mt-2 border-l-2 pl-4 border-gray-300">
            {item.children
              .sort((a, b) => a.displayOrder - b.displayOrder)
              .map((child) => renderMenuItem(child))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-end items-center">
          <div className="w-full space-y-2">
            <CardTitle>Menu Access Control</CardTitle>
            <CardDescription>
              Select which menu items this role should have access to.
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={() => mutate(Array.from(checkedItems))}
              disabled={isPending}
              className="w-fit self-end"
              variant={'main'}
              iconName="SaveIcon"
              iconProps={{ className: 'text-success-foreground', size: 20 }}
            >
              {isPending ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="pr-4">
          {menuItems.length > 0 ? (
            menuItems
              .sort((a, b) => a.displayOrder - b.displayOrder)
              .map((item) => renderMenuItem(item))
          ) : (
            <p className="text-sm text-gray-600">No items in this section</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
