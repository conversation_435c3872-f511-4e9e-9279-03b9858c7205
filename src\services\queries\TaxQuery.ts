import { Tax } from '@/models/Tax';
import fetcher from './fetcher';
import { BriefData } from '@/models/BriefData';
import { TaxRequestData } from '@/schema/Tax';

const TaxQuery = {
  tags: ['Tax'] as const,

  getTaxType: async () => fetcher<BriefData[]>('Tax/gettaxtype'),

  getAll: async () => fetcher<Tax[]>('Tax/getalltax'),

  getTax: async (id: number) => fetcher<TaxRequestData>(`Tax/gettax/${id}`),

  create: async (data: TaxRequestData) =>
    fetcher<boolean>('Tax/CreateTaxProvince', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),

  update: (id: number) => async (data: TaxRequestData) =>
    fetcher<boolean>(`Tax/updatetaxprovince/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),

  updateIsActive: (id: number, isActive: boolean) =>
    fetcher<boolean>(`Tax/UpdateIsActive/${id}?isActive=${isActive}`, {
      method: 'PATCH',
    }),
};

export default TaxQuery;
