'use client';

interface BoothOptionsTabProps {
  showId?: number;
  onSuccess?: () => void;
}

export default function BoothOptionsTab({
  showId,
  onSuccess,
}: BoothOptionsTabProps) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-base font-semibold text-[#00646C] border-b border-slate-200 pb-2 mb-4">
          Booth Options
        </h2>

        <div className="min-h-[400px] flex items-center justify-center">
          <div className="text-center">
            <h3 className="text-xl font-semibold text-slate-800 mb-2">
              Booth Options
            </h3>
            <p className="text-slate-500">
              This section is under development. Future API endpoints will be
              integrated here.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
