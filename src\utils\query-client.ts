import { QueryClient, QueryClientConfig } from '@tanstack/react-query';
import { cache } from 'react';

import { AppError } from '@/services/queries/error';

function makeQueryClient(config?: QueryClientConfig | undefined) {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // With SSR, we usually want to set some default staleTime
        // above 0 to avoid refetching immediately on the client
        staleTime: 60 * 1000,
        retry(failureCount, error) {
          // Check if the error is an AppError and if its status is 401
          if (error instanceof AppError && error.status === 401) {
            // Do not retry on 401 errors
            return false;
          }
          // Retry up to 2 times for other errors (total 3 attempts)
          return failureCount < 2; // Corrected retry logic: retry if count is 0 or 1
        },
      },
    },
    ...config,
  });
}

let browserQueryClient: QueryClient | undefined = undefined;

export function getQueryClient(cached = false) {
  if (typeof window === 'undefined') {
    // Server: always make a new query client
    return cached ? cache(makeQueryClient)() : makeQueryClient();
  } else {
    if (!browserQueryClient) browserQueryClient = makeQueryClient();
    return browserQueryClient;
  }
}
