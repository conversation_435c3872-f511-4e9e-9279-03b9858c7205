import { dehydrate, HydrationBoundary } from '@tanstack/react-query';

import { getQueryClient } from '@/utils/query-client';

import ProfileForm from './component/profile-form';
import UsersQuery from '@/services/queries/UsersQuery';

export default async function page() {
  await getQueryClient().prefetchQuery({
    queryKey: ['profile'],
    queryFn: UsersQuery.getCurrent,
  });
  return (
    <div>
      <HydrationBoundary state={dehydrate(getQueryClient())}>
        <ProfileForm />
      </HydrationBoundary>
    </div>
  );
}
