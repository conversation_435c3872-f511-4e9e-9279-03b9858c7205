'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { useMemo } from 'react';
import ShowQuery from '@/services/queries/ShowQuery';
import { ShowSchedule } from '@/models/Show';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import { useToast } from '@/components/ui/use-toast';

import ScheduleModal from './ScheduleModal';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import { ErrorNotificationIcon, SuccessNotificationIcon } from '@/assets/Icons';
import MutationConfirmModal from '../modals/mutation_confirm_modal/mutation-confirm-modal';

interface ShowScheduleTabProps {
  showId?: number;
  onSuccess?: () => void;
}

export default function ShowScheduleTab({
  showId,
  onSuccess,
}: ShowScheduleTabProps) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { data, isLoading } = useQuery({
    queryKey: ['show-schedules', showId!],
    queryFn: () => ShowQuery.getSchedules(showId!),
    enabled: !!showId,
  });

  // const toggleConfirmed = useMutation({
  //   mutationFn: (id: number) => ShowQuery.toggleScheduleConfirmed(showId!, id),
  //   onSuccess: () =>
  //     queryClient.invalidateQueries({ queryKey: ['show-schedules', showId] }),
  // });
  // const toggleApplyToService = useMutation({
  //   mutationFn: (id: number) =>
  //     ShowQuery.toggleScheduleApplyToService(showId!, id),
  //   onSuccess: () =>
  //     queryClient.invalidateQueries({ queryKey: ['show-schedules', showId] }),
  // });
  const columns = useMemo(
    () =>
      generateTableColumns<ShowSchedule>(
        {
          showScheduleDate: { name: 'Date', type: 'text', sortable: true },
          timeStart: { name: 'Start Time', type: 'text' },
          timeEnd: { name: 'End Time', type: 'text' },
          showScheduleConfirmed: {
            name: 'Confirmed',
            type: {
              type: 'node',
              render: ({ row }) => (
                <div className="flex items-center whitespace-nowrap">
                  {row.showScheduleConfirmed ? (
                    <>
                      <SuccessNotificationIcon className="text-green-600 w-4 h-4 mr-1" />
                      <span className="text-green-600 hidden">Yes</span>
                    </>
                  ) : (
                    <>
                      <ErrorNotificationIcon className="text-red-600 w-4 h-4 mr-1" />
                      <span className="text-red-600 hidden">No</span>
                    </>
                  )}
                </div>
              ),
            },
          },
          applyScheduleToServiceForm: {
            name: 'Apply to Service',
            type: {
              type: 'node',
              render: ({ row }) => (
                <div className="flex items-center whitespace-nowrap">
                  {row.applyScheduleToServiceForm ? (
                    <>
                      <SuccessNotificationIcon className="text-green-600 w-4 h-4 mr-1" />
                      <span className="text-green-600 hidden">Yes</span>
                    </>
                  ) : (
                    <>
                      <ErrorNotificationIcon className="text-red-600 w-4 h-4 mr-1" />
                      <span className="text-red-600 hidden">No</span>
                    </>
                  )}
                </div>
              ),
            },
          },
          showScheduleComments: { name: 'Comments', type: 'text' },
        },
        {
          action: {
            name: 'Actions',
            type: {
              type: 'node',
              render: ({ row }) => (
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="secondary"
                    iconName="EditIcon"
                    onClick={() =>
                      modal(
                        <ScheduleModal showId={showId!} scheduleId={row.id} />,
                        {
                          ...DEFAULT_MODAL,
                          width: '40%',
                        },
                      ).open()
                    }
                  ></Button>
                  <Button
                    variant="remove"
                    size="sm"
                    iconName="RemoveIcon"
                    onClick={() => {
                      modal(
                        ({ close }) => (
                          <MutationConfirmModal
                            close={close}
                            title="Delete Schedule"
                            description={`Are you sure you want to delete this schedule?`}
                            mutateFn={() =>
                              ShowQuery.deleteSchedule(showId!, row.id!)
                            }
                            mutationKey={['show-schedules', showId]}
                            onSuccess={() => {
                              queryClient.invalidateQueries({
                                queryKey: ['show-schedules', showId],
                              });
                              toast({
                                title: 'Schedule deleted',
                                variant: 'success',
                              });
                            }}
                            onError={(e: any) =>
                              toast({
                                title: e.message || 'Failed to delete',
                                variant: 'destructive',
                              })
                            }
                            variant="destructive"
                            confirmButtonText="Delete"
                            confirmIconName="DeleteIcon"
                            loadingIconName="LoadingIcon"
                          />
                        ),
                        DEFAULT_MODAL,
                      ).open();
                    }}
                  ></Button>
                </div>
              ),
            },
          },
        },
        false,
      ),
    [showId],
  );
  const filters = useMemo(
    () =>
      generateTableFilters<ShowSchedule>({
        showScheduleDate: { name: 'Date', type: 'text' },
        showScheduleConfirmed: {
          name: 'Confirmed',
          type: {
            type: 'select',
            options: [
              { label: 'Yes', value: 'true' },
              { label: 'No', value: 'false' },
            ],
          },
        },
        applyScheduleToServiceForm: {
          name: 'Apply to Service',
          type: {
            type: 'select',
            options: [
              { label: 'Yes', value: 'true' },
              { label: 'No', value: 'false' },
            ],
          },
        },
      }),
    [],
  );
  return (
    <>
      <DataTable
        columns={columns}
        data={data}
        isLoading={isLoading}
        filterFields={filters}
        controls={
          <Button
            variant="main"
            onClick={() => {
              modal(<ScheduleModal showId={showId!} />, {
                ...DEFAULT_MODAL,
                width: '40%',
              }).open();
            }}
            iconName="AddIcon"
          >
            Add Schedule
          </Button>
        }
      />
    </>
  );
}
