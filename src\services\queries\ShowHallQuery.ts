import { ShowHallData } from '@/schema/ShowHallSchema';
import fetcher from './fetcher';
import { Hall } from '@/models/ShowLocation';
import { BriefData } from '@/models/BriefData';
import { urlToFile } from '@/utils/file-helper';

const ShowHallQuery = {
  tags: ['ShowHall'] as const,

  // GET: /ShowHall/location/{locationId}
  getByLocation: async (locationId: number) => {
    return fetcher<Hall[]>(`ShowHall/${locationId}`);
  },

  // GET: /ShowHall/location/{locationId}
  getBriefByLocation: async (locationId: number) => {
    return fetcher<BriefData[]>(`ShowHall/GetBrief/${locationId}`);
  },

  get: async (hallId: number): Promise<ShowHallData> => {
    const data = await fetcher<ShowHallData>(`ShowHall/hall/${hallId}`);
    const filePath = '/doc' + String(data.floorPlanPath) + '?protected=true';
    const floorPlan = await urlToFile(filePath);
    return {
      locationId: data.locationId,
      hallName: data.hallName,
      hallCode: data.hallCode,
      hallStyle: data.hallStyle,
      hallFloorType: data.hallFloorType,
      banquetCapacity: data.banquetCapacity,
      hallWidth: data.hallWidth,
      hallLength: data.hallLength,
      overheadHeight: data.overheadHeight,
      hallArea: data.hallArea,
      isElecOnFloor: data.isElecOnFloor,
      isElecOnCeiling: data.isElecOnCeiling,
      hallSurface: data.hallSurface,
      hallCeilingHeight: data.hallCeilingHeight,
      accessDoor: data.accessDoor,
      loadingDocks: data.loadingDocks,
      hallBoothCount: data.hallBoothCount,
      floorPlanPath: data.floorPlanPath,
      floorPlan: data.floorPlanPath ? [floorPlan] : [],
      isArchived: data.isArchived,
    } as ShowHallData;
  },

  // GET: /ShowHall/{hallId}
  // getById: async (hallId: number) => {
  //   return fetcher<ShowHallData>(`ShowHall/hall/${hallId}`);
  // },

  // POST: /ShowHall
  // create: async (data: ShowHallData) => {
  //   fetcher<boolean>('ShowHall', {
  //     method: 'POST',
  //     headers: { 'Content-Type': 'application/json' },
  //     body: JSON.stringify(data),
  //   });
  // },

  createHall: async (data: ShowHallData) => {
    const formData = new FormData();
    if (data.locationId)
      formData.append('locationId', data.locationId.toString());
    if (data.hallName && data.hallName != '')
      formData.append('hallName', data.hallName);
    if (data.hallCode && data.hallCode != '')
      formData.append('hallCode', data.hallCode);
    if (data.hallStyle && data.hallStyle != '')
      formData.append('hallStyle', data.hallStyle);
    if (data.hallFloorType && data.hallFloorType != '')
      formData.append('hallFloorType', data.hallFloorType);
    if (data.banquetCapacity)
      formData.append('banquetCapacity', data.banquetCapacity.toString());
    if (data.hallWidth) formData.append('hallWidth', data.hallWidth.toString());
    if (data.hallLength)
      formData.append('hallLength', data.hallLength.toString());
    if (data.overheadHeight)
      formData.append('overheadHeight', data.overheadHeight.toString());
    if (data.hallArea) formData.append('hallArea', data.hallArea.toString());
    if (data.isElecOnFloor)
      formData.append('isElecOnFloor', data.isElecOnFloor.toString());
    if (data.isElecOnCeiling)
      formData.append('isElecOnCeiling', data.isElecOnCeiling.toString());
    if (data.hallSurface)
      formData.append('hallSurface', data.hallSurface.toString());
    if (data.hallCeilingHeight)
      formData.append('hallCeilingHeight', data.hallCeilingHeight.toString());
    if (data.accessDoor)
      formData.append('accessDoor', data.accessDoor.toString());
    if (data.loadingDocks)
      formData.append('loadingDocks', data.loadingDocks.toString());
    if (data.hallBoothCount)
      formData.append('hallBoothCount', data.hallBoothCount.toString());
    if (data.floorPlan) formData.append('floorPlan', data.floorPlan[0]);

    return fetcher<boolean>(
      `ShowHall`,
      {
        method: 'POST',
        body: formData,
      },
      true,
    );
  },

  editHall: (hallId: number) => async (data: ShowHallData) => {
    const formData = new FormData();
    if (data.locationId)
      formData.append('locationId', data.locationId.toString());
    if (data.hallName && data.hallName != '')
      formData.append('hallName', data.hallName);
    if (data.hallCode && data.hallCode != '')
      formData.append('hallCode', data.hallCode);
    if (data.hallStyle && data.hallStyle != '')
      formData.append('hallStyle', data.hallStyle);
    if (data.hallFloorType && data.hallFloorType != '')
      formData.append('hallFloorType', data.hallFloorType);
    if (data.banquetCapacity)
      formData.append('banquetCapacity', data.banquetCapacity.toString());
    if (data.hallWidth) formData.append('hallWidth', data.hallWidth.toString());
    if (data.hallLength)
      formData.append('hallLength', data.hallLength.toString());
    if (data.overheadHeight)
      formData.append('overheadHeight', data.overheadHeight.toString());
    if (data.hallArea) formData.append('hallArea', data.hallArea.toString());
    if (data.isElecOnFloor)
      formData.append('isElecOnFloor', data.isElecOnFloor.toString());
    if (data.isElecOnCeiling)
      formData.append('isElecOnCeiling', data.isElecOnCeiling.toString());
    if (data.hallSurface)
      formData.append('hallSurface', data.hallSurface.toString());
    if (data.hallCeilingHeight)
      formData.append('hallCeilingHeight', data.hallCeilingHeight.toString());
    if (data.accessDoor)
      formData.append('accessDoor', data.accessDoor.toString());
    if (data.loadingDocks)
      formData.append('loadingDocks', data.loadingDocks.toString());
    if (data.hallBoothCount)
      formData.append('hallBoothCount', data.hallBoothCount.toString());
    if (data.floorPlan) formData.append('floorPlan', data.floorPlan[0]);
    if (data.floorPlanPath)
      formData.append('floorPlanPath', data.floorPlanPath);

    return fetcher<boolean>(
      `ShowHall/${hallId}`,
      {
        method: 'PATCH',
        body: formData,
      },
      true,
    );
  },

  // PATCH: /ShowHall/{hallId}
  // update: (hallId: number) => async (data: ShowHallData) => {
  //   fetcher<boolean>(`ShowHall/${hallId}`, {
  //     method: 'PATCH',
  //     headers: { 'Content-Type': 'application/json' },
  //     body: JSON.stringify(data),
  //   });
  // },
};

export default ShowHallQuery;
