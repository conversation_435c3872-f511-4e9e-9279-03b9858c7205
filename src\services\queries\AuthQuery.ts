import { Tokens } from '@/models/Auth';
import { UserData } from '@/models/User';
import { LoginData } from '@/schema/Login';
import { VerificationData } from '@/schema/Verification';

import fetcher from './fetcher';
enum Tags {
  auth = 'auth',
  me = 'me',
  verify = 'verify',
  password = 'password',
}
const AuthQuery = {
  tags: Tags,
  Login: async (data: LoginData) =>
    fetcher<Tokens>(`auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ...data }),
    }),
  me: async () => fetcher<UserData>(`auth/me`),
  getUserToVerify: async (key: string) =>
    fetcher<UserData>(`auth/verify/${key}`),
  verify: (key: string) => async (data: VerificationData) =>
    fetcher<Tokens>(`auth/verify/${key}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ password: data.password }),
    }),
  requestPasswordReset: (data: { email: string }) =>
    fetcher<Tokens>(`auth/requestPasswordReset`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),
  resetPassword: (email: string) => async (data: VerificationData) =>
    fetcher<Tokens>(`auth/resetPassword`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ password: data.password, email }),
    }),
  changePassword: (data: { password: string; oldPassword: string }) =>
    fetcher<Tokens>(`auth/changePassword`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ...data }),
    }),
};
export default AuthQuery;
