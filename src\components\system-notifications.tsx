import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Bell, Check, Info, AlertTriangle, X, ChevronDown } from 'lucide-react';

export default function SystemNotifications() {
  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-slate-800">
          System Notifications (2)
        </h1>
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" className="text-slate-600">
            Mark all as read
          </Button>
          <Button variant="ghost" size="sm" className="text-slate-600">
            <ChevronDown className="h-4 w-4 mr-1" />
            Collapse
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        {/* System Maintenance */}
        <Card className="border border-slate-200 shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Bell className="h-5 w-5 mr-2 text-[#00646C]" />
              <span className="text-[#00646C]">System Maintenance</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-slate-600 mb-2">
              Scheduled maintenance will occur on June 15, 2025 from 2:00 AM to
              4:00 AM EDT.
            </p>
            <p className="text-sm text-slate-500">Time: 9:30 AM</p>
          </CardContent>
        </Card>

        {/* Order Deadline */}
        <Card className="border border-slate-200 shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-[#77400F]" />
              <span className="text-[#77400F]">Order Deadline Approaching</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-slate-600 mb-2">
              CIM CONNECT 2025 order deadline is in 5 days. Remind exhibitors to
              complete their orders.
            </p>
            <p className="text-sm text-slate-500">Time: 2:15 PM</p>
          </CardContent>
        </Card>

        {/* New Show Added */}
        <Card className="border border-slate-200 shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Check className="h-5 w-5 mr-2 text-[#CED600]" />
              <span className="text-[#CED600]">New Show Added</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-slate-600 mb-2">
              Canadian Mining Expo 2025 has been successfully added to the
              system.
            </p>
            <p className="text-sm text-slate-500">Time: 11:45 AM</p>
          </CardContent>
        </Card>

        {/* New User Registration */}
        <Card className="border border-slate-200 shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Info className="h-5 w-5 mr-2 text-[#00646C]" />
              <span className="text-[#00646C]">New User Registration</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-slate-600 mb-2">
              5 new exhibitors have registered for upcoming shows in the last 24
              hours.
            </p>
            <p className="text-sm text-slate-500">Time: 4:20 PM</p>
          </CardContent>
        </Card>

        {/* Payment Processing Issue */}
        <Card className="border border-slate-200 shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <X className="h-5 w-5 mr-2 text-[#B10055]" />
              <span className="text-[#B10055]">Payment Processing Issue</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-slate-600 mb-2">
              There was an issue with payment gateway integration. Technical
              team has been notified.
            </p>
            <p className="text-sm text-slate-500">Time: 10:05 AM</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
