'use client';
import './style/index.scss';
import { Button } from '@/components/ui/button';
import {
  CheckCircle,
  Edit2,
  XCircle,
  ImageIcon,
  PlusCircle,
} from 'lucide-react';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import AddingPropertyModal from '../adding_property_modal';
import { useQuery } from '@tanstack/react-query';
import OfferingQuery from '@/services/queries/OfferingQuery';
import Suspense from '@/components/ui/Suspense';
import UpdateOfferingPropertyModal from '../update_offering_property_modal';

interface IPropertyMatrixTable {
  groupId: number;
  categoryId: number;
  id: number;
}

function PropertyMatrixTable({
  groupId,
  categoryId,
  id,
}: IPropertyMatrixTable) {
  const {
    data: property,
    isLoading,
    isPending,
  } = useQuery({
    queryKey: ['Offering Property Details', { id }],
    queryFn: () => OfferingQuery.getOfferingPropertyDetail(Number(id)),
    enabled: !!id,
  });

  return (
    <div className="property-matrix-table">
      <h2 className="text-xl font-semibold text-[#00646C] border-b border-slate-200 pb-3 mb-4">
        Select Property
      </h2>
      <Suspense isLoading={isLoading || isPending}>
        <div>
          {!property || property.length === 0 ? (
            <p className="text-center">
              No property combinations available. Click on this button{' '}
              <Button
                variant="secondary"
                size="icon"
                onClick={() => {
                  modal(
                    <AddingPropertyModal
                      id={Number(id!)}
                      groupId={Number(groupId)}
                      categoryId={Number(categoryId)}
                    />,
                    { ...DEFAULT_MODAL, width: '40%' },
                  ).open();
                }}
              >
                <PlusCircle className="size-4" />
              </Button>{' '}
              to add property
            </p>
          ) : (
            <div className="overflow-x-auto p-1 flex flex-col gap-2">
              <div className="flex justify-end mb-2">
                <Button
                  variant="main"
                  onClick={() => {
                    modal(
                      <AddingPropertyModal
                        id={Number(id!)}
                        groupId={Number(groupId)}
                        categoryId={Number(categoryId)}
                        isUpdate={true}
                      />,
                      { ...DEFAULT_MODAL, width: '40%' },
                    ).open();
                  }}
                >
                  <Edit2 className="size-4 mr-2" />
                  Update Property
                </Button>
              </div>

              <table className="min-w-full table-auto border border-gray-300 rounded-md overflow-hidden shadow text-sm">
                <thead className="bg-[#CDDB00] text-[#784311] uppercase text-xs tracking-wider">
                  <tr>
                    <th className="border px-4 py-3 text-center">Name</th>
                    <th className="border px-4 py-3 text-center">Code</th>
                    <th className="border px-4 py-3 text-center">Image</th>
                    <th className="border px-4 py-3 text-center">Status</th>
                    <th className="border px-4 py-3 text-center">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {property.map((item) => (
                    <tr
                      key={item.id}
                      className={`transition-all duration-150 hover:bg-[#E5F9F8]`}
                    >
                      <td className="px-4 py-1 border text-[#00646C] font-semibold text-center">
                        {item.propertyOption1}
                        {item.propertyOption2
                          ? `, ${item.propertyOption2}`
                          : ''}
                      </td>
                      <td className="px-4 py-1 border font-mono text-gray-600 text-center">
                        {item.code ?? '-'}
                      </td>
                      <td className="px-4 py-1 border text-center">
                        {item.image ? (
                          <div className="relative inline-block group">
                            <div className="absolute bottom left-full ml-2 transform -translate-y-1/2 hidden group-hover:block z-10 bg-white border border-gray-300 rounded shadow-md">
                              <img
                                src={
                                  item.image.startsWith('/images')
                                    ? item.image
                                    : '/images' + item.image
                                }
                                alt="preview"
                                className="max-w-[100px] max-h-[100px] object-cover rounded"
                              />
                            </div>
                            <ImageIcon className="w-4 h-4" />
                          </div>
                        ) : (
                          '—'
                        )}
                      </td>

                      <td className="px-4 py-1 border text-center">
                        <div className="flex justify-center items-center h-full">
                          {item.isActive ? (
                            <CheckCircle className="text-green-600 w-4 h-4" />
                          ) : (
                            <XCircle className="text-red-600 w-4 h-4" />
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-1 border text-center">
                        <Button
                          size="sm"
                          variant="secondary"
                          iconName="EditIcon"
                          onClick={() => {
                            modal(
                              <UpdateOfferingPropertyModal
                                id={item.id}
                                offeringId={Number(id)}
                              />,
                              { ...DEFAULT_MODAL, width: '40%' },
                            ).open();
                          }}
                        ></Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </Suspense>
    </div>
  );
}

export default PropertyMatrixTable;
