import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatHeaderString(inputString: string) {
  return inputString.replace(/([A-Z])/g, ' $1').trim();
}

export function formatTextPairString(str: string) {
  return str
    .replace(/([a-z])([A-Z])/g, '$1 $2')
    .replace(/^./, str[0].toUpperCase());
}
