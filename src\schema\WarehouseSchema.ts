import * as z from 'zod';

export const WarehouseSchema = z.object({
  code: z.string().optional(),
  warehouseName: z.string().min(1, { message: 'Warehouse name is required' }),
  addressLine1: z.string().nullable().optional(),
  addressLine2: z.string().nullable().optional(),
  phone: z.string().nullable().optional(),
  city: z.string().nullable().optional(),
  postalCode: z.string().nullable().optional(),
  provinceId: z.string().min(1, { message: 'Province is required' }),
  countryId: z.string().min(1, { message: 'Province is required' }),
  warehouseTypeId: z.string().nullable().optional(),
  contactPersonId: z.string().optional(),
  isActive: z.boolean().default(false),
});

export type WarehouseData = z.infer<typeof WarehouseSchema>;
