import React from 'react';
import PhoneInput, { PhoneInputProps } from 'react-phone-input-2';

import 'react-phone-input-2/lib/style.css';
import './style/index.scss';
import { useFormField } from '../../form';

interface IPhoneNumberInput {}

const PhoneNumberInput = React.forwardRef<
  HTMLInputElement,
  IPhoneNumberInput & PhoneInputProps
>((props, ref) => {
  const { error } = useFormField();
  return (
    <PhoneInput
      containerClass="containerClass "
      inputClass="inputClass  !bg-transparent  !text-foreground  "
      buttonClass="buttonClass"
      containerStyle={{
        borderColor: error ? 'hsl(0, 84.2%, 60.2%)' : '#e5e7eb',
        borderWidth: '1.5px',
      }}
      country={'ca'}
      countryCodeEditable={false}
      {...props}
    />
  );
});

PhoneNumberInput.displayName = 'PhoneNumberInput'; // Assigning the display name

export default PhoneNumberInput;
