.image-changer {
  position: relative;
  cursor: pointer;
  width: fit-content;

  > .change-image-span {
    visibility: hidden;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    font-weight: 700;
    font-size: 12px;
  }
  > .circle-image-wrapper {
    opacity: 1;
    transition: opacity 0.2s;
  }

  &:hover {
    > .change-image-span {
      visibility: visible;
    }

    > .circle-image-wrapper {
      opacity: 0.5;
    }
  }
}
