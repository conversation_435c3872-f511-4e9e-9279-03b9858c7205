import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { ReactNode } from 'react';

import AuthQuery from '@/services/queries/AuthQuery';
import { getQueryClient } from '@/utils/query-client';
import AppHeader from '@/components/app_header/app-header';

export default async function Layout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery({
    queryKey: [AuthQuery.tags.me],
    queryFn: AuthQuery.me,
  });

  return (
    <div className="flex flex-col    bg-slate-50 h-full">
      <HydrationBoundary state={dehydrate(queryClient)}>
        <AppHeader />
        <div className="flex flex-col w-full container pb-8 pt-4 px-4  mx-auto overflow-y-auto flex-grow scrollbar-hide">
          {children}
        </div>
      </HydrationBoundary>
    </div>
  );
}
