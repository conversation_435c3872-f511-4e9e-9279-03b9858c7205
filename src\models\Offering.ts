export interface OfferingDto {
  id: number;
  name?: string | null;
  code?: string | null;
  supplierItemNumber?: string | null;
  publicDescription?: string | null;
  isActive?: boolean | null;
  isObsolete?: boolean | null;
  options?: PropertyOptionsDto[];
}

export interface OfferingDetailDto {
  id: number;
  name?: string | null;
  categoryId?: number | null;
  groupTypeId?: number | null;
  imagePath?: string | null;
  code?: string | null;
  supplierItemNumber?: string | null;
  publicDescription?: string | null;
  internalDescription?: string | null;
  displayOrder?: number | null;
  unitChargedId?: number | null;
  isUnitTypeEach?: boolean | null;
  isAddOn?: boolean | null;
  isForSmOnly?: boolean | null;
  isInternalOnly?: boolean | null;
  // Image as file upload is typically handled differently in front-end;
  // you might use File or Blob for the type here if needed.
  image: File[] | null;
  isActive?: boolean | null;
  isObsolete?: boolean | null;
  createdAt: string; // Date serialized as ISO string
  updatedAt: string; // Date serialized as ISO string
  createdById?: number | null;
  updatedById?: number | null;
  categoryName?: string | null;
  groupTypeName?: string | null;
  groupName?: string | null;
  taxTypeIds: string[];
}

export interface GroupTypeWithGroupDto {
  groupTypeId: number;
  groupTypeName: string;
  isAvailable?: boolean;
  group: GroupWithCategoriesDto[];
}

export interface GroupWithCategoriesDto {
  groupId: number;
  groupName: string;
  isAvailable?: boolean;
  categories: CategoryWithOfferingsDto[];
}

export interface CategoryWithOfferingsDto {
  categoryId: number;
  categoryName: string;
  offerings: OfferingDto[];
  isAvailable?: boolean;
}

export interface OfferingPropertyDto {
  id: number;
  code?: string;
  property1?: string;
  property2?: string;
  propertyOption1?: string;
  propertyOption2?: string;
  image?: string;
  isActive?: boolean;
}

export interface PropertyOptionsDto {
  id: number;
  code?: string;
  name?: string;
  isActive?: boolean;
  image?: string;
}

export interface OfferingPropertyCreateDto {
  name?: string;
  id: number;
  propertyOption1?: string;
  propertyOption2?: string;
  code?: string;
  image: File[] | null;
  imagePath?: string;
  supplierItemNumber?: string;
  isForSmOnly?: boolean;
  isInternalOnly?: boolean;
  isActive?: boolean;
}

export interface AddonSelectionDto {
  selectedOfferings: number[];
  selectedOptions: number[];
}

// models/Offering.ts

// export interface OfferingPropertyDto {
//   property1?: [];
//   property2?: [];
// }
