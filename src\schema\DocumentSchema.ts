import { z } from 'zod';
import { langSchema } from './common';
// Document Schema Validation

// Document Group Schema
export const DocumentGroupSchema = z.object({
  code: z
    .string()
    .length(2, { message: 'Code must be exactly 2 characters' })
    .regex(/^[A-Z]+$/, { message: 'Code must contain only uppercase letters' }),
  names: langSchema(z.string().min(1, { message: 'Name is required' })),
});

export type DocumentGroupData = z.infer<typeof DocumentGroupSchema>;

// Document Category Schema
export const DocumentCategorySchema = z.object({
  code: z
    .string()
    .length(2, { message: 'Code must be exactly 2 characters' })
    .regex(/^[A-Z]+$/, { message: 'Code must contain only uppercase letters' }),
  names: langSchema(z.string().min(1, { message: 'Name is required' })),
  groupId: z
    .string()
    .min(1, { message: 'Group is required' })
    .transform((val) => Number(val)),
  isExternal: z.boolean().default(false),
  displayOrder: z
    .number()
    .min(1, 'Display order must be at least 1')
    .optional(),
});

export type DocumentCategoryData = {
  code: string;
  names: { [key: string]: string };
  groupId: string | number;
  isExternal: boolean;
  displayOrder?: number;
};

// Document File Type Schema
export const DocumentFileTypeSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  extensionCode: z
    .string()
    .length(2, { message: 'Extension code must be exactly 2 characters' })
    .regex(/^[A-Z]+$/, {
      message: 'Extension code must contain only uppercase letters',
    }),
  extension: z
    .string()
    .min(1, { message: 'Extension is required' })
    .regex(/^[a-z0-9]+$/, {
      message: 'Extension should not include the dot and should be lowercase',
    }),
  isImage: z.boolean().default(false),
});

export type DocumentFileTypeData = z.infer<typeof DocumentFileTypeSchema>;

// Document Upload Schema
export const DocumentUploadSchema = z.object({
  file: z.any(),
  categoryId: z.string().min(1, { message: 'Category is required' }),
  langCode: z.string().min(1, { message: 'Language is required' }),
  descriptions: langSchema(z.string()),
  altTexts: langSchema(z.string()),
  metaTags: langSchema(z.string()),
  expiryDate: z.string().optional(),
});

export type DocumentUploadData = z.infer<typeof DocumentUploadSchema>;

// Document Update Schema
export const DocumentUpdateSchema = z.object({
  file: z.any().optional(),
  categoryId: z.string().min(1, { message: 'Category is required' }),
  langCode: z.string().min(1, { message: 'Language is required' }),
  descriptions: langSchema(z.string()),
  altTexts: langSchema(z.string()),
  metaTags: langSchema(z.string()),
  expiryDate: z.string().optional(),
  status: z.enum(['Active', 'Inactive', 'Archived']),
});

export type DocumentUpdateData = z.infer<typeof DocumentUpdateSchema>;

// Helper function to generate schema with required languages
export const generateDocumentGroupSchema = (requiredLanguages?: string[]) => {
  if (!requiredLanguages) return DocumentGroupSchema;

  return DocumentGroupSchema.refine(
    (data) => {
      return requiredLanguages.every((lang) => {
        return data.names[lang] && data.names[lang].trim() !== '';
      });
    },
    {
      message: `Names are required for all languages: ${requiredLanguages.join(', ')}`,
      path: ['names'],
    },
  );
};

export const generateDocumentCategorySchema = (
  requiredLanguages?: string[],
) => {
  if (!requiredLanguages) return DocumentCategorySchema;

  return DocumentCategorySchema.refine(
    (data) => {
      return requiredLanguages.every((lang) => {
        return data.names[lang] && data.names[lang].trim() !== '';
      });
    },
    {
      message: `Names are required for all languages: ${requiredLanguages.join(', ')}`,
      path: ['names'],
    },
  );
};

export const generateDocumentUploadSchema = (requiredLanguages?: string[]) => {
  if (!requiredLanguages) return DocumentUploadSchema;

  return DocumentUploadSchema.refine(
    (data) => {
      return requiredLanguages.every((lang) => {
        return data.descriptions[lang] && data.descriptions[lang].trim() !== '';
      });
    },
    {
      message: `Descriptions are required for all languages: ${requiredLanguages.join(', ')}`,
      path: ['descriptions'],
    },
  );
};

export const generateDocumentUpdateSchema = (requiredLanguages?: string[]) => {
  if (!requiredLanguages) return DocumentUpdateSchema;

  return DocumentUpdateSchema.refine(
    (data) => {
      return requiredLanguages.every((lang) => {
        return data.descriptions[lang] && data.descriptions[lang].trim() !== '';
      });
    },
    {
      message: `Descriptions are required for all languages: ${requiredLanguages.join(', ')}`,
      path: ['descriptions'],
    },
  );
};
