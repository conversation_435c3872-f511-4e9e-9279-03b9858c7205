'use client';

import React, { useC<PERSON>back, useEffect, useMemo, useState } from 'react';
import {
  ColumnDef,
  ColumnFiltersState,
  PaginationState,
  SortingState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import { Button } from './button';
import { DateRangePicker } from './date-range-picker';
import { OptionType } from './inputs/field/field';
import MultiSelectBadgeField from './inputs/MultiBadgeSelector';
import { Skeleton } from './skeleton';
import { Input } from './input';
import { SearchIcon, FilterIcon, ClearIcon } from '@/assets/Icons';

function Label({
  htmlFor,
  children,
}: {
  htmlFor: string;
  children: React.ReactNode;
}) {
  return (
    <label
      htmlFor={htmlFor}
      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2 block"
    >
      {children}
    </label>
  );
}

export type filterFieldType<T> = {
  id: keyof T;
  name: string;
  type:
    | 'text'
    | 'date'
    | {
        type: 'select';
        options: OptionType[];
      };
};

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data?: TData[];
  controls?: React.ReactNode;
  isLoading?: boolean;
  filterFields?: filterFieldType<TData>[];
  hiddenColumns?: (keyof TData)[];
  preservePagination?: boolean;
  FilterSelected?: boolean;
  expandedRows?: Set<any>;
  renderExpandedRow?: (row: TData) => React.ReactNode;
}

export function DataTable<TData, TValue>({
  columns,
  data = [],
  controls,
  isLoading,
  filterFields,
  hiddenColumns,
  preservePagination = false,
  FilterSelected,
  expandedRows,
  renderExpandedRow,
}: DataTableProps<TData, TValue>) {
  const c = useTranslations('Common');

  const searchParams = useSearchParams();
  const page = searchParams?.get('page') ?? '1';
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [isFilterSelected, setIsFilterSelected] = useState(
    FilterSelected !== undefined ? FilterSelected : true,
  );
  const [showFilters, setShowFilters] = useState(false);
  const [filterResetKey, setFilterResetKey] = useState(0);

  const createQueryString = useCallback(
    (params: Record<string, string | number | null>) => {
      const newSearchParams = new URLSearchParams(searchParams?.toString());

      for (const [key, value] of Object.entries(params)) {
        if (value === null) {
          newSearchParams.delete(key);
        } else {
          newSearchParams.set(key, String(value));
        }
      }

      return newSearchParams.toString();
    },
    [searchParams],
  );

  const [{ pageIndex, pageSize }, setPagination] = useState<PaginationState>({
    pageIndex: Number(page) - 1,
    pageSize: 10,
  });

  const pagination = useMemo(
    () => ({
      pageIndex,
      pageSize,
    }),
    [pageIndex, pageSize],
  );

  useEffect(() => {
    setPagination({
      pageIndex: Number(page) - 1,
      pageSize: 10,
    });
  }, [page]);

  useEffect(() => {
    if (preservePagination) {
      const newUrl = `${window.location.pathname}?${createQueryString({
        page: pageIndex + 1,
      })}`;

      window.history.replaceState(null, '', newUrl);
    }
  }, [createQueryString, pageIndex, pageSize, preservePagination]);
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: setPagination,
    state: {
      pagination,
      sorting,
      columnFilters,
      columnVisibility: hiddenColumns
        ? Object.fromEntries(
            hiddenColumns?.map((column) => [[column], false]) as any,
          )
        : undefined,
    },
  });

  const isSelectType = (
    field: filterFieldType<TData>,
  ): field is filterFieldType<TData> & {
    type: { type: 'select'; options: OptionType[] };
  } => typeof field.type === 'object' && field.type.type === 'select';

  const searchField = filterFields?.find((field) => field.type === 'text');

  return (
    <div>
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between mb-4 ">
        {searchField ? (
          <div className="relative w-full md:w-80">
            <SearchIcon
              size={16}
              className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-400"
            />
            <Input
              type="text"
              placeholder={`Search ${searchField.name.toLowerCase()}...`}
              className="pl-8"
              value={
                (table
                  ?.getColumn(searchField.id.toString())
                  ?.getFilterValue() as string) ?? ''
              }
              onChange={(event) => {
                table
                  ?.getColumn(searchField.id.toString())
                  ?.setFilterValue(event.target.value);
                setIsFilterSelected(true);
              }}
            />
          </div>
        ) : (
          <div className="w-full md:w-80"></div>
        )}

        <div className="flex flex-wrap gap-2 w-full md:w-auto">
          {controls}
          {filterFields && filterFields.length > 1 && (
            <Button
              variant={showFilters ? 'default' : 'outline'}
              className={
                showFilters
                  ? 'bg-slate-800 hover:bg-slate-700'
                  : 'border-slate-200 text-slate-700'
              }
              onClick={() => setShowFilters(!showFilters)}
            >
              <FilterIcon size={16} className="mr-2 h-4 w-4" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
            </Button>
          )}
        </div>
      </div>

      {showFilters && filterFields && filterFields.length > 1 && (
        <div className="mb-4">
          <div className="bg-white shadow-sm rounded-lg border border-slate-200 overflow-hidden">
            <div className="px-4 py-1 border-b border-slate-200 bg-slate-50 flex justify-between items-center">
              <h3 className="font-medium text-slate-800">Advanced Filters</h3>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 text-sm"
                onClick={() => {
                  setShowFilters(false);
                  setColumnFilters([]);
                  setFilterResetKey((prev) => prev + 1);
                  setIsFilterSelected(true);
                }}
              >
                <ClearIcon size={16} className="mr-2 h-4 w-4" />
                {c('reset')}
              </Button>
            </div>

            <div className="p-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
              {filterFields
                .filter((field) => field !== searchField)
                .map((field) => {
                  if (field.type === 'text') {
                    return (
                      <div key={field.id.toString()} className="space-y-2">
                        <Label htmlFor={field.id.toString()}>
                          {field.name}
                        </Label>
                        <Input
                          id={field.id.toString()}
                          placeholder={`${c('filterBy')} ${field.name}`}
                          value={
                            (table
                              ?.getColumn(field.id.toString())
                              ?.getFilterValue() as string) ?? ''
                          }
                          onChange={(event) => {
                            table
                              ?.getColumn(field.id.toString())
                              ?.setFilterValue(event.target.value);
                            setIsFilterSelected(true);
                          }}
                          className="w-full"
                        />
                      </div>
                    );
                  } else if (field.type === 'date') {
                    return (
                      <div key={field.id.toString()} className="space-y-2">
                        <Label htmlFor={field.id.toString()}>
                          {field.name}
                        </Label>
                        <DateRangePicker
                          key={field.id.toString()}
                          onUpdate={(values) => {
                            table
                              ?.getColumn(field.id.toString())
                              ?.setFilterValue(values.range);
                            setIsFilterSelected(true);
                            if (values.rangeCompare) {
                              table
                                ?.getColumn(field.id.toString())
                                ?.setFilterValue(values.rangeCompare);
                            }
                          }}
                          initialDateFrom="2024-01-01"
                          initialDateTo="2024-12-31"
                          align="start"
                          locale="en"
                          showCompare={false}
                        />
                      </div>
                    );
                  } else if (isSelectType(field)) {
                    return (
                      <div key={field.id.toString()} className="space-y-2">
                        <Label htmlFor={field.id.toString()}>
                          {field.name}
                        </Label>
                        <MultiSelectBadgeField
                          key={`${field.id.toString()}-${filterResetKey}`}
                          options={field.type.options}
                          placeholder={`Filter by ${field.name}`}
                          onValueChange={(value) => {
                            setIsFilterSelected(true);
                            table
                              ?.getColumn(field.id.toString())
                              ?.setFilterValue(
                                value && value.length > 0 ? value : undefined,
                              );
                          }}
                        />
                      </div>
                    );
                  }
                  return null;
                })}
            </div>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between mb-2 text-sm text-slate-500">
        <div>
          Results:{' '}
          <span className="font-medium">
            {table.getFilteredRowModel().rows.length}
          </span>{' '}
          {table.getFilteredRowModel().rows.length === 1 ? 'item' : 'items'}{' '}
          found.
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">Rows per page:</span>
          <select
            value={
              table.getState().pagination.pageSize ===
              table.getFilteredRowModel().rows.length
                ? 'All'
                : table.getState().pagination.pageSize
            }
            onChange={(e) => {
              const value = e.target.value;
              if (value === 'All') {
                // Set page size to the total number of rows
                table.setPageSize(table.getFilteredRowModel().rows.length);
              } else {
                // Set page size to the selected value
                table.setPageSize(Number(value));
              }
            }}
            className="text-sm text-muted-foreground border p-1 rounded"
          >
            {[10, 20, 50, 100].map((size) => (
              <option key={size} value={size}>
                Show {size} rows
              </option>
            ))}
            <option key="all" value="All">
              Show All rows
            </option>
          </select>
        </div>
      </div>

      {isFilterSelected && (
        <div className="rounded-md border border-slate-200 overflow-hidden mb-2">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow className="bg-slate-50" key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead className="font-medium" key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={columns.length}>
                    <div className="space-y-1 w-full">
                      <Skeleton className="h-6 w-full" />
                      <Skeleton className="h-6 w-full" />
                      <Skeleton className="h-6 w-full" />
                    </div>
                  </TableCell>
                </TableRow>
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row, index) => {
                  const rowData = row.original;
                  const isExpanded =
                    expandedRows &&
                    renderExpandedRow &&
                    expandedRows.has((rowData as any).id);

                  return (
                    <React.Fragment key={row.id}>
                      <TableRow
                        data-state={row.getIsSelected() && 'selected'}
                        className={index % 2 === 1 ? 'bg-slate-50' : ''}
                      >
                        {row.getVisibleCells().map((cell) => (
                          <TableCell key={cell.id}>
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext(),
                            )}
                          </TableCell>
                        ))}
                      </TableRow>

                      {/* Expandable Row */}
                      {isExpanded && renderExpandedRow && (
                        <TableRow className="bg-brand-brown/10 hover:bg-brand-brown/10">
                          <TableCell
                            colSpan={columns.length}
                            className="p-0 bg-brand-brown/10"
                          >
                            <div className="border-l-4 border-brand-brown">
                              {renderExpandedRow(rowData)}
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </React.Fragment>
                  );
                })
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    {c('noResults')}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}

      {table.getFilteredRowModel().rows.length > 0 && (
        <div className="flex items-center justify-end space-x-2 text-sm text-slate-500">
          <div className="flex-1 text-sm text-muted-foreground">
            {table.getFilteredSelectedRowModel().rows.length} of{' '}
            {table.getFilteredRowModel().rows.length} row(s) selected.
          </div>
          <div className="space-x-2">
            <Button
              variant="outline"
              // size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              className="min-w-10"
            >
              Previous
            </Button>
            <Button
              variant="outline"
              // size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              className="min-w-10"
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {!isFilterSelected && (
        <div className="text-center mt-4 text-gray-500 text-sm">
          {c('tableNote')}
        </div>
      )}
    </div>
  );
}
