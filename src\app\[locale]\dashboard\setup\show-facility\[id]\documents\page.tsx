import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { redirect } from 'next/navigation';
import { getQueryClient } from '@/utils/query-client';
import ShowDocQuery from '@/services/queries/ShowDocQuery';
import ShowDocTable from '../components/show_doc_table';

export default async function ShowDocPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;

    const client = getQueryClient();

    if (id !== 'add') {
      if (Number.isNaN(Number(id))) throw new Error();
      await client.prefetchQuery({
        queryKey: ['ShowDoc', { locationId: Number(id) }],
        queryFn: () => ShowDocQuery.getByLocation(Number(id)),
      });
    }

    return (
      <div className="space-y-4 px-2">
        <h2 className="text-xl font-semibold text-[#00646C] border-b border-slate-200 pb-3">
          List of Documents
        </h2>
        <HydrationBoundary state={dehydrate(client)}>
          <ShowDocTable locationId={Number(id)} />
        </HydrationBoundary>
      </div>
    );
  } catch (error) {
    redirect('/dashboard/setup/show-facility/add');
  }
}
