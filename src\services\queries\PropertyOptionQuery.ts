import fetcher from './fetcher';
import {
  PropertyOptionCreateData,
  PropertyOptionUpdateData,
} from '@/schema/PropertyOptionSchema'; // This should match the schema for PropertyOption
import { PropertyOption } from '@/models/PropertyOption'; // Define the PropertyOption model

const PropertyOptionQuery = {
  tags: ['PropertyOption'] as const,
  // Add the getByPropertyId method to fetch property options by propertyId
  getByPropertyId: async (propertyId: number) =>
    fetcher<PropertyOption[]>(`PropertyOption/property/${propertyId}`),

  // Get all property options
  getAll: async () => fetcher<PropertyOption[]>('PropertyOption'),

  // Get a specific property option by ID
  get: async (id: number) =>
    fetcher<PropertyOptionUpdateData>(`PropertyOption/${id}`),

  // Add a new property option
  add: async (data: PropertyOptionCreateData) =>
    fetcher<boolean>('PropertyOption', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  // Update an existing property option by ID
  update: (id: number) => async (data: PropertyOptionUpdateData) =>
    fetcher<boolean>(`PropertyOption/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),

  // Delete a property option by ID
  delete: async (id: number) =>
    fetcher<boolean>(`PropertyOption/${id}`, {
      method: 'DELETE',
    }),
};

export default PropertyOptionQuery;
