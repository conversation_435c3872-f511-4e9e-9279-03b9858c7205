import {
  Schedule,
  CreateScheduleRequest,
  UpdateScheduleRequest,
} from '@/models/Schedule';
import fetcher from './fetcher';

const ScheduleQuery = {
  tags: ['Schedules'] as const,

  getAll: async () => fetcher<Schedule[]>('Schedules'),

  get: async (id: number) => fetcher<Schedule>(`Schedules/${id}`),

  create: async (data: CreateScheduleRequest) =>
    fetcher<boolean>('Schedules', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),

  update: (id: number) => async (data: UpdateScheduleRequest) =>
    fetcher<boolean>(`Schedules/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    }),

  toggleActive: async (id: number) =>
    fetcher<boolean>(`Schedules/${id}/toggle-active`, {
      method: 'PATCH',
    }),

  delete: async (id: number) =>
    fetcher<boolean>(`Schedules/${id}`, {
      method: 'DELETE',
    }),
};

export default ScheduleQuery;
