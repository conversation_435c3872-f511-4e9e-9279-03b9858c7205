'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Form } from '@/components/ui/form';
import Field from '@/components/ui/inputs/field';
import { Button } from '@/components/ui/button';
import Suspense from '@/components/ui/Suspense';
import { useToast } from '@/components/ui/use-toast';
import {
  OfferingPropertyCreateData,
  OfferingPropertyCreateSchema,
} from '@/schema/OfferingSchema';
import OfferingQuery from '@/services/queries/OfferingQuery';
import { DropzoneOptions } from 'react-dropzone';
import { getQueryClient } from '@/utils/query-client';
import { OfferingPropertyCreateDto } from '@/models/Offering';
import ModalContainer from '@/components/ui/overlay/components/modal_container';

interface UpdateOfferingPropertyModalProps {
  id: number;
  offeringId: number;
}

function FormContent({
  defaultValues,
  id,
  offeringId,
}: {
  defaultValues?: OfferingPropertyCreateDto;
  id: number;
  offeringId: number;
}) {
  const { toast } = useToast();

  const dropzone = {
    accept: {
      'image/*': ['.jpg', '.jpeg', '.png'],
    },
    multiple: false,
    maxFiles: 1,
  } satisfies DropzoneOptions;

  const form = useForm<OfferingPropertyCreateData>({
    resolver: zodResolver(OfferingPropertyCreateSchema),
    defaultValues: defaultValues
      ? {
          code: defaultValues.code,
          supplierItemNumber: defaultValues.supplierItemNumber,
          isForSmOnly: defaultValues.isForSmOnly,
          isInternalOnly: defaultValues.isInternalOnly,
          image: defaultValues.image,
          isActive: defaultValues.isActive,
        }
      : {
          code: '',
          supplierItemNumber: '',
          isForSmOnly: false,
          isInternalOnly: false,
          image: undefined,
          isActive: true,
        },
  });

  console.log(form.watch());

  const { mutate, isPending } = useMutation({
    mutationFn: (data: OfferingPropertyCreateData) =>
      OfferingQuery.updateOfferingProperty(id, data),
    onSuccess: async () => {
      if (id) {
        await getQueryClient().invalidateQueries({
          queryKey: ['Offering Property', { id }],
        });

        await getQueryClient().invalidateQueries({
          queryKey: ['Offering Property Details', { offeringId }],
        });
      }

      await getQueryClient().invalidateQueries({
        queryKey: OfferingQuery.tags,
      });

      toast({
        title: 'Success',
        description: id
          ? 'Product updated successfully.'
          : 'Product created successfully.',
        variant: 'success',
      });
    },
  });

  return (
    <Form {...form}>
      <ModalContainer
        title={`${defaultValues && defaultValues?.name} `}
        description={'Update property details'}
        onSubmit={form.handleSubmit((data) => mutate(data))}
        controls={
          <div className="flex justify-end items-center gap-4">
            <Button
              variant={'main'}
              disabled={isPending}
              iconName={isPending ? 'LoadingIcon' : 'SaveIcon'}
              iconProps={{ className: isPending ? 'animate-spin' : '' }}
            >
              {isPending ? 'Saving...' : 'Save'}
            </Button>
          </div>
        }
      >
        <Field
          control={form.control}
          name="code"
          label="Code"
          placeholder="Enter code"
          type="text"
          disabled
        />
        <Field
          control={form.control}
          name="supplierItemNumber"
          label="Supplier Item Number"
          placeholder="Enter item number"
          type="text"
        />
        <Field
          control={form.control}
          name="image"
          label="Product Image"
          required={id ? false : true}
          type={{
            type: 'file',
            props: {
              dropzoneOptions: dropzone,
            },
          }}
          placeholder="Upload offering image"
        />
        <Field
          control={form.control}
          name="isForSmOnly"
          label="Show Manager Only"
          type="checkbox"
        />
        <Field
          control={form.control}
          name="isInternalOnly"
          label="Internal Only"
          type="checkbox"
        />
        <Field
          control={form.control}
          name="isActive"
          label="Active"
          type="checkbox"
        />
      </ModalContainer>
    </Form>
  );
}

export default function UpdateOfferingPropertyModal({
  id,
  offeringId,
}: UpdateOfferingPropertyModalProps) {
  const { data, isLoading, isPaused } = useQuery({
    queryKey: ['Offering Property', { id }],
    queryFn: () => OfferingQuery.getOfferingPropertyById(id!),
    enabled: !!id,
  });

  return (
    <Suspense isLoading={isLoading && !isPaused}>
      <FormContent defaultValues={data} id={id} offeringId={offeringId} />
    </Suspense>
  );
}
