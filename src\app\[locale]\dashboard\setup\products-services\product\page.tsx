import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { Metadata } from 'next';
import AppLayout from '@/components/ui/app_layout';
import { getQueryClient } from '@/utils/query-client';
import ProductTableAccordion from './components/product_table_accordion';

export const metadata: Metadata = {
  title: 'Goodkey | Product',
};

export default async function ShowFacilityPage() {
  const queryClient = getQueryClient();
  return (
    <AppLayout
      items={[
        { title: 'Setup', link: '/dashboard/setup' },
        {
          title: 'Products & Services',
          link: '/dashboard/setup',
        },
        {
          title: 'Product',
          link: '/dashboard/setup/products-services/product',
        },
      ]}
    >
      <HydrationBoundary state={dehydrate(queryClient)}>
        {/* <ProductTable /> */}
        <ProductTableAccordion />
      </HydrationBoundary>
    </AppLayout>
  );
}
