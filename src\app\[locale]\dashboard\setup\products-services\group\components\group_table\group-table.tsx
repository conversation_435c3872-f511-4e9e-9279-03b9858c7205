'use client';

import { useQuery } from '@tanstack/react-query';
import { DataTable } from '@/components/ui/data-table';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import GroupQuery from '@/services/queries/GroupQuery';
import { Button } from '@/components/ui/button';
import GroupModal from '../group_modal';
import { GroupDto } from '@/models/Category';
import GroupTypeQuery from '@/services/queries/GroupTypeQuery';
import {
  CheckCircleIcon,
  XCircleIcon,
  EditIcon,
  AddIcon,
} from '@/assets/Icons';

export const GroupTable = () => {
  const { data, isLoading } = useQuery({
    queryKey: GroupQuery.tags,
    queryFn: GroupQuery.getAll,
  });

  const { data: groupType, isLoading: isLoadingGroupType } = useQuery({
    queryKey: GroupTypeQuery.tags,
    queryFn: GroupTypeQuery.getAll,
  });

  const columns = generateTableColumns<GroupDto>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      name: { name: 'Name', type: 'text', sortable: true },
      code: { name: 'Code', type: 'text', sortable: true },
      groupTypeName: { name: 'Group Type', type: 'text', sortable: true },
      isAvailable: {
        name: 'Available',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <>
                  <CheckCircleIcon size={16} className="text-success mr-1" />
                  <span className="text-success hidden">Yes</span>
                </>
              ) : (
                <>
                  <XCircleIcon size={16} className="text-destructive mr-1" />
                  <span className="text-destructive hidden">No</span>
                </>
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <Button
              size="sm"
              variant="secondary"
              iconName="EditIcon"
              onClick={() => {
                modal(<GroupModal id={row.id} />, {
                  ...DEFAULT_MODAL,
                  width: '30%',
                }).open();
              }}
            ></Button>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<GroupDto>({
    name: {
      name: 'Name',
      type: 'text',
    },
    groupTypeName: {
      name: 'Group Type',
      type: {
        type: 'select',
        options:
          (!isLoadingGroupType &&
            groupType &&
            groupType.map((o) => ({
              label: o.name,
              value: o.name,
            }))) ||
          [],
      },
    },
    isAvailable: {
      name: 'Available',
      type: {
        type: 'select',
        options: [
          { label: 'Yes', value: 'true' },
          { label: 'No', value: 'false' },
        ],
      },
    },
  });

  return (
    <DataTable
      filterFields={filters}
      columns={columns}
      data={data}
      isLoading={isLoading}
      controls={
        <Button
          variant="main"
          onClick={() => {
            modal(<GroupModal />, {
              ...DEFAULT_MODAL,
              width: '30%',
            }).open();
          }}
        >
          <AddIcon />
          Add New Group
        </Button>
      }
    />
  );
};

export default GroupTable;
