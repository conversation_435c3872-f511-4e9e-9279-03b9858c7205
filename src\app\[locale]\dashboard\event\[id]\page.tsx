import type { Metadata } from 'next';
import EventPageClient from './EventPageClient';

export const metadata: Metadata = {
  title: 'TEST SANDY | GOODKEY SHOW SERVICES LTD.',
  description:
    'View details for TEST SANDY event at Vancouver Convention Centre East',
};

export default async function EventPage(props: {
  params: Promise<{ id: string }>;
}) {
  const params = await props.params;
  return <EventPageClient params={params} />;
}
