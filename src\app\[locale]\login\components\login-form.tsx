'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { AlertCircle, Eye, EyeOff } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/inputs/input';
import { Label } from '@/components/ui/label';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import { useToast } from '@/components/ui/use-toast';
import LoginSchema, { LoginData } from '@/schema/Login';
import AuthQuery from '@/services/queries/AuthQuery';
import { useAuthStore } from '@/services/zustand/authStore';
import useConnectionStore from '@/services/zustand/ConnectionStore';
import { getQueryClient } from '@/utils/query-client';

import ForgetPasswordModal from './Forget-password-modal';

export default function LoginForm() {
  const t = useTranslations('contact');
  const c = useTranslations('Common');
  const [showPassword, setShowPassword] = useState(false);

  const { toast } = useToast();
  const form = useForm<LoginData>({
    resolver: zodResolver(LoginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });
  const { handleSubmit, control, watch, setValue } = form;
  const { setTokens, discard } = useAuthStore();
  const { push } = useRouter();
  const { isSuccess, isError, isPending, mutate } = useMutation({
    mutationKey: [AuthQuery.tags.auth],
    mutationFn: AuthQuery.Login,
    onSuccess: async (data) => {
      setTokens({
        accessToken: data.accessToken,
        refreshToken: data.refreshToken,
      });
      await getQueryClient().invalidateQueries();
      useConnectionStore.getState().setConnected(true);
      push('/dashboard');
    },
  });

  useEffect(() => {
    discard();
    useConnectionStore.persist.rehydrate();
    if (useConnectionStore.getState().isConnected) {
      if (useConnectionStore.getState().connectionStatus)
        setTimeout(
          () => toast({ title: t('sessionExpired'), variant: 'destructive' }),
          0,
        );
      useConnectionStore.getState().setConnected(false);
    }
  }, [push, toast, discard, t]);

  return (
    <div className="w-full max-w-md ">
      <div className="flex justify-center mb-8">
        <Image
          src="/gss-logo.svg"
          alt="Logo"
          width={350}
          height={129}
          priority
        />
      </div>

      <Card className="border-slate-200 shadow-sm pb-5">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center text-slate-800">
            {t('signIn')}
          </CardTitle>
          <CardDescription className="text-center text-slate-500">
            {t('enterCredentials')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isError && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{t('credentialsError')}</AlertDescription>
            </Alert>
          )}
          <Form {...form}>
            <form
              onSubmit={handleSubmit((data) => mutate(data))}
              className="space-y-4"
            >
              <div className="space-y-2">
                <Label htmlFor="username">{t('userName')}</Label>
                <FormField
                  control={control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          id="username"
                          placeholder={t('usernamePlaceholder')}
                          className="border-slate-200"
                          disabled={isPending || isSuccess}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">{t('password')}</Label>
                <FormField
                  control={control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="relative">
                          <Input
                            id="password"
                            type={showPassword ? 'text' : 'password'}
                            placeholder="••••••"
                            className="border-slate-200 pr-10"
                            disabled={isPending || isSuccess}
                            {...field}
                          />
                          <button
                            type="button"
                            className="absolute right-3 top-2.5 text-slate-400 hover:text-slate-600"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOff size={16} />
                            ) : (
                              <Eye size={16} />
                            )}
                          </button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="remember"
                    className="h-4 w-4 rounded border-slate-300 text-[#00646C] focus:ring-[#00646C]"
                  />
                  <Label htmlFor="remember" className="text-sm font-normal">
                    {t('rememberMe')}
                  </Label>
                </div>
                <Button
                  variant="link"
                  className="text-sm text-[#00646C] hover:underline p-0 h-auto"
                  type="button"
                  onClick={() =>
                    modal(
                      ({ close }) => <ForgetPasswordModal close={close} />,
                      DEFAULT_MODAL,
                    ).open()
                  }
                >
                  {t('forgetPassword')}?
                </Button>
              </div>
              <Button
                type="submit"
                className="w-full bg-[#00646C] hover:bg-[#00646C]/90"
                disabled={isPending || isSuccess}
              >
                {isPending
                  ? t('signInLoading')
                  : isSuccess
                    ? c('pleaseWait')
                    : t('signIn')}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
