export default function Loading() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="animate-pulse">
        <div className="h-12 bg-slate-200 rounded mb-6"></div>
        <div className="h-8 bg-slate-200 rounded w-1/3 mb-6"></div>

        <div className="h-40 bg-slate-200 rounded mb-6"></div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="h-[200px] bg-slate-200 rounded"></div>
          ))}
        </div>
      </div>
    </div>
  );
}
