import { GroundService, GroundServiceCreate } from '@/models/GroundService';
import fetcher from './fetcher';

const GroundServiceQuery = {
  getAll: async () => fetcher<GroundService[]>('GroundServices'),
  getById: async (id: number) => fetcher<GroundService>(`GroundServices/${id}`),
  create: async (data: GroundServiceCreate) =>
    fetcher('GroundServices', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),
  update: (id: number) => async (data: GroundServiceCreate) =>
    fetcher(`GroundServices/${id}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }),
  delete: async (id: number) =>
    fetcher(`GroundServices/${id}`, { method: 'DELETE' }),
};

export default GroundServiceQuery;
