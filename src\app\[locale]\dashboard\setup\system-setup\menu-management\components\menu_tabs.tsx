'use client';

import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface MenuTabsProps {
  sections: string[];
  activeSection: string;
  onSectionChange: (section: string) => void;
}

export function MenuTabs({
  sections,
  activeSection,
  onSectionChange,
}: MenuTabsProps) {
  return (
    <Tabs
      value={activeSection}
      onValueChange={(section) => onSectionChange(section)}
    >
      <TabsList>
        {sections.map((section) => (
          <TabsTrigger key={section} value={section}>
            {section}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
}
