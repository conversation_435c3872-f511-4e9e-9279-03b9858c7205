import createMiddleware from 'next-intl/middleware';
import { localePrefix, locales, pathnames } from './i18n.config';
import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { Tokens } from './models/Auth';
import AuthQuery from './services/queries/AuthQuery';
import { filterMenuItems } from './models/MenuItem';
import MenuQuery from './services/queries/MenuQuery';
const publicRoutes = ['/logout', '/not-found'];
const authRoutes = ['/login', '/account-verification', '/reset-password'];

export default async function middleware(req: NextRequest) {
  const path = req.nextUrl.pathname;

  if (path === '/')
    return NextResponse.redirect(new URL('/dashboard', req.nextUrl));

  const respond = createMiddleware({
    locales: locales,
    pathnames: pathnames,
    defaultLocale: 'en',
    localePrefix: localePrefix,
  })(req);
  respond.headers.set('X-next-path', path);

  if (path.startsWith('/api')) return respond;
  const isPublicRoute = publicRoutes.includes(path);
  if (isPublicRoute) return respond;
  const isAuthRoute = authRoutes.includes(path);
  const cookieStore = await cookies();
  const auth = JSON.parse(cookieStore.get('AuthStore')?.value ?? '{}')
    ?.state as Tokens | undefined;
  if (auth?.accessToken != undefined) {
    if (isAuthRoute)
      return NextResponse.redirect(new URL('/dashboard', req.nextUrl));
    else {
      try {
        const authorization = await AuthQuery.me();

        const menuItems = await MenuQuery.getAll();

        const items = filterMenuItems(menuItems, authorization);

        const cleanPath = path.replace(/^\//, '');

        const isAllowed =
          authorization.isSuper ||
          'dashboard' === cleanPath ||
          'dashboard/profile' === cleanPath ||
          items.some((item) => {
            if (item.url) {
              const itemUrlClean = item.url.replace(/^\//, '');

              const isExactMatch = itemUrlClean === cleanPath;

              const isDynamicRouteMatch = cleanPath.startsWith(
                itemUrlClean + '/',
              );

              if (itemUrlClean === 'dashboard' && isDynamicRouteMatch) {
                const dashboardChildPath = cleanPath
                  .split('/')
                  .slice(0, 2)
                  .join('/');
                const hasAccessToChild = items.some(
                  (menuItem) =>
                    menuItem.url &&
                    menuItem.url.replace(/^\//, '') === dashboardChildPath,
                );

                return hasAccessToChild;
              }

              return isExactMatch || isDynamicRouteMatch;
            }
            return false;
          });
        console.log('isAllowed', isAllowed);
        if (!isAllowed) {
          return NextResponse.redirect(new URL('/dashboard', req.nextUrl));
        }

        if (isAuthRoute) {
          return NextResponse.redirect(new URL('/dashboard', req.nextUrl));
        }

        return respond;
      } catch (e) {
        console.error(e);
        return NextResponse.redirect(new URL('/logout', req.nextUrl));
      }
    }
  } else {
    if (!isAuthRoute)
      return NextResponse.redirect(new URL('/login', req.nextUrl));
    else {
      return respond;
    }
  }
}

export const config = {
  matcher: ['/', '/(fr|en)/:path*', '/((?!_next|_vercel|.*\\..*).*)'],
};
