import { ComponentProps, ReactNode } from 'react';

import BreadCrumb from '../bread_crumb';
import { Heading } from '../heading';

interface IAppLayout {
  items: ComponentProps<typeof BreadCrumb>['items'];
  children: ReactNode;
  headerExtras?: ReactNode;
}
export default function AppLayout({
  items,
  children,
  headerExtras,
}: IAppLayout) {
  return (
    <div className="flex flex-col self-stretch w-full gap-4">
      <div className="flex justify-between gap-4">
        <div className="flex flex-col w-full rounded-md border border-slate-200 p-6 bg-white">
          <Heading title={items[items.length - 1]?.title} />
          <BreadCrumb items={items} />
        </div>
        {headerExtras && (
          <div className="rounded-md border border-slate-200 p-6 bg-white">
            {headerExtras && <div>{headerExtras}</div>}
          </div>
        )}
      </div>
      <div className="flex flex-col gap-5 w-full rounded-md border border-slate-200 p-6 overflow-y-auto scrollbar-hide bg-white">
        {children}
      </div>
    </div>
  );
}
