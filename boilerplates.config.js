[
  {
    name: 'Component',
    variants: ['componentName'],
    template: [
      [
        '{{componentName}sc}/{{componentName}kc}.tsx',
        `import React from 'react';
  import './style/index.scss';
  interface I{{componentName}pc} {
  }
  
  function {{componentName}pc}({}: I{{componentName}pc}) {
    return (
      <div className="{{componentName}kc}"></div>
    );
  }
      
  export default {{componentName}pc};
  
  `,
      ],
      [
        '{{componentName}sc}/index.ts',
        `import {{componentName}pc} from './{{componentName}kc}';
  export default {{componentName}pc};`,
      ],
      [
        '{{componentName}sc}/style/index.scss',
        `.{{componentName}kc}{
  }`,
      ],
    ],
  },
  {
    name: 'Page',
    variants: ['PageName'],
    template: [
      [
        '{{PageName}kc}/page.tsx',
        `
export default function {{PageName}pc}() {
              return (
                <div>
                  <h1>{{PageName}} Page</h1>
                </div>
              );
            };
 `,
      ],
    ],
  },
];
