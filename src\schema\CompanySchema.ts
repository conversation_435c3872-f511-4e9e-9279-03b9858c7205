import * as z from 'zod';

export const CompanyCreateSchema = z.object({
  name: z.string().min(1, { message: 'Company name is required' }),
  phone: z.string().optional(),
  email: z
    .string()
    .email({ message: 'Please enter a valid email' })
    .optional()
    .or(z.literal('')),
  address1: z.string().optional(),
  address2: z.string().optional(),
  city: z.string().optional(),
  provinceId: z
    .string()
    .optional()
    .transform((val) => (val ? Number(val) : undefined)),
  postalCode: z.string().optional(),
  countryId: z
    .string()
    .optional()
    .transform((val) => (val ? Number(val) : undefined)),
  websiteUrl: z
    .string()
    .url({ message: 'Please enter a valid URL' })
    .optional()
    .or(z.literal('')),
  accountNumber: z.string().optional(),
  companyGroup: z.string().optional(),
  note: z.string().optional(),
  isArchived: z.boolean().optional(),
});

export const CompanyUpdateSchema = z.object({
  name: z.string().min(1, { message: 'Company name is required' }),
  phone: z.string().optional(),
  email: z
    .string()
    .email({ message: 'Please enter a valid email' })
    .optional()
    .or(z.literal('')),
  address1: z.string().optional(),
  address2: z.string().optional(),
  city: z.string().optional(),
  provinceId: z
    .string()
    .optional()
    .transform((val) => (val ? Number(val) : undefined)),
  postalCode: z.string().optional(),
  countryId: z
    .string()
    .optional()
    .transform((val) => (val ? Number(val) : undefined)),
  websiteUrl: z
    .string()
    .url({ message: 'Please enter a valid URL' })
    .optional()
    .or(z.literal('')),
  accountNumber: z.string().optional(),
  companyGroup: z.string().optional(),
  note: z.string().optional(),
  isArchived: z.boolean().optional(),
});

export type CompanyCreateData = z.infer<typeof CompanyCreateSchema>;
export type CompanyUpdateData = z.infer<typeof CompanyUpdateSchema>;
export type CompanyData = CompanyCreateData;
