'use client';

import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import { ShowInList } from '@/models/Show';
import ShowQuery from '@/services/queries/ShowQuery';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Check, XCircle } from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import { useToast } from '@/components/ui/use-toast';
import { modal, DEFAULT_MODAL } from '@/components/ui/overlay';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';
import { getQueryClient } from '@/utils/query-client';
import { ArchiveIcon } from '@/assets/Icons';

export default function ShowsTable() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { data, isLoading } = useQuery({
    queryKey: ShowQuery.tags,
    queryFn: ShowQuery.getAll,
  });

  const toggleArchiveMutation = useMutation({
    mutationFn: ShowQuery.toggleArchive,
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Archive status updated successfully',
      });
      queryClient.invalidateQueries({ queryKey: ShowQuery.tags });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update archive status',
        variant: 'destructive',
      });
    },
  });

  const columns = generateTableColumns<ShowInList>(
    {
      id: { name: 'ID', type: 'text', sortable: true },
      name: { name: 'Show Name', type: 'text', sortable: true },
      code: { name: 'Show Code', type: 'text', sortable: true },
      locationName: { name: 'Location', type: 'text', sortable: true },
      displayDate: {
        name: 'Display Date',
        type: {
          type: 'node',
          render: ({ cell }) => {
            try {
              return format(new Date(cell), 'MM/dd/yyyy');
            } catch {
              return cell || '-';
            }
          },
        },
        sortable: true,
      },

      display: {
        name: 'Display',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {cell ? (
                <>
                  <Check className="text-success w-4 h-4 mr-1" />
                  <span className="text-success font-medium text-xs">
                    Active
                  </span>
                </>
              ) : (
                <>
                  <XCircle className="text-[#784311] w-4 h-4 mr-1" />
                  <span className="text-[#784311] font-medium text-xs">
                    Inactive
                  </span>
                </>
              )}
            </div>
          ),
        },
        sortable: true,
      },
      // view: {
      //   name: 'View',
      //   type: {
      //     type: 'node',
      //     render: ({ cell }) => (
      //       <div className="flex items-center whitespace-nowrap">
      //         {cell ? (
      //           <>
      //             <Check className="text-[#CDDB00] w-4 h-4 mr-1" />
      //             <span className="text-[#CDDB00] font-medium text-xs">
      //               Visible
      //             </span>
      //           </>
      //         ) : (
      //           <>
      //             <XCircle className="text-[#784311] w-4 h-4 mr-1" />
      //             <span className="text-[#784311] font-medium text-xs">
      //               Hidden
      //             </span>
      //           </>
      //         )}
      //       </div>
      //     ),
      //   },
      //   sortable: true,
      // },
      archive: {
        name: 'Status',
        type: {
          type: 'node',
          render: ({ cell }) => (
            <div className="flex items-center whitespace-nowrap">
              {!cell ? (
                <>
                  <Check className="text-success w-4 h-4 mr-1" />
                  <span className="text-success font-medium text-xs">
                    Active
                  </span>
                </>
              ) : (
                <>
                  <ArchiveIcon className="text-[#784311] w-4 h-4 mr-1" />
                  <span className="text-[#784311] font-medium text-xs line-through">
                    Archived
                  </span>
                </>
              )}
            </div>
          ),
        },
        sortable: true,
      },
    },
    {
      action: {
        name: 'Actions',
        type: {
          type: 'node',
          render: ({ row }) => (
            <div className="flex flex-row gap-2 justify-center">
              <Link href={`/dashboard/setup/list-of-shows/${row.id}`}>
                <Button size="sm" variant="secondary" iconName="EditIcon" />
              </Link>
              <Button
                variant="outline"
                size="sm"
                onClick={() => toggleArchiveMutation.mutate(row.id)}
                disabled={toggleArchiveMutation.isPending}
                title={row.archive ? 'Unarchive Show' : 'Archive Show'}
                iconName="ArchiveIcon"
                iconProps={{
                  size: 16,
                  className: row.archive ? 'text-[#CDDB00]' : 'text-[#784311]',
                }}
              />

              <Button
                variant="remove"
                size="sm"
                iconName="RemoveIcon"
                onClick={() => {
                  modal(
                    ({ close }) => (
                      <MutationConfirmModal
                        close={close}
                        title="Delete Show"
                        description={`Are you sure you want to delete "${row.name}"? This action cannot be undone.`}
                        mutateFn={() => ShowQuery.delete(row.id)}
                        mutationKey={[...ShowQuery.tags]}
                        onSuccess={async () => {
                          await getQueryClient().invalidateQueries({
                            queryKey: [...ShowQuery.tags],
                          });
                        }}
                        variant="destructive"
                        confirmButtonText="Delete"
                        confirmIconName="DeleteIcon"
                        loadingIconName="LoadingIcon"
                      />
                    ),
                    DEFAULT_MODAL,
                  ).open();
                }}
                title="Delete Show"
              />
            </div>
          ),
        },
      },
    },
    false,
  );

  const filters = generateTableFilters<ShowInList>({
    name: {
      name: 'Show Name',
      type: 'text',
    },
    code: {
      name: 'Show Code',
      type: 'text',
    },
    locationName: {
      name: 'Location',
      type: 'text',
    },
  });

  return (
    <DataTable
      columns={columns}
      data={data}
      filterFields={filters}
      isLoading={isLoading}
      controls={
        <div className="flex flex-row gap-2 justify-end">
          <Link href="/dashboard/setup/list-of-shows/add">
            <Button variant="main" iconName="AddIcon" iconProps={{ size: 16 }}>
              Add New Show
            </Button>
          </Link>
        </div>
      }
    />
  );
}
