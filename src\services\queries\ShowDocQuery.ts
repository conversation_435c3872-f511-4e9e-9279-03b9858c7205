import { ShowDocUpdate, ShowDocUpload } from '@/schema/ShowDocSchema';
import fetcher from './fetcher';
import { ShowDoc } from '@/models/ShowDoc';

const ShowDocQuery = {
  tags: ['ShowDoc'] as const,

  // GET: /ShowDoc/{locationId}
  getByLocation: async (locationId: number) => {
    return fetcher<ShowDoc[]>(`ShowDoc/${locationId}`);
  },

  // GET: /ShowDoc/Get/{id}
  getById: async (id: number) => {
    return fetcher<ShowDocUpdate>(`ShowDoc/Get/${id}`);
  },

  // POST: /ShowDoc/upload
  upload: async (data: ShowDocUpload) => {
    return fetcher<boolean>('ShowDoc/upload', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
  },

  // PUT: /ShowDoc/update
  update: async (data: ShowDocUpdate) => {
    return fetcher<boolean>('ShowDoc/update', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
  },
};

export default ShowDocQuery;
