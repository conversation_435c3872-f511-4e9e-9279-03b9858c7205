export function arraysEqual<T>(arr1: T[], arr2: T[]) {
  if (arr1.length !== arr2.length) return false;
  return arr1.every((element) => arr2.indexOf(element) != -1);
}
export function objectValuesEqual(obj: { [key: string]: any }): boolean {
  const values = Object.values(obj);
  if (values.length === 0) {
    return true; // An empty object has all "equal" values by definition
  }

  const firstValue = values[0];
  return values.every((value) => value === firstValue);
}
