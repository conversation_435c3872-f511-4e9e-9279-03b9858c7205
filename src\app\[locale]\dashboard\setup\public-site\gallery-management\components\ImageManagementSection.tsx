'use client';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { generateTableColumns, generateTableFilters } from '@/lib/tableUtils';
import GalleryQuery from '@/services/queries/GalleryQuery';
import { GalleryImage } from '@/models/Gallery';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import { toast } from '@/components/ui/use-toast';

import { List, LayoutGrid, X } from 'lucide-react';
import { DEFAULT_MODAL, modal } from '@/components/ui/overlay';
import ImageModal from './ImageModal';
import GalleryImageGrid from './GalleryImageGrid';
import { Input } from '@/components/ui/input';
import MultiSelectBadgeField from '@/components/ui/inputs/MultiBadgeSelector';
import { Label } from '@/components/ui/label';
import { FilterIcon } from '@/assets/Icons';
import MutationConfirmModal from '@/components/modals/mutation_confirm_modal';

export default function ImageManagementSection() {
  const queryClient = useQueryClient();
  const { data: categories, isLoading: loadingCategories } = useQuery({
    queryKey: ['gallery', 'categories'],
    queryFn: GalleryQuery.getCategories,
  });
  const { data: subcategories, isLoading: loadingSubcategories } = useQuery({
    queryKey: ['gallery', 'subcategories'],
    queryFn: GalleryQuery.getSubcategories,
  });
  const { data: images, isLoading } = useQuery({
    queryKey: ['gallery', 'images'],
    queryFn: GalleryQuery.getImages,
  });

  // Filter state
  const [view, setView] = useState<'list' | 'grid'>('list');
  const [showFilters, setShowFilters] = useState(false);
  const [filterResetKey, setFilterResetKey] = useState(0);
  const [filterState, setFilterState] = useState<Record<string, any>>({});

  // Download handler
  const handleDownload = async (id: number, name: string) => {
    try {
      const blob = (await GalleryQuery.downloadImage(id)) as Blob;
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = name;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (e: any) {
      toast({
        title: e.message || 'Failed to download image',
        variant: 'destructive',
      });
    }
  };

  // Modal handlers
  const handleAddImage = () => {
    modal(<ImageModal />, DEFAULT_MODAL).open();
  };

  const handleEditImage = (image: GalleryImage) => {
    modal(
      <ImageModal imgId={image.id} subcategoryId={image.subcategoryId} />,
      DEFAULT_MODAL,
    ).open();
  };

  // Add a handler to open the image modal
  const handlePreviewImage = (url: string) => {
    modal(
      <div className="flex flex-col items-center justify-center p-4 bg-white rounded-lg">
        <div className="text-lg font-semibold mb-4">Image Preview</div>
        <img
          src={url.startsWith('/images') ? url : '/images' + url}
          alt="preview"
          className="max-w-full max-h-[80vh] rounded shadow-lg"
        />
      </div>,
      { ...DEFAULT_MODAL, width: 'auto', height: 'auto' },
    ).open();
  };

  // Table columns
  const columns = useMemo<ColumnDef<GalleryImage>[]>(() => {
    return generateTableColumns<GalleryImage>(
      {
        name: { name: 'Name', type: 'text', sortable: true },
        categoryName: { name: 'Category', type: 'text' },
        subcategoryName: { name: 'Subcategory', type: 'text' },
        displayOrder: { name: 'D/O', type: 'text', sortable: true },
        url: {
          name: 'Preview',
          type: {
            type: 'node',
            render: ({ cell }) =>
              cell ? (
                <img
                  src={cell.startsWith('/images') ? cell : '/images' + cell}
                  alt="preview"
                  className="h-10 w-10 object-cover rounded cursor-pointer hover:opacity-80 transition"
                  onClick={() => handlePreviewImage(cell)}
                />
              ) : null,
          },
        },
      },
      {
        actions: {
          name: 'Actions',
          type: {
            type: 'node',
            render: ({ row }) => (
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="secondary"
                  iconName="EditIcon"
                  onClick={() => handleEditImage(row)}
                ></Button>
                <Button
                  variant="remove"
                  size="sm"
                  iconName="RemoveIcon"
                  onClick={() => {
                    modal(
                      ({ close }) => (
                        <MutationConfirmModal
                          close={close}
                          title="Delete Gallery Image"
                          description="Are you sure you want to delete this gallery image? This action cannot be undone."
                          mutateFn={async () =>
                            GalleryQuery.deleteImage(row.id)
                          }
                          mutationKey={['gallery', 'images']}
                          onSuccess={() => {
                            queryClient.invalidateQueries({
                              queryKey: ['gallery', 'images'],
                            });
                            toast({
                              title: 'Image deleted',
                              variant: 'success',
                            });
                          }}
                          onError={(e: any) =>
                            toast({
                              title: e.message || 'Failed to delete image',
                              variant: 'destructive',
                            })
                          }
                          variant="destructive"
                          confirmButtonText="Delete"
                          confirmIconName="DeleteIcon"
                          loadingIconName="LoadingIcon"
                        />
                      ),
                      DEFAULT_MODAL,
                    ).open();
                  }}
                ></Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => handleDownload(row.id, row.name)}
                  iconName="DownloadIcon"
                  iconProps={{
                    size: 16,
                  }}
                >
                  {/* <Download className="size-4" /> */}
                </Button>
              </div>
            ),
          },
        },
      },
      false,
    );
  }, []);

  // Table filters
  const filters = useMemo(
    () =>
      generateTableFilters<GalleryImage>({
        name: { name: 'Name', type: 'text' },
        categoryName: {
          name: 'Category',
          type: {
            type: 'select',
            options: (categories || []).map((cat) => ({
              label: cat.name,
              value: cat.name,
            })),
          },
        },
        subcategoryName: {
          name: 'Subcategory',
          type: {
            type: 'select',
            options: (subcategories || []).map((sub) => ({
              label: sub.name,
              value: sub.name,
            })),
          },
        },
      }),
    [categories, subcategories],
  );

  // Filtering logic for grid view
  const filteredImages = useMemo(() => {
    if (!images) return [];
    let result = images;
    filters.forEach((filter) => {
      const value = filterState[filter.id];
      if (!value || (Array.isArray(value) && value.length === 0)) return;
      if (filter.type === 'text') {
        result = result.filter((img) => {
          const cell = img[filter.id];
          return String(cell || '')
            .toLowerCase()
            .includes(String(value).toLowerCase());
        });
      } else if (
        typeof filter.type === 'object' &&
        filter.type.type === 'select'
      ) {
        result = result.filter((img) =>
          Array.isArray(value)
            ? value.includes(img[filter.id])
            : value === img[filter.id],
        );
      }
    });
    return result;
  }, [images, filters, filterState]);

  return (
    <>
      <div className="flex items-center justify-between mb-4">
        <div className="flex gap-2 items-center">
          <Button
            variant={view === 'list' ? 'main' : 'outline'}
            size="icon"
            onClick={() => setView('list')}
          >
            <List className="w-5 h-5" />
          </Button>
          <Button
            variant={view === 'grid' ? 'main' : 'outline'}
            size="icon"
            onClick={() => setView('grid')}
          >
            <LayoutGrid className="w-5 h-5" />
          </Button>
        </div>
        {view === 'grid' && (
          <div className="flex gap-2 items-center ml-auto">
            <Button
              variant={showFilters ? 'default' : 'outline'}
              className={
                showFilters
                  ? 'bg-slate-800 hover:bg-slate-700'
                  : 'border-slate-200 text-slate-700'
              }
              onClick={() => setShowFilters(!showFilters)}
            >
              <FilterIcon className="mr-2 h-4 w-4" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
            </Button>
            <Button variant="main" onClick={handleAddImage} iconName="AddIcon">
              Add Image
            </Button>
          </div>
        )}
      </div>
      {showFilters && (
        <div className="mb-4">
          <div className="flex flex-wrap gap-2 items-center mb-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-2 text-slate-500 hover:text-slate-700"
              onClick={() => {
                setFilterState({});
                setFilterResetKey((prev) => prev + 1);
              }}
            >
              <X className="h-4 w-4 mr-1" />
              Clear All
            </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 bg-white shadow-sm rounded-lg border border-slate-200 p-4 mb-4">
            {filters.map((filter) => {
              if (filter.type === 'text') {
                return (
                  <div key={filter.id} className="space-y-2">
                    <Label htmlFor={filter.id}>{filter.name}</Label>
                    <Input
                      id={filter.id}
                      placeholder={`Filter by ${filter.name}`}
                      value={filterState[filter.id] || ''}
                      onChange={(e) =>
                        setFilterState((s) => ({
                          ...s,
                          [filter.id]: e.target.value,
                        }))
                      }
                      className="w-full"
                    />
                  </div>
                );
              }
              if (
                typeof filter.type === 'object' &&
                filter.type.type === 'select'
              ) {
                return (
                  <div key={filter.id} className="space-y-2">
                    <Label htmlFor={filter.id}>{filter.name}</Label>
                    <MultiSelectBadgeField
                      key={`${filter.id}-${filterResetKey}`}
                      options={filter.type.options}
                      placeholder={`Filter by ${filter.name}`}
                      defaultValue={filterState[filter.id] || []}
                      onValueChange={(value) =>
                        setFilterState((s) => ({ ...s, [filter.id]: value }))
                      }
                    />
                  </div>
                );
              }
              return null;
            })}
          </div>
        </div>
      )}
      {view === 'list' ? (
        <DataTable
          columns={columns}
          data={images || []}
          isLoading={isLoading}
          filterFields={filters}
          controls={
            <Button variant="main" onClick={handleAddImage} iconName="AddIcon">
              Add Image
            </Button>
          }
        />
      ) : (
        <GalleryImageGrid
          images={filteredImages}
          isLoading={isLoading}
          onPreview={handlePreviewImage}
          onEdit={handleEditImage}
          onDelete={(id) => {
            modal(
              ({ close }) => (
                <MutationConfirmModal
                  close={close}
                  title="Delete Gallery Image"
                  description="Are you sure you want to delete this gallery image? This action cannot be undone."
                  mutateFn={async () => GalleryQuery.deleteImage(id)}
                  mutationKey={['gallery', 'images']}
                  onSuccess={() => {
                    queryClient.invalidateQueries({
                      queryKey: ['gallery', 'images'],
                    });
                    toast({ title: 'Image deleted', variant: 'success' });
                  }}
                  onError={(e: any) =>
                    toast({
                      title: e.message || 'Failed to delete image',
                      variant: 'destructive',
                    })
                  }
                  variant="destructive"
                  confirmButtonText="Delete"
                  confirmIconName="DeleteIcon"
                  loadingIconName="LoadingIcon"
                />
              ),
              DEFAULT_MODAL,
            ).open();
          }}
          onDownload={handleDownload}
        />
      )}
    </>
  );
}
