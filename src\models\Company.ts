export interface CompanyInList {
  id: number;
  name: string;
  phone?: string;
  email?: string;
  address1?: string;
  address2?: string;
  city?: string;
  province?: string;
  postalCode?: string;
  country?: string;
  websiteUrl?: string;
  accountNumber?: string;
  companyGroup?: string;
  note?: string;
  isArchived: boolean;
}

export interface Company {
  id: number;
  name: string;
  phone?: string;
  email?: string;
  address1?: string;
  address2?: string;
  city?: string;
  provinceId?: number;
  postalCode?: string;
  countryId?: number;
  websiteUrl?: string;
  accountNumber?: string;
  companyGroup?: string;
  note?: string;
  isArchived: boolean;
}

export interface CompanyCreateRequest {
  name: string;
  phone?: string;
  email?: string;
  address1?: string;
  address2?: string;
  city?: string;
  provinceId?: number;
  postalCode?: string;
  countryId?: number;
  websiteUrl?: string;
  accountNumber?: string;
  companyGroup?: string;
  note?: string;
  isArchived?: boolean;
}

export interface CompanyUpdateRequest {
  name: string;
  phone?: string;
  email?: string;
  address1?: string;
  address2?: string;
  city?: string;
  provinceId?: number;
  postalCode?: string;
  countryId?: number;
  websiteUrl?: string;
  accountNumber?: string;
  companyGroup?: string;
  note?: string;
  isArchived?: boolean;
}
