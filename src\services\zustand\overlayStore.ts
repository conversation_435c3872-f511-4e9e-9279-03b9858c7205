import { ReactNode } from 'react';
import { create } from 'zustand';

interface ModalOptions {
  modalProps?: any;
  modalStyle?: string | string[];
}
interface OverlayState {
  visible: boolean;
  options: ModalOptions;
  close: () => void;
  modal: (
    children: ReactNode,
    headerTitle?: string,
    options?: ModalOptions,
  ) => { open: () => void; close: () => void };
  children?: ReactNode;
  headerTitle: string;
}

const defaultModalProps: any = {};

export const useOverlayStore = create<OverlayState>((set) => ({
  headerTitle: '',
  options: {
    modalProps: defaultModalProps,
    modalStyle: '',
  },
  visible: false,
  close: () =>
    set(() => ({
      visible: false,
      options: {
        modalProps: defaultModalProps,
        modalStyle: '',
      },
    })),
  modal: (children, headerTitle, options) => {
    set((state) => ({
      children,
      headerTitle,
      options: {
        modalProps: { ...state.options.modalProps, ...options?.modalProps },
        modalStyle: options?.modalStyle,
      },
    }));
    return {
      open: () => set(() => ({ visible: true })),
      close: () => set(() => ({ visible: false })),
    };
  },
}));
