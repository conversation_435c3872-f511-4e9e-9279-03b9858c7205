'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import FloatingCartButton from '@/components/floating-cart-button';
import { useRouter } from 'next/navigation';
import { AlertTriangle } from 'lucide-react';
import WelcomeBanner from '@/components/ui/welcome-banner';

export default function ProductCatalogClient({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const eventId = params.id;

  return (
    <div className="container mx-auto py-8 px-4">
      <WelcomeBanner userName="Harry Hekimian" showInstructions={false} />
      {/* <HorizontalMenu activeItem="MANAGEMENT" /> */}

      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold text-slate-800">Products Catalog</h1>
        <Button
          variant="outline"
          onClick={() => router.push(`/event/${eventId}`)}
        >
          Back to Event
        </Button>
      </div>

      <Card className="mb-6">
        <CardHeader className="pb-2">
          <CardTitle>Available Products</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center p-6 bg-amber-50 border border-amber-200 rounded-md">
            <AlertTriangle className="h-10 w-10 text-amber-500 mr-4 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-medium text-amber-800">
                Under Development
              </h3>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* TABLES AND COVERS category */}
        <Card className="hover:shadow-md transition-shadow duration-200 cursor-pointer overflow-hidden">
          <div className="relative h-48 w-full">
            <img
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-IO0AaV5mDRRZQQdj1CQyu04tcm8Acw.png"
              alt="Table skirts placeholder"
              className="w-full h-full object-cover"
            />
          </div>
          <CardContent className="p-6">
            <div className="flex flex-col items-center text-center">
              <h3 className="text-lg font-medium text-slate-800">
                TABLES AND COVERS
              </h3>
              <p className="text-slate-600 text-sm mt-2">
                Various tables and table covers for your event
              </p>
              <Button
                className="mt-4 bg-[#00646C] hover:bg-[#00646C]/90"
                onClick={() =>
                  router.push(`/event/${eventId}/products/tables-and-covers`)
                }
              >
                View Products
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Hidden categories - we'll keep them in the code but not display them */}
        <div className="hidden">
          {Array.from({ length: 5 }).map((_, index) => (
            <Card
              key={index}
              className="bg-slate-50 border border-dashed border-slate-200"
            >
              <CardContent className="p-6 flex flex-col items-center justify-center min-h-[200px] text-center">
                <div className="w-16 h-16 bg-slate-200 rounded-full mb-4 flex items-center justify-center">
                  <span className="text-slate-400 text-2xl">?</span>
                </div>
                <h3 className="text-slate-400 font-medium">
                  Product Category {index + 1}
                </h3>
                <p className="text-slate-400 text-sm mt-2">Coming soon</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Floating Cart Button */}
      <FloatingCartButton eventId={eventId} itemCount={4} />
    </div>
  );
}
